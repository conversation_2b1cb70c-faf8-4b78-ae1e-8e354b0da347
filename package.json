{"name": "probusmanagement", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --port 8080", "build": "vite build", "preview": "vite preview --port 5050", "typecheck": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@azure/msal-browser": "^4.11.0", "@fullcalendar/core": "6.1.10", "@fullcalendar/daygrid": "6.1.10", "@fullcalendar/interaction": "6.1.10", "@fullcalendar/timegrid": "6.1.10", "@fullcalendar/vue3": "6.1.10", "@iconify/vue": "4.1.1", "@tiptap/extension-text-style": "^2.8.0", "@tiptap/pm": "2.7.4", "@tiptap/starter-kit": "2.7.4", "@tiptap/vue-3": "2.7.4", "@types/aos": "3.0.7", "@vee-validate/i18n": "^4.15.0", "@vee-validate/rules": "^4.15.0", "aos": "2.3.4", "apexcharts": "3.45.2", "axios": "1.6.7", "axios-mock-adapter": "1.22.0", "chance": "1.1.12", "date-fns": "3.6.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "maska": "^1.5.0", "moment": "^2.30.1", "msal": "^1.4.18", "pinia": "2.2.2", "remixicon": "4.1.0", "svgmap": "^2.10.1", "vee-validate": "^4.15.0", "vite-plugin-vuetify": "2.0.1", "vue": "^3.5.7", "vue-clipboard3": "2.0.0", "vue-draggable-next": "2.2.1", "vue-easy-lightbox": "^1.19.0", "vue-i18n": "9.9.1", "vue-router": "4.2.5", "vue-scrollto": "2.20.0", "vue-tabler-icons": "2.21.0", "vue3-apexcharts": "1.5.2", "vue3-carousel": "0.3.1", "vue3-perfect-scrollbar": "1.6.1", "vue3-print-nb": "0.1.4", "vuedraggable": "2.24.3", "vuetify": "3.7.1", "yup": "1.4.0"}, "devDependencies": {"@mdi/font": "7.4.47", "@rushstack/eslint-patch": "1.7.2", "@types/chance": "^1.1.6", "@types/lodash": "^4.14.202", "@types/node": "20.11.17", "@vitejs/plugin-vue": "5.0.4", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.1.3", "esbuild": "^0.20.0", "eslint": "^8.5.0", "eslint-plugin-vue": "^9.21.1", "prettier": "3.2.5", "sass": "1.70.0", "sass-loader": "14.1.0", "typescript": "^5.3.3", "vite": "5.4.8", "vue-cli-plugin-vuetify": "2.5.8", "vue-tsc": "^2.1.6"}}