export type ConfigProps = {
    Sidebar_drawer: any;
    Customizer_drawer: boolean;
    mini_sidebar: boolean;
    setHorizontalLayout: boolean;
    setRTLLayout: boolean;
    actTheme: string;
    boxed: boolean;
    setBorderCard: boolean;
};

const config: ConfigProps = {
    Sidebar_drawer: true,
    Customizer_drawer: false,
    mini_sidebar: false, //rail activo por defecto(modo iconos)
    setHorizontalLayout: false,
    setRTLLayout: false,
    actTheme: 'BLUE_THEME',
    boxed: true,
    setBorderCard: false
};


export default config;
