// lazyFetch.ts
type LazyFunction<T> = () => Promise<T>;

// debounce simple
let debounceTimer: ReturnType<typeof setTimeout>;

const lazyFetch = async <T>(fetchFn: LazyFunction<T>): Promise<T> => {
  clearTimeout(debounceTimer);
  await new Promise((resolve) => (debounceTimer = setTimeout(resolve, 100)));

  try {
    const result = await fetchFn();
    return result;
  } catch (error) {
    console.error("Error en lazyFetch:", error);
    throw error;
  }
};

export default lazyFetch;
export { lazyFetch };
