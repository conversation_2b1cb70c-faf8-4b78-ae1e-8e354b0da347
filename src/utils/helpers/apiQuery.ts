const baseUrl = import.meta.env.VITE_API_URL;

// ✅ Add token if exists
function authHeader(): Record<string, string> {
  const token = localStorage.getItem("token");
  return token ? { Authorization: `Bearer ${token}` } : {};
}

// ✅ HTTP methods
async function get(url: string): Promise<any> {
  return sendRequest("GET", url);
}

async function post(url: string, body: any): Promise<any> {
  return sendRequest("POST", url, body);
}

async function put(url: string, body: any): Promise<any> {
  return sendRequest("PUT", url, body);
}

async function _delete(url: string): Promise<any> {
  return sendRequest("DELETE", url);
}

// ✅ Centralized request logic
async function sendRequest(
  method: string,
  url: string,
  body?: any
): Promise<any> {
  const requestOptions: RequestInit = {
    method,
    headers: {
      ...(body ? { "Content-Type": "application/json" } : {}),
      ...authHeader(),
    },
    ...(body ? { body: JSON.stringify(body) } : {}),
  };

  const fullUrl = `${baseUrl}/${url}`;
  const response = await fetch(fullUrl, requestOptions);
  return handleResponse(response, fullUrl);
}

// ✅ Unified response handler
async function handleResponse(response: Response, url: string): Promise<any> {
  const text = await response.text();
  let data: any;

  try {
    data = text ? JSON.parse(text) : null;
  } catch {
    data = text;
  }

  if (!response.ok) {
    const error = data?.message || response.statusText;

    // ✅ Store the last error
    localStorage.setItem("fetch_last_error", error);

    // ✅ Redirect to login if unauthorized (401 or 403)
    if (response.status === 401 || response.status === 403) {
      localStorage.removeItem("token"); // Optional: clear token
      window.location.href = "/login"; // Adjust path as needed
    }

    throw new Error(error);
  }

  // ✅ Special logic for /opciones-aplicacion
  if (url.includes("/opciones-aplicacion")) {
    localStorage.setItem("opciones", JSON.stringify(data));
  }

  return data;
}

export const apiQuery = {
  get,
  post,
  put,
  delete: _delete,
};
