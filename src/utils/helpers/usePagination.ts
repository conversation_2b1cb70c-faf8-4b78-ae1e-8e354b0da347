import { computed, ref, watch } from "vue";

function usePagination(list: any[], itemsPerPageDefault = 10) {
  const dataList = ref(list);

  const currentPage = ref(1);
  const itemsPerPage = ref(itemsPerPageDefault);

  const totalPages = computed(() => {
    return Math.ceil(dataList.value.length / itemsPerPage.value) || 1;
  });

  const paginatedData = computed(() => {
    const start = (currentPage.value - 1) * itemsPerPage.value;
    const end = start + itemsPerPage.value;
    return dataList.value.slice(start, end);
  });

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page;
    }
  };

  const nextPage = () => {
    if (currentPage.value < totalPages.value) {
      currentPage.value += 1;
    }
  };

  const prevPage = () => {
    if (currentPage.value > 1) {
      currentPage.value -= 1;
    }
  };

  const setItemsPerPage = (newItemsPerPage: number) => {
    itemsPerPage.value = newItemsPerPage;
    currentPage.value = 1; // Reset to first page
  };

  watch(
    () => dataList.value.length,
    (newLength) => {
      const newTotalPages = Math.ceil(newLength / itemsPerPage.value) || 1;
      if (currentPage.value > newTotalPages) {
        currentPage.value = newTotalPages;
      }
    }
  );

  const updateList = (newList: any[]) => {
    dataList.value = newList;
  };

  return {
    currentPage,
    itemsPerPage,
    totalPages,
    paginatedData,
    goToPage,
    nextPage,
    prevPage,
    setItemsPerPage,
    updateList,
  };
}

export default usePagination;
