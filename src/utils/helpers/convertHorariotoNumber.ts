function convertHorarioToNumber(
  hour: string | null | undefined
): number | undefined {
  if (!hour) return;

  const isPM = hour.includes("PM");
  const timePart = hour.replace("AM", "").replace("PM", "").trim();
  const [rawHour, rawMin] = timePart.split(":");

  let hourNum = parseInt(rawHour, 10);
  const minNum = parseInt(rawMin, 10);

  if (isPM && hourNum < 12) hourNum += 12;
  if (!isPM && hourNum === 12) hourNum = 0; // 12 AM edge case

  const hourStr = padStart(hourNum.toString(), 2);
  const minStr = padStart(minNum.toString(), 2);

  return parseInt(`${hourStr}${minStr}`);
}

function padStart(str: string, targetLength: number, padChar = "0") {
  while (str.length < targetLength) {
    str = padChar + str;
  }
  return str;
}

export default convertHorarioToNumber;
