function generaHoras(
  arrHor: any[],
  startTime: string = "07:00AM",
  endTime: string = "20:00", // Can now be 24-hour format
  timeRange: number = 15,
  use24HourFormat: boolean = false
) {
  const parseTime = (timeStr: string): number => {
    const twelveHourMatch = timeStr.match(/^(\d{1,2}):(\d{2})(AM|PM)$/i);
    const twentyFourHourMatch = timeStr.match(/^(\d{1,2}):(\d{2})$/);

    if (twelveHourMatch) {
      let hour = parseInt(twelveHourMatch[1]);
      const minute = parseInt(twelveHourMatch[2]);
      const period = twelveHourMatch[3].toUpperCase();

      if (period === "PM" && hour !== 12) hour += 12;
      if (period === "AM" && hour === 12) hour = 0;

      return hour * 60 + minute;
    }

    if (twentyFourHourMatch) {
      const hour = parseInt(twentyFourHourMatch[1]);
      const minute = parseInt(twentyFourHourMatch[2]);

      return hour * 60 + minute;
    }

    throw new Error(`Invalid time format: ${timeStr}`);
  };

  const pad = (value: number) => (value < 10 ? `0${value}` : `${value}`);

  const formatTime = (totalMinutes: number): string => {
    const hours24 = Math.floor(totalMinutes / 60) % 24;
    const minutes = totalMinutes % 60;
    const paddedMinutes = pad(minutes);

    if (use24HourFormat) {
      const paddedHours = pad(hours24);
      return `${paddedHours}:${paddedMinutes}`;
    } else {
      const hours12 = hours24 % 12 === 0 ? 12 : hours24 % 12;
      const suffix = hours24 < 12 ? "AM" : "PM";
      const paddedHours = pad(hours12);
      return `${paddedHours}:${paddedMinutes}${suffix}`;
    }
  };

  const startMinutes = parseTime(startTime);
  const endMinutes = parseTime(endTime);

  let current = startMinutes;
  do {
    arrHor.push({ hora: formatTime(current) });
    current = (current + timeRange) % (24 * 60);
  } while (current !== (endMinutes + timeRange) % (24 * 60));
}

export default generaHoras;
