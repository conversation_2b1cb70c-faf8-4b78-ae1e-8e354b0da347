import type Persona from "@/utils/models/Generales/persona";
import type { PersonaDireccion } from "@/utils/models/Generales/PersonaDireccion";

export interface Usuario {
  id: number; // Maps to UsuarioId
  usuario: string; // Maps to UsuaNombre
  companyId: number;
  personaId: number;
  usuaEmail: string; // Maps to UsuaEmail
  usuaClave_Hash: string;
  usuaClave_Salt: string;
  usuaClaveVence_Fecha: string | null;
  usuaCambiaClave: boolean;
  rolId: number;
  dispositivoMovilId: string | null;
  usuaFechaCreacion: string;
  estatus: boolean; // Maps to Estatus
  updateByUsuarioId: number | null;
  fechaActualizado: string | null;
  
  // Fields directly from the API response for the user object
  nombre: string;
  apellido: string;
  correo: string;
  telefono: string | null;
  tipoDocumento: number;
  documento: number;
  foto: string | null;
  rolDescripcion: string;
  estado: boolean;

  // Nested objects
  persona: Persona;
}