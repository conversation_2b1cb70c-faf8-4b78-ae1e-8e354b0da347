export interface estudianteCarrera {
  id: number;
  estudiante: number;
  carrera: number;
  codigoCarrera?: string;
  carreraDescripcion?: string;
  sede: number;
  sedeDescripcion?: string;
  cohorte: number;
  cohorteDescripcion?: string;
  planEstudio: number;
  planEstudioDescripcion?: string;
  planComplementario?: number;
  planPago: number;
  planDePagoDescripcion?: string;
  becado: boolean;
  descuento?: number;
  fechaInicio: string;
  fechaSeGraduara: string;
  fechaGraduado?: string;
  enBloque: boolean;
  solicitudAdmision?: number;
  admitidoComo: number;
  admitidoComoDescripcion?: string;
  colegio?: number;
  universidad?: number;
  estudianteGradoObtenido?: number;
  bloqueHorarioTanda?: number;
  estadoEstudiante: number;
  estadoEstudainteDescripcion?: string;
  esCaAuditado?: boolean;
  activo: boolean;
}
