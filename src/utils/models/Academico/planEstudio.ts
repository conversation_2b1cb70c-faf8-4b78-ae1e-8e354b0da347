import type { planEstudioCorrequisito } from "./planEstudioCorrequisito";
import type { planEstudioDetalle } from "./planEstudioDetalle";
import type { planEstudioPrerrequisito } from "./planEstudioPrerrequisito";
import type { planEstudioReforzamiento } from "./planEstudioReforzamiento";

export interface PlanEstudio {
  id: number;
  descripcion: string;
  version: number | null;
  fechaInicio: Date | null;
  fechaFin: Date | null;
  carrera: number | null;
  carreraDescripcion: string;
  carreraAlias: string;
  planComplementario: number | null;
  creditoMaximo: number;
  decretoNo: string;
  resolucionConsejo: string;
  resolucionNo: string;
  tituloMasc: string;
  tituloFem: string;
  vigente: boolean;
  estatus: boolean;
  planEstudioDet: planEstudioDetalle[];
  planEstudioCorrequisitos: planEstudioCorrequisito[];
  planEstudioPrerrequisitos: planEstudioPrerrequisito[];
  planEstudioReforzamientos: planEstudioReforzamiento[];
}
