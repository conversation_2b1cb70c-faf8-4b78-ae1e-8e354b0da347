export interface IProfesor {
  id: number;
  company: number;
  codigoReferencia: string;
  sede: number | null;
  tipoDocumento: number | null;
  tipoTelefonos: number | null;
  tipoEmail:number | null;
  emailEstado:boolean;
  email:string;
  personaId: number;
  esElegible: boolean;
  categoriaPago: number;
  fechaIngreso: Date;
  fechaInhabilidad: Date;
  inhabilidadObservacion: string;
  carrera: null;
  documento: string;
  direccion:string,
  provinciaDireccion: number | null,
  municipioDireccion: number | null,
  telefono:string;
  carreraCodigo: string;
  carreraDescripcion: string;
  esDirector: boolean;
  emailPrincipal:boolean,
  esCoordinador: boolean;
  esAdministrativo: boolean;
  casoEmergencia: string;
  casoEmergenciaTelefono: string;
  esExterno: boolean;
  educacionContinua: boolean;
  estado: number;
  estadoDescripcion: string;
  nombreCompleto: string;
  nombres: string;
  apellidos: string;
  sexoId: number | null;
  estadoCivil: number | null;
  fechaNacimiento: Date| null;
  pais: number;
  ciudad: null;
  nacionalidad: null;
  tipoSangre: null;
  alergico: string;
  foto: string | null;
  firmaUrl: string | null;
  codigoPostal:string,
  paisDireccion:number | null,
  personas:{
    nombres:string,
    apellidos:string,
    sexo:number,
    estadoCivil:number,
    fechaNacimiento: Date|null,
    pais:number | null,
    ciudad: number | null,
    nacionalidad:number | null,
    tipoSangre: number|null,
    alergias:string |null,
  };
  personaTelefonos: {
    id: number;
    persona: number;
    tipoTelefono: number;
    telefono: string;
    telefonoDescripcion: string;
  }[];
  personaDocumentosIdentificacion: {
    id: number;
    persona: number;
    tipoDocumentoIdentificacion: number;
    valor: string;
    documentoDescripcion: string;
    modificado: number;
  }[];
  personaDireccion: {
    id: number;
    persona: number;
    pais: number;
    provincia: number;
    municipio: number;
    ciudad: number;
    direccion: string;
    codigoPostal: string ;
  }[];
  personaEmails: {
    id: number;
    tipoEmail: number;
    email: string;
    principal: boolean;
    tipoEmailDescripcion:string,
    estado: boolean;
    modificado: number;

  }[];
}


