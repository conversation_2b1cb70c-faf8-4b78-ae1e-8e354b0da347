import type Persona from '../Generales/persona';
import type { estudianteCarrera } from './estudianteCarrera';

export interface Estudiante {
  id: number;
  companyId: number;
  matricula: string;
  idPersona: number;
  creditosCursados?: number;
  creditosConvalidados?: number;
  contactoEmergenciaNombre?: string;
  contactoEmergenciaTelefono?: string;
  fechaCreacion: string;
  persona: Persona;
  estudianteCarreras: estudianteCarrera[];
}
