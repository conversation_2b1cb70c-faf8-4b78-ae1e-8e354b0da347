export interface Carrera {
  id: number;
  alias: string;
  descripcion: string;
  nombreCorto: string;
  escuela: number;
  fechaInicio: Date;
  fechaFin: Date | null;
  telefono: string;
  email: string;
  director: string;
  tipoCarreraId: number;
  analista: string;
  emailAnalista: string;
  telefonoAnalista: string;
  cuentaContable: number;
  cuentaContableGP: string;
  cuentaContableGPDimension: number;
  estado: boolean;
}
