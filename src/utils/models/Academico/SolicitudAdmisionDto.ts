// TypeScript interface for SolicitudAdmisionDto
// This interface defines the structure of a student admission request
export interface SolicitudAdmisionDto {
    id?: number;
    empresaId: number;
    campusId: number;
    campusNombre?: string;
    codigoReferencia: string;
    codigoMatricula?: string;
    nombreCompleto: string;
    apellidoCompleto: string;
    nacimiento: Date;
    paisOrigenId: number;
    paisOrigenNombre?: string;
    ciudadOrigenId?: number;
    ciudadOrigenNombre?: string;
    ciudadTexto?: string;
    tipoSangreId?: number;
    tipoSangreNombre?: string;
    infoAlergias?: string;
    enfermedadPadece?: string;
    nacionalidadId: number;
    nacionalidadNombre?: string;
    sexoId: number;
    sexoNombre?: string;
    estadoCivilId: number;
    estadoCivilNombre?: string;
    estadoCiudadanoNombre?: string;
    estadoCiudadanoId: number;
    documentoIdentidad?: string;
    tipoDocumentoIdentidad?: number;
    tipoDocumentoIdentidadNombre?: string;
    telefonoCelular: string;
    correoElectronico: string;
    empresaTrabajo?: string;
    cargoTrabajo?: string;
    telefonoTrabajo?: string;
    direccionTrabajo?: string;
    tipoIngresoId: number;
    tipoIngresoNombre?: string;
    cohorteId: number;
    cohorteNombre?: string;
    carreraId: number;
    carreraNombre?: string;
    modalidadCarreraId?: number;
    modalidadCarreraNombre?: string;
    admitidoComoId: number;
    admitidoComoNombre?: string;
    horarioId: number;
    formaPagoId: number;
    formaPagoNombre?: string;
    canalEnteradoId: number;
    canalEnteradoNombre?: string;
    objetivoIngreso?: string;
    fechaProceso?: string;
    fechaAceptacion?: string;
    fechaRegistro?: string;
    monedaPagoId?: number;
    monedaPagoNombre?: string;
    tieneBeca?: boolean;
    porcentajeDescuento?: number;
    estadoSolicitudId: number | null
    estadoSolicitudNombre?: string;
    tratamiento?: string;
    condicionMedica?: string;
    discapacidad?: string;
    comentariosAdicionales?: string;
    fechaGraduacion?: string;
    redSocialId?: number;
    redSocialNombre?: string;
    
  
    documentos: DocumentoDto[];
    idiomas: IdiomaDto[];
    educacion: EducacionDto[];
    infoCrm: CrmDto[];
    direcciones: DireccionDto[];
    preguntas: PreguntaDto[];
  }
  
  // Dependencias (puedes separar cada una en su propio archivo si deseas)
  
  export  interface DocumentoDto {
    id?: number;
    solicitudId?: number;
    documentoId?: number;
    comentario?: string;
    enlaceArchivo?: string;
    estadoDocumentoId?: number;
  }
  
  export interface IdiomaDto {
    id?: number;
    idIdioma?: number;
    puedeLeer?: boolean;
    puedeEscribir?: boolean;
    puedeHablar?: boolean;
    fechaRegistro?: string;
    nivelLectura?: number;
    nivelEscritura?: number;
    nivelConversacion?: number;
  }
  
  export   interface EducacionDto {
    id?: number;
    solicitudId: number;
    tipo: string;
    centroEstudio?: string;
    nivelEstudio?: string;
    titulo?: string;
    anio?: number;
    promedio?: number;
    cursoNombre?: string;
    cursoFecha?: string;
  }
  
  export interface CrmDto {
    id?: number;
    solicitudId?: number;
    codigoCohortePrograma?: string;
    nombrePrograma?: string;
    cohorteNombre?: string;
    idContactoCrm?: string;
    idOportunidadCrm?: string;
    idEstudianteSisbba?: string;
  }
  
  export interface DireccionDto {
    id?: number;
    paisId: number;
    paisNombre?: string;
    provinciaId: number;
    provinciaNombre?: string;
    municipioId?: number;
    municipioTexto?: string;
    direccion?: string;
    telefono?: string;
    fechaCreacion?: string;
  }
  

  export interface PreguntaDto {
    id?: number;
    idSolicitud?: number;
    idPreguntaDetalle: number;
    respuestaid: string;
    fechaRegistro?: string;
  }