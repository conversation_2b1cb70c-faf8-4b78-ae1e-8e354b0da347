// Archivo: types/SoPrPregunta.ts (puedes poner el nombre que gustes)

export interface PreguntaEncabezadoDto {
  soPrDescripcion: string;      // Descripción (máx 500 caracteres)
  orden: number;                // Número positivo (orden)
  soPrFechaCreacion: Date;      // Fecha de creación
  estatus: string;              // Estatus (máx 50 caracteres)
}
export interface preguntasDetalleDto {
  id?: number;                  // ID de la pregunta
  solicitudId?: number;         // ID de la solicitud de admisión
  preguntaId?: number;          // ID de la pregunta
  respuesta?: string;           // Respuesta (máx 500 caracteres)
  soPrFechaCreacion?: Date;     // Fecha de creación
  soPrEstatus?: string;         // Estatus (máx 50 caracteres)
}