import type { carreraAnalista } from "./carreraAnalista";
import type { carreraSede } from "./carreraSede";

export interface carreras {
  id: number;
  alias: string;
  descripcion: string;
  nombreCorto: string;
  escuelaId: number | null;
  escuelaDescripcion: string;
  fechaInicio: Date | null;
  fechaFin: Date | null;
  telefono: string;
  email: string;
  director: string;
  tipoCarreraId: number | null;
  tipoCarreraDes: string;
  emailAnalista: string;
  telefonoAnalista: string;
  cuentaContableCatalogoId: number | null;
  cuCodigo: string;
  cuDescripcion: string;
  cuentaContableGP: string;
  cuentaContableGPDimension: string;
  estado: boolean;
  carreraAnalistas: carreraAnalista[];
  carreraSedes: carreraSede[];
}
