export interface asignatura {
  id: number;
  codigo: string;
  descripcion: string;
  creditos: number;
  horasTeoricas: number;
  horasPracticas: number;
  horasInvestigacion: number;
  horasNoPresenciales: number;
  escuela: number | null;
  escuelaDescripcion: string;
  tipoAsignatura: number;
  tipoAsignaturaDescripcion: string;
  tipoAsignaturaElectiva: number | null;
  tipoAsignaturaElectivaDescripcion: string;
  calculaIndice: boolean;
  exonera: boolean;
  pagaHora: boolean;
  estatus: boolean;
}
