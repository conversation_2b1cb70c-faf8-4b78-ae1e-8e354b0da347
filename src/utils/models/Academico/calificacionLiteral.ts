export interface calificacionLiteral {
  id: number;
  descripcion: string;
  literal: string;
  tipoGradoTitulo: number | null;
  tipoGradoTituloDescripcion: string;
  supera: boolean;
  califica: boolean;
  retirada: boolean;
  seleccion: boolean;
  convalidada: boolean;
  colacionada: boolean;
  exonerada: boolean;
  puntos: number;
  faltaNota: boolean;
  rangoInicio: number;
  rangoFin: number;
  digita: boolean;
  estatus: boolean;
}
