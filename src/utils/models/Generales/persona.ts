/* import { PostPersonaEmailDto } from './PostPersonaEmailDto';
import { PostTelefonoDto } from './PostTelefonoDto';
import { PostPersonaDireccionDto } from './PostPersonaDireccionDto';
import { PostPersonaDocumentoIdentificacionDto } from './PostPersonaDocumentoIdentificacionDto';
import { PersonaOtrosDatosDto } from './PersonaOtrosDatosDto'; */

import type { PersonaDireccion } from "./PersonaDireccion";

export default interface Persona {
  personaID: number;
  companyID: number;
  persNombres: string;
  persApellidos: string;
  sexoID: number;
  estadoCivilID: number;
  persFechaNacimiento: string;
  paisID: number;
  ciudadID: number;
  nacionalidadID: number;
  tipoSangreID: number | null;
  persAlergico: string | null;
  persFoto: string | null;
  persFechaCreacion: string;
  updateByUsuarioId: number | null;
  fechaActualizado: string | null;
  direcciones?: PersonaDireccion[]; // Added based on Persona_Direccion table
}
