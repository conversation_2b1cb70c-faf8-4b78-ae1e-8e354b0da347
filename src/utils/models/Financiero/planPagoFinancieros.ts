import type { IplanPagoDet } from "./planPagoDetFinancieros"


export interface planesPagos{
    id: number, 
    descripcion: string,
    recintoId: number | null,
    recintoDescripcion: string,
    carrId: number | null,
    carrAlias: string,
    carrDescripcion: string,
    monPagoId: number | null
    monPagoDescripcion: string,
    monPagoCodigo: string,
    montoTotal: number | null,
    porcentajeDescuento :number | null,
    recargoInscripcion: number | null,
    cantidadPago: number | null,
    montoInscripcion: number | null,
    montoReinscripcion: number | null,
    isPorCredito: boolean,
    isDefault: boolean,
    costoCredito: number | null, //opcional
    creditoMinimo: number | null, // opcional
    isRecargoPorciento: boolean,
    montoRecargoCuota: number | null,
    montoInscripcionMonografico: number | null, //opcional
    montoInscripcionTutorias: number | null, //opcional
    costoAdmision: number | null, //opcional
    isCalculaRecargoInscripcionPorciento: boolean 
    estado: boolean,
    planPagoDet: IplanPagoDet[]
}