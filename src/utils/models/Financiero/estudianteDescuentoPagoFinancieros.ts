

export interface estudianteDescuentoPagos {
    id: number,
    admisionId: number | null,
    estuCarreraId: number | null,
    monedaId: number | null,
    monedaDescripcion: string,
    descuentoId : number | null,
    descuentoDescripcion: string,
    descuentoJustificacionId : number | null,
    descuentoJustificacionDes: string,
    porcentaje: number | null,
    porcentajeMonto: number | null,
    isPermanente: boolean,
    isAfectaInscripcion: boolean,
    isAfectaMensualidad: boolean,
    empresasId: number | null,
    empresasDescripcion: string,
    estado: boolean,
    isAfectaServicios : boolean,
    isAfectaServiciosInscripcion: boolean,
    isVence : boolean,
    fechaVencimiento : Date | null,
    isMonto : boolean,
    isPorcentaje: boolean,
    carreraDescripcion: string,
    estadoEstudianteDes : string,
    valorDescuento: string
}