
export interface cuentaContableCatologo{
    id: number,
    descripcion: string,
    codigo: string,
    tipoCuentaId: number | null,
    tipoCuentaDescripcion: string,
    cuentaOrigenId: number | null,
    cuentaOrigenDescripcion: string,
    aceptaMovimientos: boolean,
    cuentaCobrarPesos: boolean,
    cuentaCobrarDolares: boolean,
    cuentaPrimaDolares: boolean,
    cuentaCobrarPrimaDolares: boolean,
    cuentaGp: boolean,
    estado: boolean
};