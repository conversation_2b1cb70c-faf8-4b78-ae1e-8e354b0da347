<script setup lang="ts">
import { computed } from 'vue'; // 'ref', 'watch', 'onBeforeMount' ya no son necesarios
import { useCustomizerStore } from '../../../stores/customizer';
import Logo from '../logo/Logo.vue';

// Dropdown/Component Imports
// Asegúrate de que las rutas sean correctas para tu estructura de proyecto
import LanguageDD from '../vertical-header/LanguageDD.vue';
import NotificationDD from '../vertical-header/NotificationDD.vue';
import ProfileDD from '../vertical-header/ProfileDD.vue';
import Navigations from '../vertical-header/Navigations.vue'; // Asumo que esto es el "Mega menu"
import Searchbar from '../vertical-header/Searchbar.vue';
import RightMobileSidebar from '../vertical-header/RightMobileSidebar.vue'; // Para elementos específicos del móvil
import ThemeToggler from '../vertical-header/ThemeToggler.vue';

import { useEcomStore } from '@/stores/apps/eCommerce'; // Asegúrate de que esta ruta es correcta
import { Icon } from '@iconify/vue'; // Importar Iconify

const customizer = useCustomizerStore();
const ecomStore = useEcomStore(); // Renombrado a 'ecomStore' para mayor claridad

// Computed property para el número de ítems en el carrito
const cartItemCount = computed(() => ecomStore.cart?.length || 0);

// La prop `priority` en v-app-bar no se usa comúnmente para la visibilidad o z-index.
// Si necesitas controlar el z-index, puedes usar CSS o verificar otras props de Vuetify.
// Se mantiene en 0 como en el original para mantener la compatibilidad, pero es algo a revisar.
const appBarPriority = computed(() => (customizer.setHorizontalLayout ? 0 : 0));
</script>

<template>
  <v-app-bar
    elevation="0"
    :priority="appBarPriority"
    height="70"
    id="top"
    class="horizontal-header border-b"
  >
    <div
      :class="
        customizer.boxed ? 'maxWidth v-toolbar__content' : 'v-toolbar__content px-6'
      "
    >
      <div class="hidden-md-and-down">
        <Logo />
      </div>

      <v-btn
        class="hidden-lg-and-up ms-3"
        icon
        rounded="sm"
        variant="text"
        size="small"
        @click.stop="customizer.SET_SIDEBAR_DRAWER"
      >
        <Icon icon="solar:hamburger-menu-line-duotone" height="22" />
      </v-btn>

      <Searchbar />

      <div class="hidden-sm-and-down">
        <Navigations />
      </div>

      <v-spacer class="hidden-sm-and-down" />

      <div class="hidden-md-and-up">
        <Logo />
      </div>

      <ThemeToggler />

      <div class="hidden-sm-and-down">
        <LanguageDD />
      </div>

      <v-btn
        icon
        class="custom-hover-primary hidden-sm-and-down"
        size="small"
        variant="text"
        color="primary"
        to="/ecommerce/checkout"
      >
        <v-badge color="error" :content="cartItemCount">
          <Icon icon="solar:cart-large-2-outline" height="22" />
        </v-badge>
      </v-btn>

      <div class="hidden-sm-and-down">
        <NotificationDD />
      </div>

      <div class="hidden-sm-and-down">
        <ProfileDD />
      </div>

      <v-menu :close-on-content-click="true" class="mobile_popup">
        <template v-slot:activator="{ props }">
          <v-btn
            icon
            class="hidden-md-and-up custom-hover-primary"
            color="primary"
            variant="text"
            v-bind="props"
            size="small"
          >
            <Icon icon="solar:menu-dots-bold-duotone" height="22" />
          </v-btn>
        </template>
        <v-sheet rounded="lg" elevation="10" class="mt-4 dropdown-box px-4 py-3">
          <div class="d-flex flex-column align-start">
            <RightMobileSidebar />

            <LanguageDD class="mt-2" />
            <NotificationDD class="mt-2" />
            <v-btn
              variant="text"
              class="custom-hover-primary mt-2"
              to="/ecommerce/checkout"
              size="small"
              block
              justify="start"
            >
              <v-badge color="primary" :content="cartItemCount" class="mr-2">
                <Icon icon="solar:cart-large-2-outline" height="22" />
              </v-badge>
              <span>Carrito</span>
            </v-btn>
            <ProfileDD class="mt-2" />
          </div>
        </v-sheet>
      </v-menu>
    </div>
  </v-app-bar>
</template>

<style lang="scss" scoped>
/* Puedes añadir estilos específicos para este componente aquí */
.horizontal-header {
  /* Estilos específicos para la barra de navegación horizontal */
}

.custom-hover-primary:hover {
  /* Estilos para el efecto hover, si no están definidos globalmente */
}

.dropdown-box {
  min-width: 200px; /* Ajusta el ancho mínimo del menú desplegable móvil */
}
</style>