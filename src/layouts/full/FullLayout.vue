<script setup lang="ts">
import { RouterView } from 'vue-router';
import VerticalSidebarVue from './vertical-sidebar/VerticalSidebar.vue';
import VerticalHeaderVue from './vertical-header/VerticalHeader.vue';
import Customizer from './customizer/Customizer.vue';
import { useCustomizerStore } from '../../stores/customizer';
import { useAuthStore } from '../../stores/auth';
import { jwtDecode } from 'jwt-decode';
import { onMounted, computed } from 'vue'; // Added 'computed' for dynamic drawer location

const customizer = useCustomizerStore();
const authStore = useAuthStore();

interface DecodedToken {
  usuaNombre: string;
  rol: string;
  email: string;
  [key: string]: any;
}

// Dynamically set the customizer drawer location based on RTL layout
const customizerDrawerLocation = computed(() =>
  customizer.setRTLLayout ? 'left' : 'right'
);

// Dynamically set the main content margin based on RTL layout
const mainContentMarginClass = computed(() =>
  customizer.setRTLLayout ? 'ml-md-4' : 'mr-4'
);

onMounted(() => {
  // Only attempt to load user if not already set in the store
  if (!authStore.user) {
    const token = localStorage.getItem('token');
    if (token) {
      try {
        const decoded = jwtDecode<DecodedToken>(token);
        authStore.user = decoded;
        authStore.token = token;
      } catch (e) {
        console.error('❌ Invalid token. Logging out:', e);
        authStore.logout();
      }
    }
  }
});
</script>

<template>
  <v-locale-provider :rtl="customizer.setRTLLayout">
    <v-app
      :theme="customizer.actTheme"
      class="bg-containerBg"
      :class="[
        customizer.mini_sidebar ? 'mini-sidebar' : '',
        customizer.setHorizontalLayout ? 'horizontalLayout' : 'verticalLayout',
        customizer.setBorderCard ? 'cardBordered' : '',
      ]"
    >
      <v-navigation-drawer
        app
        temporary
        elevation="10"
        :location="customizerDrawerLocation"
        v-model="customizer.Customizer_drawer"
        width="320"
        class="customizer-drawer" >
        <Customizer />
      </v-navigation-drawer>

      <VerticalSidebarVue v-if="!customizer.setHorizontalLayout" />

      <div :class="customizer.boxed ? 'maxWidth' : 'full-header'">
        <VerticalHeaderVue v-if="!customizer.setHorizontalLayout" />
      </div>

      <v-main :class="mainContentMarginClass">
        <div class="mb-3 hr-layout bg-containerBg">
          <v-container fluid class="page-wrapper bg-background pt-md-8 rounded-xl">
            <div >
              <RouterView />
            </div>
          </v-container>
        </div>
      </v-main>
    </v-app>
  </v-locale-provider>
</template>

<style scoped>
/* You can add component-specific styles here if needed.
   For example, if 'customizer-drawer' needs specific styling:
.customizer-drawer {
  // your styles
}
*/
</style>