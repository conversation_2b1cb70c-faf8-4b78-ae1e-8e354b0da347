



<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Icon } from '@iconify/vue';
import NavItem from './NavItem/index.vue';

const props = defineProps<{
  item: any;
  level: number;
}>();

const emit = defineEmits(['toggle']);
const open = ref(false);

watch(open, (val) => {
  if (val) emit('toggle', props.item.id);
});

const hasChildren = computed(() => {
  return props.item.subOpciones && props.item.subOpciones.length > 0;
});
</script>

<template>
  <v-list-group
    v-if="hasChildren"
    v-model="open"
    :value="false"
    class="nav-collapse"
    color="primary"
  >
    <template v-slot:activator="{ props: activatorProps }">
      <v-list-item v-bind="activatorProps">
        <template v-slot:prepend>
          <Icon :icon="item.icon" height="20" width="20" class="mr-2" />
        </template>
        <v-list-item-title>{{ item.title }}</v-list-item-title>
      </v-list-item>
    </template>

    <template v-for="child in item.subOpciones" :key="child.id">
      <NavCollapse
        v-if="child.subOpciones && child.subOpciones.length"
        :item="child"
        :level="level + 1"
        @toggle="$emit('toggle', $event)"
      />
      <NavItem v-else :item="child" :level="level + 1" />
    </template>
  </v-list-group>

  <NavItem v-else :item="item" :level="level" />
</template>

<style scoped>
.level-1 {
  padding-left: 16px;
}
.level-2 {
  padding-left: 32px;
}
.level-3 {
  padding-left: 48px;
}
</style>
