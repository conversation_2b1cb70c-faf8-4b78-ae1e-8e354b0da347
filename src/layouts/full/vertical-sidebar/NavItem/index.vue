<template>
  <v-list-item
    :to="item.type !== 'external' ? item.url : undefined"
    :href="item.type === 'external' ? item.url : undefined"
    :target="item.type === 'external' ? '_blank' : undefined"
    rounded
    v-scroll-to="{ el: '#top' }"
  >
    <!-- <PERSON><PERSON><PERSON><PERSON> del item -->
    <v-list-item-title class="d-flex">
      <v-icon small class="mr-1 text-red">{{ item.icon }}</v-icon>
      {{ $t(item.descripcion) }}
    </v-list-item-title>
  </v-list-item>
</template>

<script setup>
import { Icon } from "@iconify/vue";

const props = defineProps({
  item: Object,
  level: Number,
});
</script>
