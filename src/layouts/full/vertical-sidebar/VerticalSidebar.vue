<script setup lang="ts">
import { ref, shallowRef, nextTick } from "vue";
import { useCustomizerStore } from "@/stores/customizer";
import NavItem from "./NavItem/index.vue";
import NavCollapse from "./NavCollapse/NavCollapse.vue";
import Logo from "../logo/Logo.vue";
import { useRoute } from "vue-router";
import { storeToRefs } from "pinia";
import type { OpcionAplicacion } from "@/utils/OpcionAplicacion";
import { useDisplay } from 'vuetify';
import { watch } from 'vue';


// Store
const customizer = useCustomizerStore();
const { Sidebar_drawer, mini_sidebar } = storeToRefs(customizer);

// Ruta
const route = useRoute();

// Menu desde localStorage
// Recuperar menú desde localStorage
function mapOpcionesToMenu(opciones: OpcionAplicacion[]): OpcionAplicacion[] {
  // Find or create "Seguridad" group
  let seguridadGroup = opciones.find(opt => opt.descripcion === "Seguridad");
  if (!seguridadGroup) {
    seguridadGroup = {
      id: 9000, // Placeholder ID, ideally from backend
      descripcion: "Seguridad",
      decripcionIngles: "Security",
      icon: "mdi-security", // Example icon
      url: null,
      padre: null,
      orden: 900, // Example order
      activa: true,
      permisos: {},
      subOpciones: []
    };
    opciones.push(seguridadGroup);
  }

  // Add "Roles" to "Seguridad" sub-options if not already present
  if (!seguridadGroup.subOpciones.some(sub => sub.url === "/seguridad/roles")) {
    seguridadGroup.subOpciones.push({
      id: 9001, // Placeholder ID
      descripcion: "Roles",
      decripcionIngles: "Roles",
      icon: "mdi-account-group", // Icon for roles
      url: "/seguridad/roles",
      padre: seguridadGroup.id,
      orden: 1,
      activa: true,
      permisos: {},
      subOpciones: []
    });
  }

  // Add "Usuarios" to "Seguridad" sub-options if not already present
  if (!seguridadGroup.subOpciones.some(sub => sub.url === "/seguridad/usuarios")) {
    seguridadGroup.subOpciones.push({
      id: 9002, // Placeholder ID
      descripcion: "Usuarios",
      decripcionIngles: "Users",
      icon: "mdi-account-multiple", // Icon for users
      url: "/seguridad/usuarios",
      padre: seguridadGroup.id,
      orden: 2,
      activa: true,
      permisos: {},
      subOpciones: []
    });
  }

  // Sort options and sub-options for consistent display
  opciones.sort((a, b) => a.orden - b.orden);
  opciones.forEach(opt => {
    if (opt.subOpciones) {
      opt.subOpciones.sort((a, b) => a.orden - b.orden);
    }
  });

  return opciones;
}

const opcionesGuardadas = localStorage.getItem("opciones");
const opciones = opcionesGuardadas ? JSON.parse(opcionesGuardadas) : [];
const sidebarMenu = shallowRef<OpcionAplicacion[]>(mapOpcionesToMenu(opciones));

// Ruta activa
const findTitleByPath = (
  items: OpcionAplicacion[],
  path: string
): number | null => {
  for (const item of items) {
    if (item.url === path) return item.id;
    if (item.subOpciones) {
      const found = findTitleByPath(item.subOpciones, path);
      if (found) return found;
    }
  }
  return null;
};

const foundId = findTitleByPath(sidebarMenu.value, route.path);
const currentMenu = ref<number | null>(
  foundId || sidebarMenu.value[0]?.id || null
);
const activeId = ref<number | null>(null);

// Alternar entre rail y expandido
async function toggleSidebar() {
  customizer.TOGGLE_MINI_SIDEBAR();
  await nextTick();
}

// Abrir/cerrar submenús
function handleToggle(id: number) {
  activeId.value = activeId.value === id ? null : id;
}

const { mdAndDown } = useDisplay();

watch(mdAndDown, (isSmallScreen) => {
  if (isSmallScreen) {
    customizer.mini_sidebar = false; 
  } else {
    customizer.Sidebar_drawer = false;
  }
});

</script>

<template>
  <v-navigation-drawer
  left
  v-model="customizer.Sidebar_drawer"
  :rail="customizer.mini_sidebar"
  :temporary="mdAndDown"
  app
  elevation="0"
  rail-width="85"
  width="360">

    <!-- Encabezado -->
    <div class="pa-4 pb-0">
      <Logo/>
      <!-- <v-btn icon rounded="md" variant="plain" @click.stop="toggleSidebar">
        <Icon :icon=" mini_sidebar ? 'solar:menu-dots-bold-duotone' : 'solar:hamburger-menu-line-duotone'" height="25"/>
      </v-btn> -->
    </div>

    <!-- Menú -->
    <perfect-scrollbar  class="scrollnavbar">
      <div class="px-4 py-0 sidebar-menus">
        <v-list class="py-1">
          <template v-for="item in sidebarMenu" :key="item.id">
            <NavCollapse           
              v-if="item.subOpciones && item.subOpciones.length"
              :item="item"
              :level="0"
              :active-id="activeId"
              @toggle="handleToggle"
              class="leftPadding"
            />
            <NavItem v-else :item="item" :level="0" class="leftPadding" />
          </template>
        </v-list>
      </div>
    </perfect-scrollbar>

    <!-- Footer -->
    <div class="text-center pa-3">
      <!-- <p class="text-caption copyright ">
        &copy; <strong>Samudev</strong> 2025 v0.1.1 Test Barna
      </p> -->
    </div>
  </v-navigation-drawer>
</template>

<style scoped>
/* .leftSidebar {
  transition: all 0.3s ease-in-out;
} */

/* .v-navigation-drawer--rail {
  width: 90px !important;
} */

.v-list-item__content {
  transition: opacity 0.3s ease-in-out;
}
/* .v-navigation-drawer--rail .v-list-item__content {
  opacity: 0;
  pointer-events: none;
}

.v-navigation-drawer:not(.v-navigation-drawer--rail) .v-list-item__content {
  opacity: 1;
  pointer-events: auto;
}

.v-navigation-drawer--rail .v-list-item__prepend {
  justify-content: center;
  width: 100%;
}

.v-list-item--active {
  border-radius: 12px;
}

.copyright {
  color: #8e8e8e;
  font-size: 12px;
  text-align: center;
} */
</style>
