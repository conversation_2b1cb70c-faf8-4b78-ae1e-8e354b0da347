<script setup lang="ts">
import { computed } from "vue";
import { Icon } from "@iconify/vue";
import NavItem from "../NavItem/index.vue";
import type { OpcionAplicacion } from "@/utils/OpcionAplicacion";
import { useCustomizerStore } from "@/stores/customizer";
import { storeToRefs } from "pinia";

const props = defineProps<{
  item: OpcionAplicacion;
  level: number;
  activeId: number | null;
}>();

const emit = defineEmits<{
  (e: "toggle", id: number): void;
}>();

const customizer = useCustomizerStore();
const { mini_sidebar } = storeToRefs(customizer);

const isOpen = computed(() => props.activeId === props.item.id);

const hasChildren = computed(() => {
  return props.item.subOpciones && props.item.subOpciones.length > 0;
});

function handleClick() {
  if (!mini_sidebar.value) {
    emit("toggle", props.item.id);
  }
}
</script>

<template>
  <!-- Si tiene hijos -->
  <v-list-group v-if="hasChildren" :v-model="isOpen" :disabled="mini_sidebar">
    <!-- Activador -->
    <template #activator="{ props: activatorProps }">
      <v-list-item
        v-bind="activatorProps"
        @click="handleClick()"
        rounded="lg"
      >
        <template #prepend>
          <Icon
            :icon="item.icon"
            height="20"
            width="20"
            class="mr-2"
          />
        </template>
        <v-list-item-title>{{ item.descripcion }}</v-list-item-title>
      </v-list-item>
    </template>

    <!-- Subitems -->
    <template v-for="child in item.subOpciones" :key="child.id">
      <NavCollapse
        v-if="child.subOpciones && child.subOpciones.length"
        :item="child"
        :level="level + 1"
        :active-id="activeId"
        @toggle="$emit('toggle', item.id)"
      />
      <NavItem v-else :item="child" :level="level + 1" />
    </template>
  </v-list-group>

  <!-- Si no tiene hijos -->
  <NavItem v-else :item="item" :level="level" />
</template>

<style scoped>


.level-2 {
  padding-left: 32px;
}
.level-3 {
  padding-left: 48px;
}




</style>
