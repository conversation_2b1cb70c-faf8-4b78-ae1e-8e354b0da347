<script setup lang="ts">
import { profileDD } from "@/_mockApis/headerData";
import profileDefault from "@/assets/images/profile/Default.png";
import { useAuthStore } from "@/stores/auth";

import { ref } from "vue";

const authStore = useAuthStore();

// ✅ Interfaz para los datos del usuario (puedes ajustarla si tienes más campos)
interface TokenPayload {
  usuaNombre: string;
  email: string;
  fotoPerfil: string;
  [key: string]: any; // Permite que se agreguen otros campos si es necesario
}

// ✅ Variable reactiva para el usuario completo
const decodedUser = ref<TokenPayload | null>(null);

// ✅ Intentar obtener el usuario desde localStorage
const userString = localStorage.getItem("user");

if (userString) {
  try {
    // ✅ Parsear JSON y asignarlo a decodedUser
    decodedUser.value = JSON.parse(userString);
  } catch (e) {
    console.error("❌ Error al parsear user del localStorage:", e);
  }
}

// ✅ Acceso directo a la imagen si la necesitas por separado
const fotoPerfil = ref<string | null>(decodedUser.value?.fotoPerfil || null);
</script>

<template>
  <!-- ---------------------------------------------- -->
  <!-- notifications DD -->
  <!-- ---------------------------------------------- -->
  <v-menu open-on-hover open-on-click>
    <template v-slot:activator="{ props }">
      <v-btn
        variant="text"
        class="custom-hover-primary"
        color="primary"
        v-bind="props"
        icon
      >
        <v-avatar size="35">
          <img
            :src="decodedUser?.fotoPerfil || profileDefault"
            width="35"
            height="35"
            style="object-fit: cover; object-position: center"
            alt="Foto perfil"
          />
        </v-avatar>
      </v-btn>
    </template>
    <v-sheet rounded="md" width="280" elevation="10">
      <div class="pa-6">
        <div class="d-flex flex-column align-center text-center pb-6">
          <v-avatar size="55">
            <img
              :src="decodedUser?.fotoPerfil || profileDefault"
              width="55"
              height="55"
              alt="Foto perfil"
              style="object-fit: cover; object-position: center"
            />
          </v-avatar>

          <div class="mt-3">
            <h6 class="text-subtitle-1">
              {{ (decodedUser?.user || "Usuario").toUpperCase() }}
            </h6>
            <span class="text-subtitle-1 textSecondary">
              {{ (decodedUser?.email || "<EMAIL>").toUpperCase() }}
            </span>
          </div>
        </div>
        <v-divider></v-divider>
        <perfect-scrollbar
          style="height: calc(100vh - 240px); max-height: 240px"
        >
          <v-list class="py-0 theme-list mt-3" lines="one">
            <v-list-item
              v-for="item in profileDD"
              :key="item.title"
              class="py-0 px-2 rounded-md custom-text-primary"
              color="primary"
              :to="item.href"
            >
              <div class="d-flex gap-3 align-center">
                <p class="text-subtitle-1 heading custom-title">
                  {{ item.title }}
                </p>
                <v-chip size="small" color="error" v-if="item.badge">4</v-chip>
              </div>
            </v-list-item>
          </v-list>
        </perfect-scrollbar>
      </div>
    </v-sheet>
  </v-menu>
</template>
