import type { auth } from "./utils/Microsoft/auth";

function parseXML(): Document | null {
  const xml = new XMLHttpRequest();
  xml.open("GET", "/institucion.xml", false); // 👈 usa tu archivo real en public
  xml.send();

  let xmlData = xml.responseXML;

  if (!xmlData) {
    xmlData = new DOMParser().parseFromString(xml.responseText, "text/xml");
  }

  return xmlData;
}

const Configuration = {
  getInstitucion(): string | null {
    const cached = localStorage.getItem("_GetInsVal");
    if (cached !== null) return cached;

    const xmlData = parseXML();
    if (!xmlData) return null;

    const institucion =
      xmlData.getElementsByTagName("institucion")[0]?.textContent || null;

    if (institucion) {
      localStorage.setItem("_GetInsVal", institucion);
    }

    return institucion;
  },

  getEmail(): string | null {
    const xmlData = parseXML();
    if (!xmlData) return null;

    return xmlData.getElementsByTagName("email")[0]?.textContent || null;
  },

  getTelefono(): string | null {
    const xmlData = parseXML();
    if (!xmlData) return null;

    return xmlData.getElementsByTagName("telefono")[0]?.textContent || null;
  },

  getSystem(): string | null {
    const xmlData = parseXML();
    if (!xmlData) return null;

    return xmlData.getElementsByTagName("system")[0]?.textContent || null;
  },

  getCopyright(): string | null {
    const xmlData = parseXML();
    if (!xmlData) return null;

    return xmlData.getElementsByTagName("copyright")[0]?.textContent || null;
  },

  getSoftwareId(): string | null {
    const xmlData = parseXML();
    if (!xmlData) return null;

    return xmlData.getElementsByTagName("software")[0]?.textContent || null;
  },

  getMicrosoft365Configuration(): auth | null {
    const xmlData = parseXML();
    if (!xmlData) return null;

    const institucion =
      xmlData.getElementsByTagName("institucion")[0]?.textContent;
    if (!institucion) return null;

    let clientId: string | null = null;
    let tenantId: string | null = null;
    let redirectUri: string | null = null;

    switch (institucion) {
      case "PROBUS":
        clientId = "dff961e7-e537-46a0-806f-1c5aa1aa121c";
        tenantId = "672aed78-c89c-47c1-ae37-b83bf8b8241b";
        redirectUri = "https://management.probusrd.com";
        break;

      default:
        return null;
    }

    const configuration: auth = {
      clientId,
      tenantId,
      redirectUri,
    };

    return configuration;
  },

  getGoogleKeyV3(): string | null {
    const xmlData = parseXML();
    if (!xmlData) return null;

    const institucion =
      xmlData.getElementsByTagName("institucion")[0]?.textContent;
    if (!institucion) return null;

    switch (institucion) {
      case "PROBUS":
        return "6LdouSArAAAAAO8A7dTO-jiQs6jaONEvw9GUOwLV";
      default:
        return null;
    }
  },
};

// 👇 Exportación por defecto y con nombre
export default Configuration;
export { Configuration };
