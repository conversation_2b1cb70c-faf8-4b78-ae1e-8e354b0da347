const MainRoutes = {
  path: "/main",
  meta: {
    requiresAuth: true,
  },
  redirect: "/main",
  component: () => import("@/layouts/full/FullLayout.vue"),
  children: [
    {
      name: "Dashboard1",
      path: "/dashboards/dashboard1",
      component: () => import("@/views/dashboards/dashboard1/index.vue"),
    },

    //Admisiones

  {
    name: "Solicitud de admision",
    path: "/academicos/solicitud-admision", // ✅ sin barra inicial
    component: () => import("@/views/pages/Operaciones/admisiones/SolicitudAdmision/SolicitudAdmision.vue"),
  },

    //Horarios
    {
      path: "/academicos/aulas-asignadas-carrera",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Horarios/AulaAsignacionCarrera.vue"
        ),
    },
    {
      path: "/academicos/asignaturas",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/Asignaturas.vue"),
    },
    {
      path: "/academicos/calificaciones-literales",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Academicos/CalificacionLiteral.vue"
        ),
    },
    {
      path: "/academicos/carrera-tipo-integrantes",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Academicos/CarreraTipoIntegrante.vue"
        ),
    },
    {
      path: "/academicos/documentos-academicos",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Academicos/DocumentosAcademicos.vue"
        ),
    },
    {
      path: "/academicos/universidades",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/Universidad.vue"),
    },
    {
      path: "/academicos/tipos-universidades",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/TipoUniversidades.vue"),
    },
    {
      path: "/academicos/tipos-colegios",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/TipoColegio.vue"),
    },
    {
      path: "/academicos/colegios",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/Colegios.vue"),
    },
    {
      path: "/academicos/tipos-calificaciones-plantillas",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Academicos/TipoCalificacionPlantilla.vue"
        ),
    },
    {
      path: "/academicos/admitido-como",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/AdmitidosComo.vue"),
    },
    {
      path: "/academicos/estados-estudiantes",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Academicos/EstadosEstudiantes.vue"
        ),
    },
    {
      path: "/academicos/estudiantes-grado-obtenido",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Academicos/EstudiantesGradoObtenido.vue"
        ),
    },
    {
      path: "/academicos/financiamientos-estudios",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Academicos/FinanciamientosEstudios.vue"
        ),
    },
    {
      path: "/academicos/honor-academico",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/HonorAcademico.vue"),
    },
    {
      path: "/academicos/motivos-disciplinas",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/MotivosDisciplina.vue"),
    },
    {
      path: "/academicos/planes-estudios",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/PlanEstudio.vue"),
    },
    {
      path: "/academicos/procesos",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/Procesos.vue"),
    },
    {
      path: "/academicos/tipos-asignaturas",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/TiposAsignaturas.vue"),
    },
    {
      path: "/academicos/estudiantes",
      component: () =>
        import("@/views/pages/Operaciones/Academicos/estudiantes/estudiante.vue"),
    },
    {
      path: "/academicos/profesores",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/Profesor.vue"),
    },

    //Generales
    {
      path: "/generales/idiomas",
      component: () =>
        import("@/views/pages/tablasMaestras/Generales/Idiomas.vue"),
    },
    {
      path: "/generales/municipios",
      component: () =>
        import("@/views/pages/tablasMaestras/Generales/Municipio.vue"),
    },

    //Planta Fisica
    {
      path: "/planta-fisica/edificios",
      component: () =>
        import("@/views/pages/tablasMaestras/Planta_Fisica/Edificio.vue"),
    },
    {
      path: "/planta-fisica/tipos-aulas",
      component: () =>
        import("@/views/pages/tablasMaestras/Planta_Fisica/TipoAulas.vue"),
    },
    {
      path: "/planta-fisica/aulas",
      component: () =>
        import("@/views/pages/tablasMaestras/Planta_Fisica/Aulas.vue"),
    },
    {
      path: "/planta-fisica/tipos-facilidades",
      component: () =>
        import("@/views/pages/tablasMaestras/Planta_Fisica/TipoFacilidad.vue"),
    },
    {
      path: "/academicos/tipos-asignaturas-electivas",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Academicos/TiposAsignaturasElectivas.vue"
        ),
    },
    {
      path: "/planta-fisica/recintos",
      component: () =>
        import("@/views/pages/tablasMaestras/Planta_Fisica/Sedes.vue"),
    },

    {
      path: "/academicos/tipos-carreras",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/TiposCarreras.vue"),
    },
    {
      path: "/academicos/tipos-estudiantes-mensajes",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Academicos/TipoEstudianteMensaje.vue"
        ),
    },
    {
      path: "/academicos/tipos-grados-titulos",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/TipoGradoTitulo.vue"),
    },
    {
      path: "/academicos/tipos-procesos",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/TiposProcesos.vue"),
    },

    {
      path: "/academicos/tipograduacion",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/TipoGraduacion.vue"),
    },
    {
      path: "/academicos/transferidos-como",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/TransferidoComo.vue"),
    },
    {
      path: "/academicos/tipos-excepciones-seleccion",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Seleccion/TipoExcepcionSeleccion.vue"
        ),
    },
    {
      path: "/academicos/escuelas",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/Escuelas.vue"),
    },
    {
      path: "/academicos/facultades",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/Facultades.vue"),
    },

    {
      path: "/academicos/carreras",
      component: () =>
        import("@/views/pages/tablasMaestras/Academicos/carreraAcademico.vue"),
    },

   ///Seguridad
   {
     path: "/seguridad/roles",
     component: () => import("@/views/pages/seguridad/roles.vue"),
     meta: { needsAuth: true },
   },
   {
     path: "/seguridad/usuarios",
     component: () => import("@/views/pages/seguridad/usuarios.vue"),
     meta: { needsAuth: true },
   },
   {
     name: "Permisos de Rol",
     path: "/seguridad/permisos/:rolId",
     component: () => import("@/views/pages/seguridad/permisos.vue"),
     meta: { needsAuth: true },
   },
 
    ///Finaciero

    {
      path: "/Financieros/bancos",
      component: () =>
        import("@/views/pages/tablasMaestras/Financiera/Bancos.vue"),
    },

    {
      path: "/Financieros/tipos-ncf",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Financiera/tipoNcfFinancieros.vue"
        ),
    },
    {
      path: "/Financieros/tipos-formas-pagos",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Financiera/formaPagoFinancieros.vue"
        ),
    },
    {
      path: "/Financieros/ncf",
      component: () =>
        import("@/views/pages/tablasMaestras/Financiera/ncfFinancieros.vue"),
      meta: { needsAuth: true },
    },
    {
      path: "/Financieros/tipos-formas-pagos",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Financiera/formaPagoFinancieros.vue"
        ),
      meta: { needsAuth: true },
    },
    {
      path: "/Financieros/tipos-tarjetas-creditos",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Financiera/TiposTarjetaCreditoFinancieros.vue"
        ),
      meta: { needsAuth: true },
    },
    {
      path: "/Financieros/tipos-descuentos",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Financiera/tiposDescuentosFinancieros.vue"
        ),
      meta: { needsAuth: true },
    },
    {
      path: "/Financieros/tipos-cajeros",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Financiera/tipoCajeroFinancieros.vue"
        ),
      meta: { needsAuth: true },
    },
    {
      path: "/Financieros/monedas-pagos",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Financiera/monedaPagoFinancieros.vue"
        ),
      meta: { needsAuth: true },
    },
    {
      path: "/Financieros/descuentos-justificaciones",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Financiera/descuentosJustificacionesFinancieros.vue"
        ),
      meta: { needsAuth: true },
    },
    {
      path: "/Financieros/cajas",
      component: () =>
        import("@/views/pages/tablasMaestras/Financiera/cajaFinancieros.vue"),
      meta: { needsAuth: true },
    },
    {
      path: "/Financieros/servicio-contable-grupo",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Financiera/servicioContableGrupoFinancieros.vue"
        ),
      meta: { needsAuth: true },
    },
    {
      path: "/Financieros/tipo-cargo-patrocinio",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Financiera/tipoCargoPatrocinioFinancieros.vue"
        ),
      meta: { needsAuth: true },
    },
    {
      path: "/Financieros/catalogo-cuenta",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Financiera/cuentaContableCatalogo.vue"
        ),
      meta: { needsAuth: true },
    },
    {
      path: "/financieros/profesor-categoria-pago",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Financiera/profesorCategoriaPagoFinancieros.vue"
        ),
      meta: { needsAuth: true },
    },
    {
      path: "/financieros/aula-pago",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Financiera/aulaPagoFinancieros.vue"
        ),
      meta: { needsAuth: true },
    },
    {
      path: "/financieros/planes-pagos",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Financiera/planPagoFinancieros.vue"
        ),
      meta: { needsAuth: true },
    },
    {
      path: "/financieros/estudiante-descuento-pagos",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Financiera/estudianteDescuentoPagoFinancieros.vue"
        ),
      meta: { needsAuth: true },
    },
    {
      path: "/generales/dgii-rnc",
      component: () =>
        import("@/views/pages/tablasMaestras/Generales/DgiiRncGenerales.vue"),
      meta: { needsAuth: true },
    },
    {
      path: "/generales/cargos",
      component: () =>
        import("@/views/pages/tablasMaestras/Generales/Cargo.vue"),
    },
    {
      path: "/generales/paises",
      component: () =>
        import("@/views/pages/tablasMaestras/Generales/Paises.vue"),
    },
    {
      path: "/generales/estados-ciudadanos",
      component: () =>
        import("@/views/pages/tablasMaestras/Generales/EstadoCiudadano.vue"),
    },
    {
      path: "/generales/estados-civiles",
      component: () =>
        import("@/views/pages/tablasMaestras/Generales/EstadoCivil.vue"),
    },
    {
      path: "/generales/estatus",
      component: () =>
        import("@/views/pages/tablasMaestras/Generales/Estatus.vue"),
    },
    {
      path: "/generales/ocupaciones",
      component: () =>
        import("@/views/pages/tablasMaestras/Generales/Ocupacion.vue"),
    },
    {
      path: "/generales/redes-sociales",
      component: () =>
        import("@/views/pages/tablasMaestras/Generales/RedSocial.vue"),
    },
    {
      path: "/generales/tipos-documentos-identificacion",
      component: () =>
        import(
          "@/views/pages/tablasMaestras/Generales/TipoDocumentoIdentificacion.vue"
        ),
    },
    {
      path: "/generales/tipos-padres",
      component: () =>
        import("@/views/pages/tablasMaestras/Generales/TipoPadre.vue"),
    },
    {
      path: "/generales/tipos-personas",
      component: () =>
        import("@/views/pages/tablasMaestras/Generales/TipoPersona.vue"),
    },
    {
      path: "/generales/tipos-telefonos",
      component: () =>
        import("@/views/pages/tablasMaestras/Generales/TipoTelefono.vue"),
    },
    {
      path: "/generales/motivos",
      component: () =>
        import("@/views/pages/tablasMaestras/Generales/Motivo.vue"),
    },
    {
      path: "/generales/nacionalidades",
      component: () =>
        import("@/views/pages/tablasMaestras/Generales/Nacionalidad.vue"),
    },
    {
      path: "/generales/provincias",
      component: () =>
        import("@/views/pages/tablasMaestras/Generales/Provincias.vue"),
    },
    {
      name: "Maestria",
      path: "/apps/maestria",
      component: () => import("@/views/apps/maestria/Maestria.vue"),
    },

    {
      name: "/",
      path: "/dashboards/dashboard1",
      component: () => import("@/views/dashboards/dashboard1/index.vue"),
    },

    {
      name: "Ajustes de cuenta",
      path: "/pages/account-settings",
      component: () => import("@/views/pages/accountSettings.vue"),
    },
  ],
};

export default MainRoutes;
