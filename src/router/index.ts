import { createRouter, createWebHistory } from "vue-router";
import Login from "@/views/authentication/Login.vue";
import Dashboard from "@/views/dashboards/dashboard1/index.vue";
import MainRoutes from "./MainRoutes";
import noAuthRoutes from './NoAuthRoutes';
import NotFound from '@/views/apps/NotFound.vue'; // Import the NotFound component
import { useAuthStore } from "@/stores/auth";
import FullLayout from "@/layouts/full/FullLayout.vue";
import BlankLayout from '../layouts/full/Blanklayout.vue'; // Import the BlankLayout component


const routes = [
  { path: "/", redirect: "/login" },
  { path: "/login", component: Login },


  {
    path: '/',
    component: BlankLayout,
    children: [
      {
        path: 'solicitud-admision',
        name: 'SolicitudAdmisiones',
        component: () => import('@/views/apps/admisiones/SolicitudAdmision.vue')
      },
      ...noAuthRoutes.children // Rutas que no requieren autenticación
    ]
  },

  {
    path: "/",
    component: FullLayout, // layout padre con sidebar y header
    children: [
      {
        path: "dashboards/dashboard1",
        component: Dashboard,
        meta: { requiresAuth: true },
      },
     
      ...MainRoutes.children,
      {
        path: '/seguridad/permisos/:rolId',
        name: 'PermisosRol',
        component: () => import('@/views/pages/seguridad/permisos.vue'),
        meta: { requiresAuth: true },
      },
    ],
  },
 
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// Middleware global
router.beforeEach((to, from, next) => {
  const auth = useAuthStore();

  // validar token expirado (opcional)
  const isLoggedIn = !!auth.token;
  if (to.meta.requiresAuth && !isLoggedIn) {
    auth.returnUrl = to.fullPath;
    return next("/login");
  }

  next();
});

export { router };