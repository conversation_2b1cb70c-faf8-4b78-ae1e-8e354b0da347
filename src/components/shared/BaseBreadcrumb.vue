<script setup lang="ts">
import { Icon } from "@iconify/vue";
const props = defineProps({
  title: String,
  breadcrumbs: Array,
  icon: String,
  text: String,
});
</script>
<script lang="ts">
export default {
  name: "baseBreadcrumb",
};
</script>

<template>
  <v-card elevation="10" class="mb-8">
    <div class="px-6 py-4">
      <div class="d-flex justify-space-between align-center">
        <h2 class="text-h5 font-weight-medium d-flex align-center gap-2">
          <v-icon v-if="icon" size="20" aria-hidden="true">
            {{ icon }}
          </v-icon>
          {{ title }}
        </h2>
        <v-breadcrumbs :items="breadcrumbs" class="pa-0">
          <template v-slot:prepend>
            <router-link to="/dashboards/dashboard1" class="textSecondary lh-0">
              <Icon icon="solar:home-2-line-duotone" height="20" />
            </router-link>
          </template>
          <template v-slot:divider>
            <div class="d-flex align-center textSecondary"></div>
          </template>
          <template v-slot:title="{ item }">
            <v-chip size="small" class="rounded-sm" color="primary">{{
              item.text
            }}</v-chip>
          </template>
        </v-breadcrumbs>
      </div>
    </div>
  </v-card>
</template>

<style lang="scss">
.page-breadcrumb {
  .v-toolbar {
    background: transparent;
  }
}
</style>
