<script setup lang="ts">
import { ref } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { Form } from 'vee-validate';
/* Social icons */
import google from '@/assets/images/svgs/google-icon.svg';
import facebook from '@/assets/images/svgs/facebook-icon.svg';
import { useRouter } from 'vue-router'
const router = useRouter()

function nuevaSolicitud() {
  // marco la flag
  sessionStorage.setItem('showStepper', 'true')
  // navego a la ruta
  router.push({ name: 'Solicitud Admisiones' })
}
</script>

<template>
  <v-container class="pa-0">
    <!-- Botón para nueva solicitud de admisión -->
    <v-row no-gutters class="align-center mb-4">
      <v-col>
        <v-btn
        variant="text"
        color="primary"
        size="small"
        @click="nuevaSolicitud"
      >
        + NUEVA SOLICITUD DE ADMISIÓN
      </v-btn>
      </v-col>
    </v-row>

    <!-- Título y subtítulo -->
    <v-row no-gutters align="center" class="mb-2">
      <v-icon small class="mr-2">mdi-pencil</v-icon>
      <span class="text-h6 font-weight-medium">Ver o modificar una solicitud guardada</span>
    </v-row>
    <p class="subtitle-1 mb-6">
      Si ya ha iniciado una solicitud y quiere acceder a sus respuestas para modificarlas o consultarlas, ingrese el código que le enviamos a su email.
    </p>

    <!-- Formulario de inicio de sesión -->
    <Form @submit="validate" v-slot="{ errors, isSubmitting }" class="mt-5">
      <v-label class="font-weight-semibold pb-2">Usuario</v-label>
      <VTextField
        v-model="username"
        :rules="emailRules"
        class="mb-8"
        required
        hide-details="auto"
      />

      <div class="d-flex justify-space-between align-center pb-2">
        <v-label class="font-weight-semibold">Contraseña</v-label>
        <RouterLink
          to="/auth/forgot-password2"
          class="text-primary text-decoration-none font-weight-medium"
        >
          ¿Olvidaste tu contraseña?
        </RouterLink>
      </div>
      <VTextField
        v-model="password"
        :rules="passwordRules"
        required
        hide-details="auto"
        type="password"
        class="pwdInput"
      />

      <div class="d-flex flex-wrap align-center my-3 ml-n2">
        <v-checkbox
          class="pe-2"
          v-model="checkbox"
          :rules="[(v: any) => !!v || 'Debes aceptar para continuar']"
          required
          hide-details
          color="primary"
        >
          <template v-slot:label class="font-weight-medium">
            Mantenerme conectado
          </template>
        </v-checkbox>
      </div>

      <v-btn
        size="large"
        :loading="isSubmitting"
        color="primary"
        :disabled="valid"
        block
        type="submit"
        flat
      >
        Iniciar sesión
      </v-btn>

      <div v-if="errors.apiError" class="mt-2">
        <v-alert color="error">{{ errors.apiError }}</v-alert>
      </div>
    </Form>
  </v-container>
</template>
