<template>
  <div class="notification-container">
    <v-snackbar
      v-for="notification in notifications"
      :key="notification.id"
      v-model="notification.visible"
      :color="getColor(notification.type)"
      :location="notification.position"
      :timeout="notification.duration"
      rounded="md"
      elevation="6"
      class="notification-toast"
      @update:modelValue="handleVisibilityChange(notification.id, $event)"
    >
      <div class="d-flex align-center gap-2">
        <v-icon :icon="getIcon(notification.type)" size="20"></v-icon>
        <span class="text-body-2">{{ notification.message }}</span>
      </div>
      
      <template v-slot:actions>
        <v-btn
          variant="text"
          size="small"
          icon="mdi-close"
          @click="hideNotification(notification.id)"
        ></v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { useNotifications } from '@/composables/useNotifications';

const { notifications, hideNotification } = useNotifications();

const getColor = (type: string) => {
  const colors = {
    success: 'success',
    error: 'error',
    warning: 'warning',
    info: 'info'
  };
  return colors[type as keyof typeof colors] || 'info';
};

const getIcon = (type: string) => {
  const icons = {
    success: 'mdi-check-circle',
    error: 'mdi-alert-circle',
    warning: 'mdi-alert',
    info: 'mdi-information'
  };
  return icons[type as keyof typeof icons] || 'mdi-information';
};

const handleVisibilityChange = (id: string, visible: boolean) => {
  if (!visible) {
    hideNotification(id);
  }
};
</script>

<style scoped>
.notification-container {
  position: fixed;
  z-index: 9999;
  pointer-events: none;
}

.notification-toast {
  pointer-events: auto;
  margin-bottom: 8px;
}

.notification-toast .v-snackbar__wrapper {
  min-width: 300px;
  max-width: 500px;
}
</style>
