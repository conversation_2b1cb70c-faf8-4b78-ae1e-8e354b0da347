<template>
  <v-container fluid class="tabs-container">
    <v-tabs
      v-model="activeTab"
      color="primary"
      align-tabs="center"
      :fixed-tabs="display.smAndUp.value"
      :stacked="display.xs.value"
      show-arrows

      class="mb-4"
    >
      <v-tab
        v-for="(tab, index) in tabs"
        :key="index"
        :value="tab.value"
        :prepend-icon="tab.icon"
      >
        {{ tab.label }}
      </v-tab>
    </v-tabs>

    <v-window v-model="activeTab">
      <v-window-item
        v-for="(tab, index) in tabs"
        :key="index"
        :value="tab.value"
        eager
      >
        <v-card flat>
          <v-card-text class="pb-2 pt-2 pl-2 pr-2">
            <slot :name="tab.value">
           <v-row></v-row>
            </slot>
          </v-card-text>
        </v-card>
      </v-window-item>
    </v-window>
  </v-container>
</template>

<script lang="ts">
import { defineComponent, ref } from "vue";
import { useDisplay } from "vuetify";

interface Tab {
  label: string;
  value: string;
  icon?: string;
  content?: string;
}

interface Props {
  tabs: Tab[];
}

export default defineComponent({
  name: "wTabsCard",
  props: {
    tabs: {
      type: Array as () => Tab[],
      required: true,
      validator: (tabs: Tab[]) => tabs.every((tab) => tab.label && tab.value),
    },
  },
  setup() {
    const activeTab = ref<string>("");
    const display = useDisplay();

    return {
      activeTab,
      display,
    };
  },
});
</script>
