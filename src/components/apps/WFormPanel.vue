<script setup lang="ts">
import { ref } from "vue";
import BaseBreadcrumb from "../shared/BaseBreadcrumb.vue";
import wDataTable from "./wDataTable.vue";

const props = withDefaults(
  defineProps<{
    loading: boolean;
    title: string;
    breadcrumbs: any[];
    filters: any[];
    headers: any[];
    items: any[];
    icon: string;
    text: string;
    dialogWidth: string;
    filtervalue: any | null;
    pageNumber: number;
    pageSize: number;
    totalPages: number;
    totalRecords: number;
  }>(),
  {
    loading: false,
    filters: () => [],
    filtervalue: null,
  }
);

const add = ref(false); // Para controlar si el diálogo está abierto

function newItem() {
  add.value = true;
  emit("newItem");
}

function update() {
  emit("update");
  emit("updateDone");
  add.value = false; // cerrar diálogo luego de guardar
}

function cancel() {
  add.value = false; // cerrar diálogo al cancelar
  emit("cancel");
}


const emit = defineEmits<{
  (e: "editItem", item: any): void;
  (e: "update"): void;
  (e: "updateDone"): void;
  (e: "searchItem", value: string, pageNumber: number, pageSize: number): void;
  (e: "newItem"): void;
  (e: "newItemDone"): void;
  (e: "cancel"): void;
}>();

const breadcrumbs = ref([{ text: props.title, disabled: true, href: "#" }]);
const search = ref("");

const editando = ref(false); // Controla si estás editando
const itemActual = ref<any>(null); // El ítem a editar

function editItem(item: any) {
  itemActual.value = item;
  editando.value = true;
  add.value = false; // Cerrar el diálogo de añadir si está abierto
  emit("editItem", item);
}


function searchItem() {
  emit("searchItem", search.value, props.pageNumber, props.pageSize);
}

function goTo(pageNumber: number, pageSize: number) {
  emit("searchItem", search.value, pageNumber, pageSize);
}

/* function update() {
  console.log("update capa dos");
  emit("update");
  emit("updateDone");
}

function cancel() {
  emit("cancel");
}

function newItem() {
  emit("newItem");
} */
</script>

<template>
  <BaseBreadcrumb :icon="icon" :title="title" :breadcrumbs="breadcrumbs" />

  <v-card>
    <v-card-text>
      <v-row>
        <v-col cols="12" sm="6">
          <v-row align="center">
            <v-col cols="5" v-if="props.filters.length > 0">
              <v-select
                v-model="props.filtervalue"
                :items="props.filters"
                item-title="text"
                item-value="value"
                label="Filtrar por"
                variant="outlined"
                return-object
                chips
                chip-color="primary"
                hide-details
              />
            </v-col>
            <v-col cols="5">
              <v-text-field
                label="Buscar"
                dense
                hide-details
                v-model="search"
                @keypress.enter="searchItem"
              />
            </v-col>
            <v-col cols="2" class="text-right">
              <v-btn color="grey200" @click="searchItem" :loading="loading">
                <v-icon>mdi-magnify</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </v-col>

        <v-col cols="12" sm="6" class="text-right">
          <v-btn color="primary" @click.stop="newItem">
            <v-icon>mdi-plus</v-icon> Nuevo
          </v-btn>
        </v-col>

        <v-col cols="12">
        <wDataTable
          :dialogWidth="dialogWidth"
          :loading="loading"
          :title="title"
          :headers="headers"
          :items="items"
          :pageNumber="pageNumber"
          :pageSize="pageSize"
          :totalPages="totalPages"
          :totalRecords="totalRecords"
          :add="add"
          @update:add="(val) => add = val"
          @update="update"
          @newItem="newItem"
          @editItem="editItem" 
          :usarDialogoInterno="false"
          @goTo="goTo"
        />

        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>
