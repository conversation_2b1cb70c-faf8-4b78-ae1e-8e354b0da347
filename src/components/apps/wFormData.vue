<script setup lang="ts">
import { ref, computed } from "vue";
import BaseBreadcrumb from "../shared/BaseBreadcrumb.vue";
import wDataTable from "./wDataTable.vue";
import { menuPermisos } from "@/stores/Permisos";

const props = withDefaults(
  defineProps<{
    loading: boolean;
    title: string;
    filters: any[] | null;
    headers: any[];
    items: any[];
    icon: string;
    text: string;
    dialogWidth: string;
    filtervalue: any | null;
    pageNumber: number;
    pageSize: number;
    totalPages: number;
    totalRecords: number;
    add?: boolean;
    disableInternalNewModal?: boolean;
    panel?: boolean | null; // nueva prop opcional para controlar el panel
    customActions?: { icon: string; link: string; tooltip: string }[]; // Nueva prop para botones de acción personalizados
  }>(),
  {
    loading: false,
    panel: false,
    add: true,
    filtervalue: null,
    disableInternalNewModal: false, // nueva prop opcional para controlar el modal interno
    customActions: () => [], // Valor por defecto para customActions
  }
);

const emit = defineEmits<{
  (e: "editItem", item: any): void;
  (e: "update"): void;
  (e: "updateDone"): void;
  (e: "searchItem", value: string, pageNumber: number, pageSize: number): void;
  (e: "newItem"): void;
  (e: "newItemDone"): void;
  (e: "save"): void;
  (e: "saveDone"): void;
  (e: "update:add", value: boolean): void;
  (e: "update:editoradd", value: boolean): void;
}>();

const breadcrumbs = ref([{ text: props.title, disabled: true, href: "#" }]);
const search = ref("");
const nuevoItem = ref(false);
const newOrEditPanel = ref(false);

const defaultPermisos = {
  ver: false,
  crear: false,
  editar: false,
  eliminar: false,
  imprimir: false,
};
const permiso = computed(
  () => menuPermisos.getPermisosOpcionMenu() || defaultPermisos
);

function editItem(item: any) {
  if (props.panel) {
    newOrEditPanel.value = true;
  }
  emit("editItem", item);
}

function searchItem() {
  emit("searchItem", search.value, props.pageNumber, props.pageSize);
}

function goTo(pageNumber: number, pageSize: number) {
  emit("searchItem", search.value, pageNumber, pageSize);
}

function update() {
  emit("update");
  emit("updateDone");
}

function save() {
  emit("save");
  emit("saveDone");
}

function newItem() {
  emit("newItem");

  // ✅ solo abrir modal interno si la prop lo permite
  if (!props.disableInternalNewModal) {
    nuevoItem.value = true;
  }
  if (props.panel) {
    newOrEditPanel.value = true;
  }
}
</script>

<template>
  <BaseBreadcrumb :icon="icon" :title="title" :breadcrumbs="breadcrumbs" />

  <v-card>
    <v-card-text>
      <v-row>
        <v-col cols="12" sm="6" v-if="!newOrEditPanel">
          <v-row align="center">
            <v-col cols="5" v-if="props.filters != null">
              <v-select
                v-model="props.filtervalue"
                :items="props.filters"
                item-title="text"
                item-value="value"
                label="Filtrar por"
                variant="outlined"
                return-object
                chips
                chip-color="primary"
                hide-details
              />
            </v-col>
            <v-col cols="5">
              <v-text-field
                label="Buscar"
                dense
                hide-details
                v-model="search"
                @keypress.enter="searchItem"
              />
            </v-col>
            <v-col cols="2" class="text-right">
              <v-btn color="grey200" @click="searchItem" :loading="loading">
                <v-icon>mdi-magnify</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </v-col>

        <v-col cols="12" sm="6" class="text-right" v-if="!newOrEditPanel">
          <v-btn
            color="primary"
            @click.stop="newItem()"
            v-if="permiso.crear && props.add"
          >
            <v-icon>mdi-plus</v-icon> Nuevo
          </v-btn>
        </v-col>
        <v-col cols="12">
          <wDataTable
            :panel="panel"
            :dialogWidth="dialogWidth"
            :loading="loading"
            :title="title"
            :headers="headers"
            :items="items"
            :pageNumber="pageNumber"
            :pageSize="pageSize"
            :totalPages="totalPages"
            :totalRecords="totalRecords"
            v-model:add="nuevoItem"
            :editoradd="newOrEditPanel"
            @update:editoradd="(value) => (newOrEditPanel = value)"
            @editItem="editItem"
            @update="update"
            @save="save"
            @newItem="newItem()"
            @goTo="goTo"
            :customActions="customActions"
          >
            <template #editItem>
              <slot name="editItemPanel"></slot>
            </template>
          </wDataTable>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>
<script lang="ts">
export default {
  name: "wFormData",
  components: {
    BaseBreadcrumb,
    wDataTable,
  },
};
</script>
