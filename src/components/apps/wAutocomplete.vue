<script setup lang="ts">
import { ref, watch } from "vue";

// Define props with type safety
const props = withDefaults(
  defineProps<{
    items: any[]; // List of items for the autocomplete
    itemTitle: string; // Field name for display text
    itemValue: string; // Field name for the value
    label: string; // Label for the autocomplete
    loading?: boolean; // Optional loading state
    loadingText?: string; // Optional loading text
    noDataText?: string; // Optional no-data text
    noResultsText?: string; // Optional no-results text
    density?: "default" | "comfortable" | "compact"; // Vuetify density prop
    variant?: "solo" | "outlined" | "filled" | "plain" | "underlined"; // Vuetify variant prop
    prependIcon?: string; // Optional icon to prepend
    appendIcon?: string; // Optional icon to append
    modelValue?: any; // Selected value (v-model)
    search?: string; // Search input (v-model:search)
  }>(),
  {
    items: () => [],
    itemTitle: "description",
    itemValue: "id",
    label: "Select an option",
    loading: false,
    loadingText: "Loading...",
    noDataText: "No data available",
    noResultsText: "No results found",
    density: "compact",
    variant: "outlined",
    prependIcon: undefined,
    appendIcon: undefined,
    modelValue: undefined,
    search: "",
  }
);

// Define emits for v-model and search updates
const emit = defineEmits<{
  (e: "update:modelValue", value: any): void;
  (e: "update:search", value: string): void;
}>();

// Reactive state for v-model and search
const selectedValue = ref(props.modelValue);
const searchText = ref(props.search);

// Watch for prop changes and sync internal state
watch(
  () => props.modelValue,
  (newValue) => {
    selectedValue.value = newValue;
  }
);

watch(
  () => props.search,
  (newValue) => {
    searchText.value = newValue;
  }
);

// Watch for internal state changes and emit updates
watch(selectedValue, (newValue) => {
  emit("update:modelValue", newValue);
});

watch(searchText, (newValue) => {
  emit("update:search", newValue);
});
</script>

<template>
  <v-autocomplete
    v-model="selectedValue"
    v-model:search="searchText"
    :items="props.items"
    :item-title="props.itemTitle"
    :item-value="props.itemValue"
    :label="props.label"
    :density="props.density"
    :variant="props.variant"
    :loading="props.loading"
    :loading-text="props.loadingText"
    :no-data-text="props.noDataText"
    :no-results-text="props.noResultsText"
    :prepend-icon="props.prependIcon"
    :append-icon="props.appendIcon"
    @update:modelValue="selectedValue = $event"
    @update:search="searchText = $event"
  ></v-autocomplete>
</template>

<script setup lang="ts">
export default {
  name: "wAutocomplete",
};
</script>
