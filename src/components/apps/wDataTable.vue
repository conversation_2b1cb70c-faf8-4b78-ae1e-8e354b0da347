<script setup lang="ts">
import { ref, computed, watch, defineEmits } from "vue";
import { menuPermisos } from "@/stores/Permisos";

const props = withDefaults(
  defineProps<{
    loading: boolean;
    dialogWidth: string;
    usarDialogoInterno?: boolean;
    title: string;
    headers: any[];
    items: any[];
    pageNumber: number;
    pageSize: number;
    totalPages: number;
    totalRecords: number;
    add: boolean;
    eliminar?: boolean;
    imprimir?: boolean;
    valid?: boolean;
    panel?: boolean | null; // nueva prop opcional para controlar el panel
    editoradd?: boolean | null; // nueva prop opcional para controlar el panel de edición
    customActions?: { icon: string; link: string; tooltip: string }[]; // Nueva prop para botones de acción personalizados
  }>(),
  {
    loading: false,
    valid: true,
    eliminar: false,
    imprimir: false,
    dialogWidth: "800px",
    pageNumber: 1,
    pageSize: 10,
    totalPages: 0,
    totalRecords: 0,
    usarDialogoInterno: true, // ⬅️ AQUÍ es donde se asigna el valor por defecto
    panel: false,
    editoradd: false,
    customActions: () => [], // Valor por defecto para customActions
  }
);

const defaultPermisos = {
  ver: false,
  crear: false,
  editar: false,
  eliminar: false,
  imprimir: false,
};
const permiso = computed(
  () => menuPermisos.getPermisosOpcionMenu() || defaultPermisos
);

// Props y Emits
const emit = defineEmits<{
  (e: "editItem", item: any): void;
  (e: "update"): void;
  (e: "delete", item: any): void;
  (e: "saveDone"): void;
  (e: "newItem"): void;
  (e: "save"): void;
  (e: "goTo", pageNumber: number, pageSize: number): void;
  (e: "update:add", value: boolean): void;
  (e: "update:editoradd", value: boolean): void;
}>();

const edit = ref(false);
const dialog = ref(false);
const dialogEliminar = ref(false);
const formValid = ref(true); // Corrección: Definir valid para v-form
const itemToDelete = ref(null);
const panelActive = ref(false);

watch(
  () => props.add,
  (newValue) => {
    if (newValue === true) {
      edit.value = false;
      dialog.value = true;
    } else {
      dialog.value = false;
      edit.value = false;
    }
  },
  { immediate: true }
);

// Watch for prop changes if needed
watch(
  () => props.editoradd,
  (newValue) => {
    newValue;
  }
);
// Computed properties
const displayedPages = computed(() => {
  const maxPagesToShow = 5; // Número máximo de botones de página a mostrar
  const halfPages = Math.floor(maxPagesToShow / 2);
  let startPage = Math.max(1, props.pageNumber - halfPages);
  let endPage = Math.min(props.totalPages, startPage + maxPagesToShow - 1);

  // Ajustar si estamos cerca del final
  if (endPage - startPage + 1 < maxPagesToShow) {
    startPage = Math.max(1, endPage - maxPagesToShow + 1);
  }

  const pages = [];
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }
  return pages;
});

const showEllipsisAfter = computed(() => {
  return (
    displayedPages.value[displayedPages.value.length - 1] < props.totalPages
  );
});

// Pagination
const pagination = ref(props.pageNumber);
const totalPagesPagination = ref(props.totalPages);

// Form title
const formTitle = computed(
  () => `${edit.value == true ? "Editar" : "Nuevo"} ${props.title}`
);

// Métodos
function editItem(item: any) {
 //edit.value = true;
 // dialog.value = true;
  emit("editItem", item);
    if (props.usarDialogoInterno) {
    edit.value = true;
    dialog.value = true;
  }
}

function confirmDialog(item: any) {
  dialogEliminar.value = true;
  itemToDelete.value = item;
}

async function deleteItem() {
  emit("delete", itemToDelete.value);
  close();
}

function close() {
  emit("update:add", false);
  emit("update:editoradd", false);
  dialog.value = false;
  dialogEliminar.value = false;
  edit.value = false;
}

async function update() {
  emit("update");
  close();
}

function save() {
  emit("save");
  close();
}

function nextPage() {
  totalPagesPagination.value = props.totalPages;
  if (pagination.value < props.totalPages && props.pageSize > 0) {
    pagination.value++;
    emit("goTo", pagination.value, props.pageSize);
  }
}

function previousPage() {
  if (pagination.value > 1) {
    pagination.value--;
    emit("goTo", pagination.value, props.pageSize);
  }
}

function firstPage() {
  pagination.value = 1;
  emit("goTo", pagination.value, props.pageSize);
}

function lastPage() {
  pagination.value = props.totalPages;
  emit("goTo", pagination.value, props.pageSize);
}

function irA(pagina: number) {
  pagination.value = pagina;
  emit("goTo", pagination.value, props.pageSize);
}

function isNestedProperty(key: string) {
  return key.includes(".");
}
// Access nested property dynamically
function getNestedProperty(item: Record<string, any>, key: string): any {
  try {
    return key.split(".").reduce((obj, k) => obj[k], item) ?? "N/A";
  } catch (e) {
    return "N/A"; // Fallback if the nested property doesn't exist
  }
}

//const inputPage = ref(props.pageNumber);
</script>

<script lang="ts">
export default {
  name: "wDataTable",
};
</script>

<template>
  <v-row>
    <v-col cols="12" lg="8" md="6" class="text-right">
      <v-dialog
        v-model="dialog"
        :max-width="dialogWidth"
        persistent
        v-if="!panel"
      >
        <v-card>
          <v-card-title class="pa-4 bg-primary">
            <v-icon>mdi-database-edit-outline</v-icon> &nbsp;
            <span class="title text-white">{{ formTitle }}</span>
          </v-card-title>

          <v-card-text>
            <v-form ref="form" v-model="formValid" lazy-validation>
              <v-row>
                <slot name="editItem" />
              </v-row>
            </v-form>
          </v-card-text>
          <v-card-text class="pt-0 pb-0">
            <v-divider class="pb-0" color="primary"></v-divider
          ></v-card-text>
          <v-card-actions class="pa-4">
            <v-btn color="error" variant="flat" @click="close"
              ><v-icon>mdi-arrow-u-left-top</v-icon> Volver</v-btn
            >
            <v-btn
              color="primary"
              variant="flat"
              :disabled="!valid"
              :loading="loading"
              @click="edit ? update() : save()"
            >
              <v-icon>mdi-check-circle</v-icon> Aceptar
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-dialog v-model="dialogEliminar" max-width="400px" persistent>
        <v-card>
          <v-card-title class="pa-4 bg-warning">
            <v-icon>mdi-alert</v-icon> &nbsp;
            <span class="title text-white">{{ "¿Estás seguro?" }}</span>
          </v-card-title>

          <v-card-text>
            <v-row>
              <v-col cols="12" align-center>
                <span>Se eliminará este registro</span>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text class="pt-0 pb-0">
            <v-divider class="pb-0" color="primary"></v-divider
          ></v-card-text>
          <v-card-actions class="pa-4">
            <v-btn color="error" variant="flat" @click="close"
              ><v-icon>mdi-arrow-u-left-top</v-icon> Volver</v-btn
            >
            <v-btn
              color="primary"
              variant="flat"
              :loading="loading"
              @click="deleteItem"
            >
              <v-icon>mdi-check-circle</v-icon> Aceptar
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-col>
  </v-row>
  <v-row v-if="panel && dialog">
    <v-card flat>
      <v-card-text>
        <v-form ref="form" v-model="formValid" lazy-validation>
          <v-row>
            <slot name="editItem" />
          </v-row>
        </v-form>
      </v-card-text>
      <v-card-actions class="pa-4">
        <v-spacer></v-spacer>
        <v-btn color="error" variant="flat" @click="close"
          ><v-icon>mdi-arrow-u-left-top</v-icon> Volver</v-btn
        >
        <v-btn
          color="primary"
          variant="flat"
          :loading="loading"
          @click="edit ? update() : save()"
        >
          <v-icon>mdi-check-circle</v-icon> Aceptar
        </v-btn>
        <v-spacer></v-spacer>
      </v-card-actions>
    </v-card>
  </v-row>
  <v-row v-if="(panel && dialog === false) || !panel">
    <v-col cols="12">
      <v-data-table
        color="primary"
        :headers="headers"
        :items="items"
        hide-default-footer
        dense
        class="border rounded-sm"
      >
        <template v-slot:no-data>
          <v-alert type="error" variant="outlined">Datos no encontrados</v-alert>
        </template>
        <template v-slot:loading>
          <v-progress-linear indeterminate />
        </template>
        <template v-slot:item="{ item }">
          <tr>
            <td v-for="header in headers" :key="header.key">
              <!-- Handle nested properties -->
              <div v-if="header.type === 'image'">
                <v-avatar size="56px" color="primary" class="data-table-avatar">
                  <v-img v-if="item[header.key]" :src="item[header.key]" alt="Foto de Usuario"></v-img>
                  <div v-else class="data-table-initials-container">
                    <span class="data-table-initials">{{ item.nombre?.charAt(0) }}{{ item.apellido?.charAt(0) }}</span>
                  </div>
                </v-avatar>
              </div>
              <!-- Handle nested properties -->
              <div v-else-if="isNestedProperty(header.key)">
                {{ getNestedProperty(item, header.key) }}
              </div>
              <!-- Handle string values -->
              <div v-else-if="typeof item[header.key] === 'string'">
                {{ item[header.key] }}
              </div>
              <!-- Handle number values -->
              <div
                v-else-if="typeof item[header.key] === 'number'"
                class="d-flex align-center"
              >
                {{ item[header.key] }}
              </div>
              <!-- Handle boolean values -->
              <div
                class="d-flex align-center"
                v-else-if="typeof item[header.key] === 'boolean'"
              >
                <v-checkbox-btn
                  readonly
                  color="primary"
                  hide-details
                  v-model="item[header.key]"
                />
              </div>
              <!-- Handle actions column -->
              <div
                v-else-if="header.key === 'acciones'"
                class="d-flex align-center"
              >
                <v-tooltip text="Editar" v-if="permiso.editar">
                  <template v-slot:activator="{ props }">
                    <span v-bind="props">
                      <v-btn
                        icon
                        flat
                        @click="editItem(item)"
                        aria-label="Editar"
                      >
                        <v-icon color="primary">mdi-pencil</v-icon>
                      </v-btn>
                    </span>
                  </template>
                </v-tooltip>

                <v-tooltip text="Imprimir" v-if="permiso.imprimir && imprimir">
                  <template v-slot:activator="{ props }">
                    <span v-bind="props">
                      <v-btn
                        icon
                        flat
                        @click="editItem(item)"
                        aria-label="Imprimir"
                      >
                        <v-icon color="secondary">mdi-printer</v-icon>
                      </v-btn>
                    </span>
                  </template>
                </v-tooltip>

                <v-tooltip text="Eliminar" v-if="permiso.eliminar && eliminar">
                  <template v-slot:activator="{ props }">
                    <span v-bind="props">
                      <v-btn
                        icon
                        flat
                        @click="confirmDialog(item)"
                        aria-label="Eliminar"
                      >
                        <v-icon color="error">mdi-delete</v-icon>
                      </v-btn>
                    </span>
                  </template>
                </v-tooltip>
                <slot name="moreActions" :item="item" />
                <v-tooltip
                  v-for="(action, index) in props.customActions"
                  :key="`custom-action-${index}`"
                  :text="action.tooltip"
                >
                  <template v-slot:activator="{ props }">
                    <span v-bind="props">
                      <v-btn
                        icon
                        flat
                        :to="`${action.link}/${item.id}`"
                        :aria-label="action.tooltip"
                      >
                        <v-icon>{{ action.icon }}</v-icon>
                      </v-btn>
                    </span>
                  </template>
                </v-tooltip>
              </div>
              <!-- Fallback for unhandled types -->
              <div v-else>
                {{ item[header.key] }}
              </div>
            </td>
          </tr>
        </template>
      </v-data-table>
      <div
        v-if="totalRecords > 0"
        class="d-flex justify-space-between align-center mt-4 px-2 flex-wrap"
      >
        <div class="pagination-info">
          <v-card color="primary" dark density="compact" class="pa-2">
            <v-card-text class="pa-2">
              <v-row align="center" dense>
                <!-- Total de Registros -->
                <v-col cols="12" class="text-caption d-flex align-center">
                  <v-icon small>mdi-database</v-icon>
                  <span class="font-weight-bold">Total de registros:</span>
                  {{ totalRecords.toLocaleString() }}
                </v-col>

                <!-- Información de Página -->
                <v-col cols="12" class="text-caption d-flex align-center">
                  <v-icon small class="mr-1">mdi-page-layout-body</v-icon>
                  <span class="font-weight-bold">Página:</span>

                  <span>
                    {{ pageNumber }} de {{ totalPages.toLocaleString() }}</span
                  >
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </div>

        <div>
          <v-row justify="center" align="center">
            <!-- Botón Primera Página -->
            <v-btn
              icon
              @click="firstPage"
              :readonly="pageNumber === 1"
              :color="pageNumber === 1 ? 'grey' : ''"
            >
              <v-icon :disabled="pageNumber === 1">mdi-page-first</v-icon>
            </v-btn>

            <!-- Botón Página Anterior -->
            <v-btn
              icon
              @click="previousPage"
              :readonly="pageNumber === 1"
              :color="pageNumber === 1 ? 'grey' : ''"
            >
              <v-icon :disabled="pageNumber === 1">mdi-chevron-left</v-icon>
            </v-btn>

            <!-- Botones de Paginación Dinámica -->
            <v-btn
              v-for="pagina in displayedPages"
              :key="pagina"
              icon
              @click="irA(pagina)"
              :color="pagina === pageNumber ? 'primary' : ''"
            >
              {{ pagina }}
            </v-btn>

            <!-- Indicador de Puntos Suspensivos (si aplica) -->
            <v-btn v-if="showEllipsisAfter" icon readonly class="ellipsis">
              ...
            </v-btn>

            <!-- Botón Página Siguiente -->
            <v-btn
              icon
              @click="nextPage"
              :readonly="pageNumber === totalPages"
              :color="pageNumber === totalPages ? 'grey' : ''"
            >
              <v-icon :disabled="pageNumber === totalPages"
                >mdi-chevron-right</v-icon
              >
            </v-btn>

            <!-- Botón Última Página -->
            <v-btn
              icon
              @click="lastPage"
              :readonly="pageNumber === totalPages"
              :color="pageNumber === totalPages ? 'grey' : ''"
            >
              <v-icon :disabled="pageNumber === totalPages"
                >mdi-page-last</v-icon
              >
            </v-btn>
          </v-row>
        </div>
      </div>
    </v-col>
  </v-row>
</template>

<style scoped>
.data-table-avatar {
  border: 3px solid #e0e0e0;
  transition: border-color 0.3s ease;
}

.data-table-avatar:hover {
  border-color: #1976d2; /* Vuetify primary color */
}

.data-table-initials-container {
  width: 100%;
  height: 100%;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.data-table-initials {
  font-size: 24px; /* Adjusted for smaller avatar size in data table */
  font-weight: bold;
  color: #003d9a;
  text-transform: uppercase;
}
</style>
