<!-- src/components/CustomAlertDialog.vue -->
<template>
  <v-dialog
    v-model="visible"
    :max-width="computedWidth"
    persistent
  >
    <v-card>
      <v-card-title :class="['d-flex', 'align-center', headerClasses]" style="gap: 12px;">
        <v-icon size="26" :color="iconColor" class="mr-2">
          {{ icon }}
        </v-icon>
        <span class="dialog-title">{{ titleText }}</span>
      </v-card-title>
      <v-divider />
      <v-card-text class="dialog-message">
        <slot>
          {{ message }}
        </slot>
      </v-card-text>
      <v-card-actions>
        <v-spacer />
        <v-btn color="primary" flat @click="visible = false">
          Cerrar
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, defineProps, defineEmits } from 'vue'

const props = defineProps<{
  modelValue: boolean
  message: string
  type: 'success' | 'warning' | 'error'
  maxWidth?: number | string
  title?: string
}>()

const emit = defineEmits<{ (event: 'update:modelValue', value: boolean): void }>()

const visible = ref(props.modelValue)
watch(() => props.modelValue, v => visible.value = v)
watch(visible, v => emit('update:modelValue', v))

// Títulos e íconos según el tipo
const typeInfo = {
  success: { title: 'Éxito', icon: 'mdi-check-circle', color: 'success' },
  error: { title: 'Error', icon: 'mdi-alert-circle', color: 'error' },
  warning: { title: 'Advertencia', icon: 'mdi-alert', color: 'warning' }
}

const titleText = computed(() => props.title || typeInfo[props.type].title)
const icon = computed(() => typeInfo[props.type].icon)
const iconColor = computed(() => typeInfo[props.type].color)

const headerClasses = computed(() => {
  return [
    `bg-${props.type}`,
    props.type === 'warning' ? 'text-dark' : 'text-white'
  ]
})

const computedWidth = computed(() => props.maxWidth ?? 400)
</script>

<style scoped>
.dialog-title {
  font-weight: bold;
  font-size: 1.18rem;
}
.dialog-message {
  font-size: 1.05rem;
  text-align: center;
  max-width: 340px;
  margin: 0 auto;
  padding: 18px 0 10px 0;
  word-break: break-word;
}
.v-card {
  border-radius: 10px;
}
.v-card-title {
  min-height: 48px;
}
</style>
