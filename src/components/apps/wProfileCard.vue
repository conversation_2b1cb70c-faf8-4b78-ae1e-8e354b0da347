<template>
  <v-row justify="center">
    <v-col cols="12">
      <v-card class="profile-card" elevation="4" rounded="lg">
        <!-- Profile Picture or Silhouette -->
        <div class="avatar-container">
          <v-avatar size="120" class="profile-avatar">
            <v-img
              v-if="!imageError && profilePicture"
              :src="imageSrc"
              alt="{{ firstName }} {{ lastName }}"
              title=""
              cover
              @error="handleImageError"
            ></v-img>
            <div v-else class="silhouette-container">
              <span class="initials text-primary">{{ initials }}</span>
            </div>
          </v-avatar>
        </div>

        <!-- Profile Details -->
        <v-card-text class="text-center pt-2">

          <!-- Name with Verification Badge -->
          <div class="d-flex justify-center align-center mb-2">
            <span class="text-h6 font-weight-bold primary-text">
              {{ firstName }} {{ lastName }}
            </span>
          </div>

          <!-- Role/Description -->
          <div class="text-h6 secondary-text mb-4">
            {{ codigo || "N/A" }}
          </div>

          <!-- Additional Info -->
          <v-divider class="mb-4"></v-divider>

          <!-- Career -->
          <div class="info-row mb-2">
            <v-icon color="primary" size="20" class="mr-2">{{
              iconIdentificacion
            }}</v-icon>
            <span class="ml-1 text-body-2 secondary-text">{{
              identificacion || "N/A"
            }}</span>
          </div>

          <!-- Email -->
          <div class="info-row mb-2">
            <v-icon color="primary" size="20" class="mr-2">mdi-email</v-icon>
            <span class="ml-1 text-body-2 secondary-text">{{
              email || "N/A"
            }}</span>
          </div>
          <!-- Status -->
          <div class="info-row">
            <v-icon color="primary" size="20" class="mr-2">mdi-circle</v-icon>
            <span class="font-weight-medium text-body-2">Estado:</span>
            <v-chip
              :color="activo ? 'success' : 'error'"
              size="x-small"
              class="ml-1"
              variant="flat"
            >
              {{ status || "N/A" }}
            </v-chip>
          </div>
        </v-card-text>
      </v-card>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from "vue";

export default defineComponent({
  name: "wProfileCard",
  props: {
    firstName: {
      type: String,
      required: true,
    },
    lastName: {
      type: String,
      required: true,
    },
    // Código o matrícula
    codigo: {
      type: String,
      default: "N/A",
    },

    //Identificación del usuario o carrera o perfil ETC
    identificacion: {
      type: String,
      default: "N/A",
    },
    //Icono que identifica
    iconIdentificacion: {
      type: String,
      default: "mdi-school",
    },
    //Correo electrónico
    email: {
      type: String,
      default: "N/A",
    },
    //lable del estado
    status: {
      type: String,
      default: "N/A",
    },
    // Active status
    activo: {
      type: Boolean,
      default: false,
    },
    // Optional profile picture URL
    profilePicture: {
      type: String,
      default: null,
    },
  },
  setup(props) {
    const imageError = ref(false);
    const isFollowing = ref(false);

    // Computed property for image source with fallback
    const imageSrc = computed(() => {
      return props.profilePicture;
    });

    // Compute initials from firstName and lastName
    const initials = computed(() => {
      const firstInitial = props.firstName
        ? props.firstName.charAt(0).toUpperCase()
        : "";
      const lastInitial = props.lastName
        ? props.lastName.charAt(0).toUpperCase()
        : "";
      return `${firstInitial}${lastInitial}`;
    });

    // Handle image loading error
    const handleImageError = () => {
      imageError.value = true;
    };

    // Toggle follow state
    const toggleFollow = () => {
      isFollowing.value = !isFollowing.value;
    };

    return {
      imageSrc,
      handleImageError,
      isFollowing,
      toggleFollow,
      initials,
      imageError,
    };
  },
});
</script>

<style scoped>
.profile-card {
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
  border: 2px solid rgba(0, 0, 0, 0.05);
}

.profile-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.avatar-container {
  display: flex;
  justify-content: center;
  padding-top: 16px;
}

.profile-avatar {
  border: 3px solid #e0e0e0;
  transition: border-color 0.3s ease;
}

.profile-avatar:hover {
  border-color: #1976d2; /* Vuetify primary color */
}

.silhouette-container {
  width: 100%;
  height: 100%;

  background-size: 60%;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.initials {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  text-transform: uppercase;
}

.primary-text {
  color: #1a1a1a;
  font-family: "Inter", sans-serif;
  letter-spacing: -0.01em;
}

.secondary-text {
  color: #666;
  font-family: "Inter", sans-serif;
}

.stat-item {
  display: flex;
  align-items: center;
  color: #666;
}

.follow-btn {
  text-transform: none;
  font-weight: 500;
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .profile-card {
    padding: 12px;
  }
  .profile-avatar {
    size: 100px;
  }
  .text-h6 {
    font-size: 1.1rem;
  }
  .initials {
    font-size: 1.5rem;
  }
}
</style>
