<template>
  <v-list-item :class="getOptionClass(option.nivel)" class="rounded-list-item">
    <v-list-item-content class="py-2">
      <v-row align="center">
        <v-col cols="auto">
          <v-icon v-if="option.icon">{{ option.icon }}</v-icon>
        </v-col>
        <v-col>
          <v-list-item-title class="option-title" :class="{ 'expanded-title': showSubOptions }">{{ option.descripcion }}</v-list-item-title>
        </v-col>
        <v-col cols="auto">
          <v-checkbox
            v-model="option.permisos.ver"
            label="Ver"
            density="compact"
            hide-details
            @update:modelValue="handlePermissionChange('ver')"
            :class="getCheckboxClass(option.nivel)"
          ></v-checkbox>
        </v-col>
        <v-col cols="auto">
          <v-checkbox
            v-model="option.permisos.crear"
            label="Crear"
            density="compact"
            hide-details
            @update:modelValue="handlePermissionChange('crear')"
            :class="getCheckboxClass(option.nivel)"
          ></v-checkbox>
        </v-col>
        <v-col cols="auto">
          <v-checkbox
            v-model="option.permisos.editar"
            label="Editar"
            density="compact"
            hide-details
            @update:modelValue="handlePermissionChange('editar')"
            :class="getCheckboxClass(option.nivel)"
          ></v-checkbox>
        </v-col>
        <v-col cols="auto">
          <v-checkbox
            v-model="option.permisos.eliminar"
            label="Eliminar"
            density="compact"
            hide-details
            @update:modelValue="handlePermissionChange('eliminar')"
            :class="getCheckboxClass(option.nivel)"
          ></v-checkbox>
        </v-col>
        <v-col cols="auto">
          <v-checkbox
            v-model="option.permisos.imprimir"
            label="Imprimir"
            density="compact"
            hide-details
            @update:modelValue="handlePermissionChange('imprimir')"
            :class="getCheckboxClass(option.nivel)"
          ></v-checkbox>
        </v-col>
        <v-col cols="auto" v-if="option.subOpciones && option.subOpciones.length > 0">
          <v-btn icon variant="text" @click="toggleSubOptions">
            <v-icon>{{ showSubOptions ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
          </v-btn>
        </v-col>
      </v-row>
    </v-list-item-content>
  </v-list-item>

  <v-list v-if="showSubOptions && option.subOpciones && option.subOpciones.length > 0" class="ml-8">
    <PermissionOption
      v-for="subOption in option.subOpciones"
      :key="subOption.id"
      :option="subOption"
      @update:permission="handleSubOptionUpdate"
    />
  </v-list>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import type { OpcionAplicacion, Permisos } from '@/utils/OpcionAplicacion';
import OpcionAplicacionService from '@/services/Seguridad/OpcionAplicacionService';

const props = defineProps<{
  option: OpcionAplicacion;
}>();

const emit = defineEmits(['update:permission']);

const showSubOptions = ref(false);

const toggleSubOptions = () => {
  showSubOptions.value = !showSubOptions.value;
};

const handlePermissionChange = async (permissionType: keyof Permisos) => {
  // Actualizar sub-opciones primero
  updateSubOptions(permissionType);

  // Guardar los cambios en el servidor
  await savePermissionChanges();
};

const updateSubOptions = (permissionType: keyof Permisos) => {
  if (props.option.subOpciones) {
    props.option.subOpciones.forEach(subOption => {
      subOption.permisos[permissionType] = props.option.permisos[permissionType];
      // Recursively update grandchildren
      if (subOption.subOpciones) {
        updateChildrenPermissions(subOption.subOpciones, permissionType, props.option.permisos[permissionType]);
      }
    });
  }
  emit('update:permission');
};

const savePermissionChanges = async () => {
  try {
    await OpcionAplicacionService.saveOpcionAplicacion(props.option);
    console.log('Permisos guardados exitosamente para:', props.option.descripcion);
  } catch (error) {
    console.error('Error al guardar permisos:', error);
    // Aquí podrías agregar una notificación de error al usuario
  }
};

const updateChildrenPermissions = (children: OpcionAplicacion[], permissionType: keyof Permisos, value: boolean) => {
  children.forEach(child => {
    child.permisos[permissionType] = value;
    if (child.subOpciones) {
      updateChildrenPermissions(child.subOpciones, permissionType, value);
    }
  });
};

const handleSubOptionUpdate = async () => {
  // Check if all sub-options for a given permission type are checked
  const allSubOptionsChecked = (permissionType: keyof Permisos) => {
    return props.option.subOpciones?.every(subOption => subOption.permisos[permissionType]) ?? false;
  };

  // Update parent permissions based on sub-options
  if (props.option.subOpciones) {
    props.option.permisos.ver = allSubOptionsChecked('ver');
    props.option.permisos.crear = allSubOptionsChecked('crear');
    props.option.permisos.editar = allSubOptionsChecked('editar');
    props.option.permisos.eliminar = allSubOptionsChecked('eliminar');
    props.option.permisos.imprimir = allSubOptionsChecked('imprimir');
  }

  // Guardar los cambios en el servidor
  await savePermissionChanges();

  emit('update:permission');
};

watch(() => props.option.permisos, () => {
  emit('update:permission');
}, { deep: true });

const getOptionClass = (nivel: number) => {
  switch (nivel) {
    case 1:
      return 'parent-option';
    case 2:
      return 'child-option';
    case 3:
      return 'grandchild-option';
    case 4:
      return 'great-grandchild-option';
    default:
      return '';
  }
};

const getCheckboxClass = (nivel: number) => {
  return `checkbox-level-${nivel}`;
};
</script>

<style scoped>
.rounded-list-item {
  border-radius: 8px; /* Rounded corners for list items */
  margin-bottom: 5px; /* Space between items */
  overflow: hidden; /* Ensures content respects border-radius */
}

.parent-option {
  background-color: #e3f2fd; /* Light blue for parents */
  font-weight: bold;
  border-left: 5px solid #2196F3; /* Deeper blue border */
}

.child-option {
  background-color: #e8f5e9; /* Light green for children */
  margin-left: 20px; /* Indent children */
  border-left: 3px solid #4CAF50; /* Green border */
}

.grandchild-option {
  background-color: #fffde7; /* Light yellow for grandchildren */
  margin-left: 40px; /* Indent grandchildren further */
  border-left: 2px solid #FFEB3B; /* Yellow border */
}

.great-grandchild-option {
  background-color: #f3e5f5; /* Light purple for great-grandchildren */
  margin-left: 60px; /* Indent even further */
  border-left: 1px solid #9C27B0; /* Purple border */
}

.option-title {
  font-size: 1em; /* Increased from 0.8em to 0.9em */
}

.expanded-title {
  font-weight: bold !important;
}

.checkbox-level-1 .v-label,
.checkbox-level-2 .v-label,
.checkbox-level-3 .v-label,
.checkbox-level-4 .v-label {
  font-size: 0.8em; /* Base font size for all checkbox labels */
}

.checkbox-level-1 .v-label {
  color: #2196F3 !important; /* Blue for level 1 checkboxes */
}
.checkbox-level-1 .v-icon {
  color: #2196F3 !important;
}

.checkbox-level-2 .v-label {
  color: #4CAF50 !important; /* Green for level 2 checkboxes */
}
.checkbox-level-2 .v-icon {
  color: #4CAF50 !important;
}

.checkbox-level-3 .v-label {
  color: #FFEB3B !important; /* Yellow for level 3 checkboxes */
}
.checkbox-level-3 .v-icon {
  color: #FFEB3B !important;
}

.checkbox-level-4 .v-label {
  color: #9C27B0 !important; /* Purple for level 4 checkboxes */
}
.checkbox-level-4 .v-icon {
  color: #9C27B0 !important;
}
</style>