<template>
  <v-list-item :class="getOptionClass(option.nivel)" class="rounded-list-item">
    <v-list-item-content class="py-2">
      <v-row align="center">
        <v-col cols="auto">
          <v-icon v-if="option.icon">{{ option.icon }}</v-icon>
        </v-col>
        <v-col>
          <div class="d-flex align-center gap-2">
            <v-list-item-title class="option-title" :class="{ 'expanded-title': showSubOptions }">{{ option.descripcion }}</v-list-item-title>
            <v-progress-circular
              v-if="isLoading"
              indeterminate
              size="16"
              width="2"
              color="primary"
            ></v-progress-circular>
          </div>
        </v-col>
        <v-col cols="auto">
          <v-checkbox
            v-model="option.permisos.ver"
            label="Ver"
            density="compact"
            hide-details
            @update:modelValue="handlePermissionChange('ver')"
            :class="getCheckboxClass(option.nivel)"
          ></v-checkbox>
        </v-col>
        <v-col cols="auto">
          <v-checkbox
            v-model="option.permisos.crear"
            label="Crear"
            density="compact"
            hide-details
            @update:modelValue="handlePermissionChange('crear')"
            :class="getCheckboxClass(option.nivel)"
          ></v-checkbox>
        </v-col>
        <v-col cols="auto">
          <v-checkbox
            v-model="option.permisos.editar"
            label="Editar"
            density="compact"
            hide-details
            @update:modelValue="handlePermissionChange('editar')"
            :class="getCheckboxClass(option.nivel)"
          ></v-checkbox>
        </v-col>
        <v-col cols="auto">
          <v-checkbox
            v-model="option.permisos.eliminar"
            label="Eliminar"
            density="compact"
            hide-details
            @update:modelValue="handlePermissionChange('eliminar')"
            :class="getCheckboxClass(option.nivel)"
          ></v-checkbox>
        </v-col>
        <v-col cols="auto">
          <v-checkbox
            v-model="option.permisos.imprimir"
            label="Imprimir"
            density="compact"
            hide-details
            @update:modelValue="handlePermissionChange('imprimir')"
            :class="getCheckboxClass(option.nivel)"
          ></v-checkbox>
        </v-col>
        <v-col cols="auto" v-if="option.subOpciones && option.subOpciones.length > 0">
          <v-btn icon variant="text" @click="toggleSubOptions">
            <v-icon>{{ showSubOptions ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
          </v-btn>
        </v-col>
      </v-row>
    </v-list-item-content>
  </v-list-item>

  <v-list v-if="showSubOptions && option.subOpciones && option.subOpciones.length > 0" class="ml-8">
    <PermissionOption
      v-for="subOption in option.subOpciones"
      :key="subOption.id"
      :option="subOption"
      :rolId="rolId"
      @update:permission="handleSubOptionUpdate"
    />
  </v-list>
</template>

<script setup lang="ts">
import { ref, watch, inject } from 'vue';
import type { OpcionAplicacion, Permisos } from '@/utils/OpcionAplicacion';
import OpcionAplicacionService from '@/services/Seguridad/OpcionAplicacionService';
import { useNotifications } from '@/composables/useNotifications';

const props = defineProps<{
  option: OpcionAplicacion;
  rolId?: number;
}>();

const emit = defineEmits(['update:permission']);

const showSubOptions = ref(false);
const isLoading = ref(false);

// Inyectar funciones del componente padre para batch operations
const addToBatch = inject<(option: OpcionAplicacion) => void>('addToBatch');
const { showSuccess, showError } = useNotifications();

const toggleSubOptions = () => {
  showSubOptions.value = !showSubOptions.value;
};

const handlePermissionChange = async (permissionType: keyof Permisos) => {
  // Actualizar sub-opciones primero (ahora es asíncrono)
  await updateSubOptions(permissionType);

  // Agregar la opción principal a batch operations si está disponible, sino guardar directamente
  if (addToBatch) {
    addToBatch(props.option);
  } else {
    await savePermissionChanges();
  }
};

const updateSubOptions = async (permissionType: keyof Permisos) => {
  if (props.option.subOpciones) {
    // Recopilar todas las opciones que necesitan ser guardadas
    const optionsToSave: OpcionAplicacion[] = [];

    props.option.subOpciones.forEach(subOption => {
      subOption.permisos[permissionType] = props.option.permisos[permissionType];
      optionsToSave.push(subOption);

      // Recursively update grandchildren
      if (subOption.subOpciones) {
        const grandChildren = updateChildrenPermissions(subOption.subOpciones, permissionType, props.option.permisos[permissionType]);
        optionsToSave.push(...grandChildren);
      }
    });

    // Guardar todas las opciones actualizadas
    if (addToBatch) {
      // Agregar todas las opciones al batch
      optionsToSave.forEach(option => addToBatch(option));
    } else {
      // Guardar todas las opciones individualmente
      await saveMultipleOptions(optionsToSave);
    }
  }
  emit('update:permission');
};

const savePermissionChanges = async () => {
  if (isLoading.value) return;

  isLoading.value = true;
  try {
    if (props.rolId) {
      await OpcionAplicacionService.saveRolPermiso(props.rolId, props.option);
    } else {
      await OpcionAplicacionService.saveOpcionAplicacion(props.option);
    }
    showSuccess(`Permisos guardados para: ${props.option.descripcion}`);
    console.log('Permisos guardados exitosamente para:', props.option.descripcion);
  } catch (error) {
    console.error('Error al guardar permisos:', error);
    showError(`Error al guardar permisos para: ${props.option.descripcion}`);
  } finally {
    isLoading.value = false;
  }
};

const saveMultipleOptions = async (options: OpcionAplicacion[]) => {
  if (isLoading.value || options.length === 0) return;

  isLoading.value = true;
  try {
    const promises = options.map(option => {
      if (props.rolId) {
        return OpcionAplicacionService.saveRolPermiso(props.rolId, option);
      } else {
        return OpcionAplicacionService.saveOpcionAplicacion(option);
      }
    });

    await Promise.all(promises);
    showSuccess(`Permisos guardados para ${options.length} opciones`);
    console.log('Permisos guardados exitosamente para múltiples opciones:', options.map(o => o.descripcion));
  } catch (error) {
    console.error('Error al guardar múltiples permisos:', error);
    showError(`Error al guardar permisos para múltiples opciones`);
  } finally {
    isLoading.value = false;
  }
};

const updateChildrenPermissions = (children: OpcionAplicacion[], permissionType: keyof Permisos, value: boolean): OpcionAplicacion[] => {
  const updatedOptions: OpcionAplicacion[] = [];

  children.forEach(child => {
    child.permisos[permissionType] = value;
    updatedOptions.push(child);

    if (child.subOpciones) {
      const grandChildren = updateChildrenPermissions(child.subOpciones, permissionType, value);
      updatedOptions.push(...grandChildren);
    }
  });

  return updatedOptions;
};

const handleSubOptionUpdate = async () => {
  // Check if all sub-options for a given permission type are checked
  const allSubOptionsChecked = (permissionType: keyof Permisos) => {
    return props.option.subOpciones?.every(subOption => subOption.permisos[permissionType]) ?? false;
  };

  // Update parent permissions based on sub-options
  if (props.option.subOpciones) {
    props.option.permisos.ver = allSubOptionsChecked('ver');
    props.option.permisos.crear = allSubOptionsChecked('crear');
    props.option.permisos.editar = allSubOptionsChecked('editar');
    props.option.permisos.eliminar = allSubOptionsChecked('eliminar');
    props.option.permisos.imprimir = allSubOptionsChecked('imprimir');
  }

  // Agregar a batch operations si está disponible, sino guardar directamente
  if (addToBatch) {
    addToBatch(props.option);
  } else {
    await savePermissionChanges();
  }

  emit('update:permission');
};

watch(() => props.option.permisos, () => {
  emit('update:permission');
}, { deep: true });

const getOptionClass = (nivel: number) => {
  switch (nivel) {
    case 1:
      return 'parent-option';
    case 2:
      return 'child-option';
    case 3:
      return 'grandchild-option';
    case 4:
      return 'great-grandchild-option';
    default:
      return '';
  }
};

const getCheckboxClass = (nivel: number) => {
  return `checkbox-level-${nivel}`;
};
</script>

<style scoped>
.rounded-list-item {
  border-radius: 8px; /* Rounded corners for list items */
  margin-bottom: 5px; /* Space between items */
  overflow: hidden; /* Ensures content respects border-radius */
}

.parent-option {
  background-color: #e3f2fd; /* Light blue for parents */
  font-weight: bold;
  border-left: 5px solid #2196F3; /* Deeper blue border */
}

.child-option {
  background-color: #e8f5e9; /* Light green for children */
  margin-left: 20px; /* Indent children */
  border-left: 3px solid #4CAF50; /* Green border */
}

.grandchild-option {
  background-color: #fffde7; /* Light yellow for grandchildren */
  margin-left: 40px; /* Indent grandchildren further */
  border-left: 2px solid #FFEB3B; /* Yellow border */
}

.great-grandchild-option {
  background-color: #f3e5f5; /* Light purple for great-grandchildren */
  margin-left: 60px; /* Indent even further */
  border-left: 1px solid #9C27B0; /* Purple border */
}

.option-title {
  font-size: 1em; /* Increased from 0.8em to 0.9em */
}

.expanded-title {
  font-weight: bold !important;
}

.checkbox-level-1 .v-label,
.checkbox-level-2 .v-label,
.checkbox-level-3 .v-label,
.checkbox-level-4 .v-label {
  font-size: 0.8em; /* Base font size for all checkbox labels */
}

.checkbox-level-1 .v-label {
  color: #2196F3 !important; /* Blue for level 1 checkboxes */
}
.checkbox-level-1 .v-icon {
  color: #2196F3 !important;
}

.checkbox-level-2 .v-label {
  color: #4CAF50 !important; /* Green for level 2 checkboxes */
}
.checkbox-level-2 .v-icon {
  color: #4CAF50 !important;
}

.checkbox-level-3 .v-label {
  color: #FFEB3B !important; /* Yellow for level 3 checkboxes */
}
.checkbox-level-3 .v-icon {
  color: #FFEB3B !important;
}

.checkbox-level-4 .v-label {
  color: #9C27B0 !important; /* Purple for level 4 checkboxes */
}
.checkbox-level-4 .v-icon {
  color: #9C27B0 !important;
}
</style>