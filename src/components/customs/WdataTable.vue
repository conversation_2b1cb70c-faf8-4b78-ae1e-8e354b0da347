<!-- src/components/shared/WDataTable.vue -->
<template>
    <v-data-table
      :headers="headers"
      :items="items"
      :loading="loading"
      class="rounded-md elevation-1"
    >
      <template v-slot:item.estatus="{ item }">
        <v-checkbox
          v-model="item.estatus"
          hide-details
          readonly
          density="compact"
          color="primary"
          class="ma-0 pa-0"
        />
      </template>
  
      <template v-slot:item.actions="{ item }">
        <div class="d-flex gap-2 align-center">
          <Icon
            v-if="showEdit"
            icon="solar:pen-new-square-broken"
            class="text-primary cursor-pointer"
            height="20"
            @click="$emit('edit', item)"
          />
          <Icon
            v-if="showDelete"
            icon="solar:trash-bin-minimalistic-linear"
            class="text-error cursor-pointer"
            height="20"
            @click="$emit('delete', item)"
          />
        </div>
      </template>
    </v-data-table>
  </template>
  
  <script setup lang="ts">
  import { Icon } from '@iconify/vue';
  
  defineProps<{
    headers: any[],
    items: any[],
    loading?: boolean,
    showEdit?: boolean,
    showDelete?: boolean
  }>();
  </script>
  