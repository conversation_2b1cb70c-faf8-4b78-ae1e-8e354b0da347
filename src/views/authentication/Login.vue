<template class="back">
  <v-container class="login-wrapper">
    <div class="login-container">
      <v-card elevation="12" class="pa-4 rounded-xl login-card">
        <!-- 🔵 Logo libre -->
        <div class="text-center">
          <img
            src="@/assets/images/logos/bnlogo.png"
            alt="Logo"
            class="login-logo"
          />
        </div>

        <!-- 🔵 Acceso con 365 -->
        <v-row justify="center" style="margin: -1px">
          <v-col cols="12" md="8" class="text-center">
            <div class="text-h6 font-weight-medium text-primary mb-3">
              Acceder con:
            </div>

            <v-tour name="officeLoginTour" />

            <v-btn
              class="mx-auto d-flex align-center justify-center"
              id="focus_office"
              color="#F0F0F0"
              density="comfortable"
              height="48"
              style="min-width: 200px; border-radius: 30px"
              @click="onMicrosoftLogin"
            >
              <img
                src="@/assets/images/logos/Microsoft_365_logo.png"
                alt="Microsoft 365"
                width="150"
                height="25"
              />
            </v-btn>
          </v-col>
        </v-row>

        <!-- 🟣 Título -->
        <v-card-title class="text-center" style="margin-bottom: -10px">
          <div class="text-h5 font-weight-bold text-primary">
            Iniciar Sesión
          </div>
          <p class="text-subtitle-2 mt-1 text-grey-darken-1">
            Ingresa tus credenciales para acceder
          </p>
        </v-card-title>

        <!-- 🟢 Formulario -->
        <v-card-text style="padding: 25px 30px">
          <v-form @submit.prevent="onLogin">
            <v-label class="font-weight-medium pb-2">Usuario</v-label>
            <v-text-field
              v-model="username"
              placeholder="Ejemplo: e.rodriguez"
              prepend-inner-icon="mdi-account"
              variant="outlined"
              density="comfortable"
              class="mb-4"
              color="primary"
              hide-details
              required
            />

            <v-label class="font-weight-medium pb-2">Contraseña</v-label>
            <v-text-field
              v-model="password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="Tu contraseña"
              prepend-inner-icon="mdi-lock"
              @click:append-inner="showPassword = !showPassword"
              :append-inner-icon="
                showPassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'
              "
              variant="outlined"
              density="comfortable"
              class="mb-4"
              color="primary"
              hide-details
              required
            />

            <!-- 🔘 Botón -->
            <v-btn
              type="submit"
              color="primary"
              block
              size="large"
              :loading="loading"
              flat
              class="mb-2"
            >
              <v-icon start>mdi-login</v-icon>
              Ingresar
            </v-btn>

            <!-- ⚠️ Error -->
            <v-alert
              v-if="error"
              type="error"
              class="mt-4"
              dense
              border="start"
              border-color="error"
            >
              {{ error }}
            </v-alert>
          </v-form>
        </v-card-text>

        <!-- 🔑 Olvidé mi clave -->
        <v-card-actions class="px-3">
          <v-btn
            class="oval-border-btn"
            small
            text-xs-left
            color="secondary"
            flat
          >
            ¿Olvidé mi Clave?&nbsp;<v-icon>help</v-icon>
          </v-btn>
        </v-card-actions>

        <!-- 📞 Footer dentro del card -->
        <v-footer class="bg-grey-lighten-4" style="height: 64px">
          <v-container>
            <v-row justify="center">
              <v-col cols="12" class="text-center">
                Ayuda y soporte técnico:
                <strong> {{ email }} </strong> |
                <strong> {{ telefono }} </strong>
              </v-col>
            </v-row>
          </v-container>
        </v-footer>
      </v-card>

      <!-- 📝 Texto final debajo del card -->
      <p class="text-center copyright-text">
        Probus Systems ©. Powered by
        <a href="https://tecklas.com/" target="_blank">TECKLAS</a>
      </p>
    </div>
  </v-container>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useAuthStore } from "@/stores/auth";
import { Configuration } from "@/configuration";

const auth = useAuthStore();
const username = ref("");
const password = ref("");
const error = ref("");
const loading = ref(false);
const showPassword = ref(false);
const email = Configuration.getEmail();
const telefono = Configuration.getTelefono();

onMounted(() => {
  localStorage.clear();
});

const onLogin = async () => {
  try {
    if (username.value == "" || password.value == "") return;
    loading.value = true;
    error.value = "";
    await auth.login(username.value, password.value);
  } catch (err: any) {
    error.value = err.message || "Credenciales inválidas";
  } finally {
    loading.value = false;
  }
};

const onMicrosoftLogin = async () => {
  try {
    loading.value = true;
    error.value = "";
    await auth.loginWithMicrosoft();
  } catch (err: any) {
    error.value = err.message || "Error al iniciar sesión con Microsoft";
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.login-wrapper {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.login-card {
  width: 100%;
  max-width: 420px;
  max-height: 90%;
  overflow-y: auto;
  box-sizing: border-box;
  border-radius: 16px;
}

.login-logo {
  width: 200px;
  height: auto;
}

.copyright-text {
  margin-top: 12px;
  font-size: 0.8rem;
  color: #666;
  text-align: center;
}
</style>
