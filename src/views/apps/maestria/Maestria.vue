<script setup>
import { computed, nextTick, ref, watch } from "vue";
import BaseBreadcrumb from "@/components/shared/BaseBreadcrumb.vue";
import { Icon } from "@iconify/vue";

const page = ref({ title: "Gestor de Maestrías" });
const breadcrumbs = ref([]);

const dialog = ref(false);
const dialogDelete = ref(false);

const headers = ref([
  { title: "ID", align: "start", sortable: false, key: "id" },
  { title: "Nombre", key: "nombre" },
  { title: "Duración (meses)", key: "duracion" },
  { title: "Costo ($)", key: "costo" },
  { title: "Institución", key: "institucion" },
  { title: "Acciones", key: "actions", sortable: false },
]);

const maestrias = ref([]);
const editedIndex = ref(-1);
const editedItem = ref({
  id: "",
  nombre: "",
  duracion: 0,
  costo: 0,
  institucion: "",
});

const defaultItem = ref({
  id: "",
  nombre: "",
  duracion: 0,
  costo: 0,
  institucion: "",
});

const formTitle = computed(() => {
  return editedIndex.value === -1 ? "Nueva Maestría" : "Editar Maestría";
});

function initialize() {
  maestrias.value = [
    { id: "M001", nombre: "Ciencia de Datos", duracion: 24, costo: 5000, institucion: "MIT" },
    { id: "M002", nombre: "Ingeniería de Software", duracion: 18, costo: 4500, institucion: "Harvard" },
    { id: "M003", nombre: "Ciberseguridad", duracion: 12, costo: 4000, institucion: "Stanford" },
    { id: "M004", nombre: "Inteligencia Artificial", duracion: 24, costo: 6000, institucion: "Oxford" },
    { id: "M005", nombre: "Administración de Empresas", duracion: 18, costo: 7000, institucion: "Columbia" },
  ];
}

function editItem(item) {
  editedIndex.value = maestrias.value.indexOf(item);
  editedItem.value = Object.assign({}, item);
  dialog.value = true;
}

function deleteItem(item) {
  editedIndex.value = maestrias.value.indexOf(item);
  editedItem.value = Object.assign({}, item);
  dialogDelete.value = true;
}

function deleteItemConfirm() {
  maestrias.value.splice(editedIndex.value, 1);
  closeDelete();
}

function close() {
  dialog.value = false;
  nextTick(() => {
    editedItem.value = Object.assign({}, defaultItem.value);
    editedIndex.value = -1;
  });
}

function closeDelete() {
  dialogDelete.value = false;
  nextTick(() => {
    editedItem.value = Object.assign({}, defaultItem.value);
    editedIndex.value = -1;
  });
}

function save() {
  if (editedIndex.value > -1) {
    Object.assign(maestrias.value[editedIndex.value], editedItem.value);
  } else {
    maestrias.value.push(editedItem.value);
  }
  close();
}

watch(dialog, (val) => {
  val || close();
});
watch(dialogDelete, (val) => {
  val || closeDelete();
});

initialize();
</script>

<template>
  <BaseBreadcrumb :title="page.title" :breadcrumbs="breadcrumbs"></BaseBreadcrumb>
  <v-row>
    <v-col cols="12">
      <UiParentCard title="">
        <v-data-table
          class="border rounded-md datatabels"
          :headers="headers"
          :items="maestrias"
          :sort-by="[{ key: 'nombre', order: 'asc' }]"
        >
          <template v-slot:top>
            <v-toolbar class="bg-lightprimary" flat>
              <v-toolbar-title>Listado de Maestrías</v-toolbar-title> 
              <v-divider class="mx-4" inset vertical></v-divider>
              <v-spacer></v-spacer>
              <v-dialog v-model="dialog" max-width="500px">
                <template v-slot:activator="{ props }">
                  <v-btn color="primary" variant="flat" dark v-bind="props">Agregar Maestría</v-btn>
                </template>
                <v-card>
                  <v-card-title class="pa-4 bg-primary">
                    <span class="text-h5">{{ formTitle }}</span>
                  </v-card-title>
                  <v-card-text>
                    <v-container class="px-0">
                      <v-row>
                        <v-col cols="12" sm="6">
                          <v-text-field v-model="editedItem.id" label="ID"></v-text-field>
                        </v-col>
                        <v-col cols="12" sm="6">
                          <v-text-field v-model="editedItem.nombre" label="Nombre"></v-text-field>
                        </v-col>
                        <v-col cols="12" sm="6">
                          <v-text-field v-model="editedItem.duracion" label="Duración (meses)"></v-text-field>
                        </v-col>
                        <v-col cols="12" sm="6">
                          <v-text-field v-model="editedItem.costo" label="Costo ($)"></v-text-field>
                        </v-col>
                        <v-col cols="12">
                          <v-text-field v-model="editedItem.institucion" label="Institución"></v-text-field>
                        </v-col>
                      </v-row>
                    </v-container>
                  </v-card-text>
                  <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="error" variant="flat" dark @click="close">Cancelar</v-btn>
                    <v-btn color="primary" variant="flat" dark @click="save">Guardar</v-btn>
                  </v-card-actions>
                </v-card>
              </v-dialog>
              <v-dialog v-model="dialogDelete" max-width="500px">
                <v-card>
                  <v-card-title class="text-h5 text-center py-6">¿Seguro que deseas eliminar esta maestría?</v-card-title>
                  <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="error" variant="flat" dark @click="closeDelete">Cancelar</v-btn>
                    <v-btn color="primary" variant="flat" dark @click="deleteItemConfirm">Aceptar</v-btn>
                    <v-spacer></v-spacer>
                  </v-card-actions>
                </v-card>
              </v-dialog>
            </v-toolbar>
          </template>
          <template v-slot:items="{ item }">
            <div class="d-flex gap-3">
              <Icon icon="solar:pen-new-square-broken" height="20" class="text-primary cursor-pointer" @click="editItem(item)" />
              <Icon icon="solar:trash-bin-minimalistic-linear" height="20" class="text-error cursor-pointer" @click="deleteItem(item)" />
            </div>
          </template>
          <template v-slot:no-data>
            <v-btn color="primary" @click="initialize">Reset</v-btn>
          </template>
        </v-data-table>
      </UiParentCard>
    </v-col>
  </v-row>
</template>
