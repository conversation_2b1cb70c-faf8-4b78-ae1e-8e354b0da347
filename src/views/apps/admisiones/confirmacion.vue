<template>
  <v-container fluid>
    <v-card class="confirmacion-card pa-6">
      <v-card-title class="text-h5 text-center mb-6">
        Revisión Final
      </v-card-title>
      
      <!-- Accordion: Cada panel es un paso con su resumen -->
      <v-expansion-panels
        multiple
        v-model="activePanels"
        class="accordion-custom"
      >
        <v-expansion-panel
          v-for="(section, index) in sections"
          :key="index"
          :value="index"
          class="mb-2"
        >
          <v-expansion-panel-title class="text-subtitle-1">
            <v-icon left color="primary">{{ section.icon }}</v-icon>
            {{ section.title }}
          </v-expansion-panel-title>
          <v-expansion-panel-text>
            <div class="section-content">
              <v-row>
                <v-col
                  v-for="[key, value] in Object.entries(section.content)"
                  :key="key"
                  cols="12"
                  sm="6"
                >
                  <div class="content-line">
                    <span class="key font-weight-bold">{{ capitalize(key) }}:</span>
                    <span class="value">{{ value }}</span>
                  </div>
                </v-col>
              </v-row>
            </div>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>

      <!-- Botón de Enviar -->
      <v-card-actions class="justify-center mt-6">
        <v-btn
         outlined
         small
          color="primary"
          @click="enviarDatos"
        >
          <v-icon left>mdi-send</v-icon>
          Enviar
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-container>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue';

// Define la forma de cada objeto de data recibido (cada paso)
interface PasoData {
  [key: string]: string;
}

// Estructura interna para renderizar cada panel
interface SectionData {
  title: string;
  content: Record<string, string>;
  icon: string;
}

export default defineComponent({
  name: 'ConfirmacionFinal',
  props: {
    aggregatedData: {
      type: Array as () => PasoData[],
      default: () => [
        {
          Nombres: 'Carlos Antonio',
          Apellidos: 'Peña Mejía',
          Fecha: '17/01/1991',
          'Tipo Admisión': 'NUEVO INGRESO',
          Sexo: 'MASCULINO',
          Nacionalidad: 'DOMINICANA',
          'Estado Civil': 'CASADO(A)',
          'País Nacimiento': 'REPÚBLICA DOMINICANA',
          Municipio: 'Municipio 1',
          Cédula: '001-0225575-1',
          Pasaporte: '',
        },
        {
          'Dirección - País': 'REPÚBLICA DOMINICANA',
          Provincia: 'SANTO DOMINGO',
          Municipio: 'SANTO DOMINGO',
          'Calle y Número': 'c/ Ave-lapepita #34',
          Teléfono: '************',
          Celular: '************',
          Email: '<EMAIL>',
          Facebook: '/carlospe',
          Instagram: '/carlospeinsta',
          'X (antes Twitter)': '/carlospeX',
          'Contactos Adicionales': 'Madre: ************; Padre: ************',
        },
        {
          'Programa/Carrera': 'MAESTRÍA',
          'Distrito Educativo': 'DISTRITO NACIONAL',
          Modalidad: 'SELECCIÓN DE DATOS EJECUTIVO',
          'Universidad/Institución': 'Universidad PUCMM',
          'Fecha Inicio': '24/01/2025',
          'Título/Grado': 'POSGRADO',
          Idiomas: 'Español (Lee, Escribe, Habla)',
        },
        {
          'Nombre Empresa': 'TEXILAS',
          Cargo: 'Analista Programador',
          'Teléfono Empresa': '************',
          'Dirección Empresa': 'Av Duarte dirección empresa',
        },
        {
          'Pregunta 1': '¿Tuvo dificultad para iniciar? R: No, ninguna dificultad.',
          'Pregunta 2': '¿Fue difícil ubicarse con la asistencia? R: No, sin dificultad.',
          'Pregunta 3': '¿Qué le pareció el formato de exámenes? R: 5 - Me gusta.',
          'Pregunta 4': '¿Sugiere mejoras? R: No, estoy conforme.',
        },
        {
          '¿Cómo se enteró?': 'Redes Sociales',
          'Cuál fue la razón principal de elegirnos': 'Prestigio',
        },
        {
          Observación: 'Datos correctos, proceda al envío.',
        },
      ],
    },
  },
  emits: ['enviar-final'],
  setup(props, { emit }) {
    // Títulos e íconos fijos para cada sección
    const sectionConfig: { title: string; icon: string }[] = [
      { title: 'Información Personal', icon: 'mdi-account' },
      { title: 'Contactos', icon: 'mdi-phone' },
      { title: 'Académico', icon: 'mdi-school' },
      { title: 'Experiencia Laboral', icon: 'mdi-briefcase' },
      { title: 'Preguntas', icon: 'mdi-help-circle' },
      { title: 'Adicional', icon: 'mdi-information' },
      { title: 'Revisión Final', icon: 'mdi-check-circle' },
    ];

    // Controla los paneles abiertos (todos abiertos por defecto)
    const activePanels = ref<number[]>(Array.from({ length: sectionConfig.length }, (_, i) => i));

    // Computed que genera un arreglo de secciones para el acordeón
    const sections = computed<SectionData[]>(() =>
      props.aggregatedData.map((data, idx) => ({
        title: sectionConfig[idx]?.title || `Paso ${idx + 1}`,
        content: data,
        icon: sectionConfig[idx]?.icon || 'mdi-file-document',
      }))
    );

    // Función auxiliar para capitalizar la primera letra de la clave
    const capitalize = (str: string) => {
      return str.charAt(0).toUpperCase() + str.slice(1);
    };

    // Función para enviar los datos finales
    const enviarDatos = () => {
      console.log('Enviando datos finales:', props.aggregatedData);
      emit('enviar-final', props.aggregatedData);
      alert('Datos enviados. Revisa la consola para más detalles.');
    };

    return { sections, activePanels, enviarDatos, capitalize };
  },
});
</script>

<style scoped>
.confirmacion-card {
  max-width: 900px;
  margin: 20px auto;
  background-color: #ffffff;
  border-radius: 8px;
}

.accordion-custom {
  background-color: transparent;
}

.v-expansion-panel {
  border-radius: 8px;
  background-color: #f9fafb;
}

.v-expansion-panel-title {
  padding: 16px;
}

.v-expansion-panel-text {
  padding: 16px;
}

.section-content {
  padding: 8px 0;
}

.content-line {
  display: flex;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
}

.key {
  margin-right: 8px;
  min-width: 150px;
}

.value {
  flex: 1;
}



/* Responsividad */
@media (max-width: 600px) {
  .confirmacion-card {
    margin: 10px;
    padding: 16px;
  }

  .key {
    min-width: 100px;
  }
}
</style>