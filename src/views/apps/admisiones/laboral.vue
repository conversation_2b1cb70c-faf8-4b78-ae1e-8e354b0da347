<template>
    <v-row>
      <!-- Encabezado de la Sección Trabajo -->
      <v-col cols="12">
        <h6 class="text-h6 mb-2 d-flex align-center">
          <BriefcaseIcon size="20" stroke-width="1.5" class="mr-2" />
          Trabajo
        </h6>
        <v-divider class="mb-3" />
      </v-col>
  
      <!-- Campos de la sección Trabajo (columna única o ajusta según tu layout) -->
      <v-col cols="12">
        <!-- Nombre de la Empresa -->
        <v-text-field
          variant="outlined"
          color="primary"
          label="Nombre Empresa"
          maxlength="50"
          show-size
          v-model="nombreEmpresa"
        >
          <template #prepend-inner>
            <BuildingSkyscraperIcon size="20" stroke-width="1.5" />
          </template>
        </v-text-field>
  
        <!-- Cargo -->
        <v-text-field
          class="mt-4"
          variant="outlined"
          color="primary"
          label="Cargo"
          maxlength="50"
          show-size
          v-model="cargo"
        >
          <template #prepend-inner>
            <UserIcon size="20" stroke-width="1.5" />
          </template>
        </v-text-field>
  
        <!-- Teléfono de trabajo -->
        <v-text-field
          class="mt-4"
          variant="outlined"
          color="primary"
          label="Teléfono"
          v-model="telefonoLaboral"
        >
          <template #prepend-inner>
            <PhoneCallIcon size="20" stroke-width="1.5" />
          </template>
        </v-text-field>
  
        <!-- Dirección Laboral (textarea) -->
        <v-textarea
          class="mt-4"
          variant="outlined"
          color="primary"
          label="Dirección"
          rows="3"
          v-model="direccionLaboral"
        >
          <template #prepend-inner>
            <MapPinIcon size="20" stroke-width="1.5" />
          </template>
        </v-textarea>
      </v-col>
    </v-row>
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue'
  import {
    BriefcaseIcon,
    BuildingSkyscraperIcon,
    UserIcon,
    PhoneCallIcon,
    MapPinIcon
  } from 'vue-tabler-icons'
  
  // Campos del formulario Trabajo
  const nombreEmpresa = ref('')
  const cargo = ref('')
  const telefonoLaboral = ref('')
  const direccionLaboral = ref('')
  </script>
  
  <style scoped>
  .mt-4 {
    margin-top: 1rem !important;
  }
  .mb-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-3 {
    margin-bottom: 1rem !important;
  }
  .d-flex {
    display: flex;
  }
  .align-center {
    align-items: center;
  }
  .mr-2 {
    margin-right: 0.5rem;
  }
  </style>
  