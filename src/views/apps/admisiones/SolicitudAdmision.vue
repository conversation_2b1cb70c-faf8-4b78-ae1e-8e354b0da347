
<template>

  <!--<PERSON><PERSON><PERSON> llaman<PERSON> al login admision-->

   <loginAdmision v-if="!showStepper" @iniciar-nueva-solicitud="iniciarNuevaSolicitud" />

  <!-- Contenedor principal con fondo gris claro y centrado -->
  <v-container v-if ="showStepper" fluid class="grey lighten-4 pa-0 ma-0 d-flex align-center justify-center fill-height" elevation="2">
    <v-card class="border pa-4 stepper-card" flat elevation="5">
      <!-- Logo dentro del card, con menos margen -->
      <div class="d-flex align-start justify-start mb-4 logo-container">
        <img
          src="@/assets/images/logos/logo.png"
          alt="Logo de la compañía"
          height="60"
        />
      </div>
  <v-stepper
    v-model="currentStep"
    :items="steps.map(s => s.title)"
    class="border"
    flat
  >

    <template 
    v-for="n in steps.length" :key="n" v-slot:[`item.${n}`]>
      <v-card flat>
        <h5 class="text-h5 mb-4">{{ steps[n - 1].title }}</h5>
        <component :is="steps[n - 1].component" :ref="el => setComponentRef(n - 1, el)" />
      </v-card>
    </template>
  </v-stepper>
  </v-card>
</v-container>
</template>

<script lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { defineComponent, ref, computed,onMounted,watch, markRaw } from 'vue'
import Personal from './personal.vue'
import Generales from './generales.vue'
import Academico from './academico.vue'
import Laboral from './laboral.vue'
import Pregunta from './pregunta.vue'
import Adicional from './adicional.vue'
import confirmacion from './confirmacion.vue'
import loginAdmision from './loginAdmision.vue'

interface Step {
  title: string
  component: any
  requiresValidation: boolean
}

interface StepComponent {
  validate?: () => boolean
}

const Contact = defineComponent({
  template: '<div><h6>Información de Contacto</h6><p>Placeholder para el componente Contact</p></div>'
})
const Education = defineComponent({
  template: '<div><h6>Educación</h6><p>Placeholder para el componente Education</p></div>'
})
const Work = defineComponent({
  template: '<div><h6>Experiencia Laboral</h6><p>Placeholder para el componente Work</p></div>'
})
const Documents = defineComponent({
  template: '<div><h6>Documentos</h6><p>Placeholder para el componente Documents</p></div>'
})
const Health = defineComponent({
  template: '<div><h6>Salud</h6><p>Placeholder para el componente Health</p></div>'
})
const Review = defineComponent({
  template: '<div><h6>Revisión</h6><p>Placeholder para el componente Review</p></div>'
})
    


export default defineComponent({
  name: 'DynamicStepper',
  components: {
    loginAdmision,
    Personal,
    Contact,
    Education,
    Work,
    Documents,
    Health,
    Review
  },
  setup() {

// tu lógica habitual…
const showStepper = ref(false)
// … definición de steps, setComponentRef, etc.

onMounted(() => {
  if (sessionStorage.getItem('showStepper') === 'true') {
    showStepper.value = true
    // ¡muy importante! eliminar la flag para que al recargar
    // no vuelva a leerse
    sessionStorage.removeItem('showStepper')
  }
})

  
    const currentStep = ref<number>(1)
    const steps = ref<Step[]>([
      { title: 'Información Personal', component: markRaw(Personal), requiresValidation: true },
      { title: 'Contacto', component: markRaw(Generales), requiresValidation: false },
      { title: 'Académico', component: markRaw(Academico), requiresValidation: false },
      { title: 'Experiencia Laboral', component: markRaw(Laboral), requiresValidation: false },
      { title: 'Preguntas', component: markRaw(Pregunta), requiresValidation: false },
      { title: 'Adicional', component: markRaw(Adicional), requiresValidation: false },
      { title: 'Revisión Final', component: markRaw(confirmacion), requiresValidation: false }
    ])

    const stepComponents = ref<(StepComponent | null)[]>(new Array(steps.value.length).fill(null))

    const setComponentRef = (index: number, el: StepComponent | null): void => {
      stepComponents.value[index] = el
    }

    const isStepValid = (index: number): boolean => {
      const step = steps.value[index]
      if (!step.requiresValidation) return true
      const component = stepComponents.value[index]
      return component && typeof component.validate === 'function' ? component.validate() : true
    }

    const nextStep = async (currentIndex: number): Promise<void> => {
      if (currentIndex === steps.value.length) {
       // console.log('Formulario completado')
        return
      }
      if (isStepValid(currentIndex - 1)) {
        currentStep.value++
      }
    }

      
    // Nueva función para iniciar una solicitud
    const iniciarNuevaSolicitud = () => {
      showStepper.value = true
      currentStep.value = 1 // Asegurar que comienza desde el primer paso
    }
    // Objeto que almacenará los datos acumulados por cada paso (usamos index como key)
    const stepsData = ref<Record<number, any>>({})

    // Función para obtener la data del paso actual (si existe)
    const getDataForStep = (index: number) => {
      return stepsData.value[index] || {}
    }

    // Función para actualizar la data almacenada para cada paso
    const updateStepData = (index: number, data: any) => {
      stepsData.value = { ...stepsData.value, [index]: data }
    }

    // Computed para obtener la data agregada de todos los pasos
    const aggregatedData = computed(() => {
      return steps.value.map((_, index) => stepsData.value[index] || {})
    })

    // Función para enviar los datos (por ejemplo, a una API)
    const enviarDatosFinal = () => {
      // Aquí puedes llamar a tu API, por ejemplo:
      // axios.post('/api/enviar', aggregatedData.value).then(...);
    }

    return {
      showStepper,
      iniciarNuevaSolicitud,
      currentStep,
      steps,
      stepComponents,
      setComponentRef,
      isStepValid,
      nextStep,

    }
  }
})
</script>


<style scoped>
.border {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.stepper-card{
  max-width: 1200px;
  width: 100%;
}



.logo-container {
  z-index: 1; /* Asegura que el logo aparezca sobre otros elementos si es necesario */
}
</style>