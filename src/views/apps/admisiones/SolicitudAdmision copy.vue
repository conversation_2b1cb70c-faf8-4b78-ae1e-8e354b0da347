<!-- src/views/apps/admisiones/SolicitudAdmision.vue -->
<template>
  <v-container class="py-4">
    <v-card class="pa-4" outlined>
      <v-card-title class="text-h5">Solicitud de Admisiones</v-card-title>
      <v-card-text>
        <v-stepper v-model="currentStep">
          <!-- Encabezado del Stepper -->
          <v-stepper-header>
            <v-stepper-step :complete="currentStep > 1" :step="1">
              Datos Personales
            </v-stepper-step>
            <v-divider></v-divider>
            <v-stepper-step :complete="currentStep > 2" :step="2">
              Contacto y Datos Académicos
            </v-stepper-step>
            <v-divider></v-divider>
            <v-stepper-step :step="3">
              Confirmación
            </v-stepper-step>
          </v-stepper-header>

          <!-- Contenido del Stepper -->
          <v-stepper-items>
            <!-- Paso 1: Información Personal -->
            <v-stepper-content :step="1">
              <StepPersonal ref="step1" v-model="stepData.personal" />
              <div class="d-flex justify-end mt-4">
                <v-btn color="primary" @click="goToNextStep(step1)">Siguiente</v-btn>
              </div>
            </v-stepper-content>

            <!-- Paso 2: Datos de Contacto y Académicos -->
            <v-stepper-content :step="2">
              <StepContactAcademic ref="step2" v-model="stepData.contactAcademic" />
              <div class="d-flex justify-space-between mt-4">
                <v-btn text @click="prevStep">Anterior</v-btn>
                <v-btn color="primary" @click="goToNextStep(step2)">Siguiente</v-btn>
              </div>
            </v-stepper-content>

            <!-- Paso 3: Confirmación y Envío -->
            <v-stepper-content :step="3">
              <StepConfirmation :data="stepData" />
              <div class="d-flex justify-space-between mt-4">
                <v-btn text @click="prevStep">Anterior</v-btn>
                <v-btn color="primary" @click="submitForm">Enviar Solicitud</v-btn>
              </div>
            </v-stepper-content>
          </v-stepper-items>
        </v-stepper>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'

// Importar los componentes de cada paso (usa los nombres y rutas reales de tus archivos)
import StepPersonal from '@/views/apps/admisiones/personal.vue'
import StepContactAcademic from '@/views/apps/admisiones/academico.vue'
import StepConfirmation from '@/views/apps/admisiones/confirmacion.vue'

// Paso actual del stepper
const currentStep = ref<number>(1)

// Estado global para almacenar los datos de todos los pasos
const stepData = reactive({
  personal: {
    admissionType: '',
    nationality: '',
    firstName: '',
    sex: '',
    lastName: '',
    civilStatus: '',
    birthDate: '',
    cityOfOrigin: '',
    passport: '',
    idNumber: '',
    bloodType: '',
    address: '',
    notes: ''
  },
  contactAcademic: {
    email: '',
    phone: '',
    program: '',
    institution: '',
    previousDegree: '',
    city: ''
  }
})

// Refs a los componentes de cada paso (para acceder a su método validate, etc.)
const step1 = ref<InstanceType<typeof StepPersonal>>()
const step2 = ref<InstanceType<typeof StepContactAcademic>>()

const router = useRouter()

/**
 * Avanza al siguiente paso validando el formulario del paso actual.
 */
async function goToNextStep(formRef: any) {
  if (await formRef.value?.validate()) {
    currentStep.value++
  }
}

/**
 * Retrocede al paso anterior.
 */
function prevStep() {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

/**
 * Envía la solicitud (placeholder para la lógica de API).
 */
async function submitForm() {
  console.log('Datos de la solicitud:', JSON.stringify(stepData))
  // Aquí integrarías la llamada a tu API
  router.push('/solicitud-enviada')
}
</script>

<style scoped>
/* Estilos personalizados opcionales */
</style>
