<script setup lang="ts">
import { ref, reactive, watch } from 'vue'

// Tabler Icons que usaremos
import {
  SchoolIcon,
  MapPinIcon,
  ClockIcon,
  CertificateIcon,
  UserIcon,
  CreditCardIcon,
  CheckIcon,
  LanguageIcon,
  CeIcon,
  CalendarIcon,
  BookIcon,
  PencilIcon,
  MicrophoneIcon
} from 'vue-tabler-icons'

// Campos principales (col. izquierda)
const periodoIngreso = ref('')
const recinto = ref('')
const tanda = ref('')
const institucionSuperior = ref('')
const noEncontreSuperior = ref(false)
const fechaGraduacion = ref('')

// Campos principales (col. derecha)
const tipoCarrera = ref('')
const programaCarrera = ref('')
const financiamiento = ref('')
const carrerasActivas = ref(false)

// Sección de Idiomas
// Cada idioma es un objeto con { nombre, lee, escribe, habla }
interface IdiomaData {
  nombre: string;
  lee: boolean;
  escribe: boolean;
  habla: boolean;
}
// Opciones predefinidas para el dropdown de idiomas
const idiomaOpciones = [
  'Español',
  'Inglés',
  'Francés',
  'Alemán',
  'Italiano',
  'Portugués',
  'Ruso',
  'Chino'
]

// Modelo para el v-select múltiple: array de strings
const selectedIdiomas = ref<string[]>([])

// Inicia el arreglo de idiomas con un objeto de ejemplo
const idiomas = ref<IdiomaData[]>([
  { nombre: '', lee: false, escribe: false, habla: false }
]);

// Función para agregar un nuevo idioma
const addIdioma = (): void => {
  idiomas.value.push({ nombre: '', lee: false, escribe: false, habla: false });
}

// Función para eliminar un idioma (si hay más de uno)
const removeIdioma = (index: number): void => {
  if (idiomas.value.length > 1) {
    idiomas.value.splice(index, 1);
  }
}
// Objeto reactivo para almacenar el estado de cada idioma seleccionado

const idiomasInfo = reactive<Record<string, { lee: boolean; escribe: boolean; habla: boolean }>>({})
// Inicializar o actualizar idiomasInfo cuando cambian los idiomas seleccionados
// Sincroniza idiomasInfo con los idiomas seleccionados:
watch(
  selectedIdiomas,
  (newVal) => {
    // Por cada idioma nuevo, si no existe, inicialízalo
    newVal.forEach((lang) => {
      if (!(lang in idiomasInfo)) {
        idiomasInfo[lang] = { lee: false, escribe: false, habla: false }
      }
    })
    // Elimina las entradas que dejaron de estar seleccionadas
    Object.keys(idiomasInfo).forEach(key => {
      if (!newVal.includes(key)) {
        delete idiomasInfo[key]
      }
    })
  },
  { immediate: true }
)
</script>   

<template>
    <v-row>
      <v-col cols="12">
        <h6 class="text-h6 mb-2 d-flex align-center">
          <SchoolIcon size="20" stroke-width="1.5" class="mr-2" />
          Programa o Carrera de Interés
        </h6>
        <v-divider class="mb-3" />
      </v-col>
  
      <!-- Columna Izquierda -->
      <v-col cols="12" sm="6">
  
        <!-- Periodo de Ingreso -->
        <v-select
          variant="outlined"
          color="primary"
          label="Periodo de Ingreso *"
          :items="['1-2023', '2-2023', '3-2020']"
          v-model="periodoIngreso"
        >
          <template #prepend-inner>
            <CalendarIcon size="20" stroke-width="1.5" />
          </template>
        </v-select>
  
        <!-- Recinto -->
        <v-select
          class="mt-4"
          variant="outlined"
          color="primary"
          label="Recinto *"
          :items="['DISTRITO NACIONAL', 'SANTIAGO', 'OTRO']"
          v-model="recinto"
        >
          <template #prepend-inner>
            <MapPinIcon size="20" stroke-width="1.5" />
          </template>
        </v-select>
  
        <!-- Tanda / Horario -->
        <v-select
          class="mt-4"
          variant="outlined"
          color="primary"
          label="Tanda / Horario *"
          :items="['MATUTINA', 'VESPERTINA', 'NOCTURNA']"
          v-model="tanda"
        >
          <template #prepend-inner>
            <ClockIcon size="20" stroke-width="1.5" />
          </template>
        </v-select>
  
        <!-- ¿Dónde cursó su educación superior? -->
        <v-text-field
          class="mt-4"
          variant="outlined"
          color="primary"
          label="¿Dónde cursó su educación superior?"
          v-model="institucionSuperior"
        >
          <template #prepend-inner>
            <SchoolIcon size="20" stroke-width="1.5" />
          </template>
          <!-- Check para "No lo encontré" -->
          <template #append>
            <v-checkbox
              hide-details
              :label="noEncontreSuperior ? 'No lo encontré' : ''"
              v-model="noEncontreSuperior"
            />
          </template>
        </v-text-field>
  
        <!-- Fecha de Graduación -->
        <v-text-field
          class="mt-4"
          variant="outlined"
          color="primary"
          label="Fecha Graduación"
          type="date"
          v-model="fechaGraduacion"
        >
          <template #prepend-inner>
            <CalendarIcon size="20" stroke-width="1.5" />
          </template>
        </v-text-field>
      </v-col>
  
      <!-- Columna Derecha -->
      <v-col cols="12" sm="6">
  
        <!-- Tipo de Carrera -->
        <v-select
          variant="outlined"
          color="primary"
          label="Tipo de Carrera *"
          :items="['MAESTRÍA', 'LICENCIATURA', 'DIPLOMADO', 'TÉCNICO']"
          v-model="tipoCarrera"
        >
          <template #prepend-inner>
            <CertificateIcon size="20" stroke-width="1.5" />
          </template>
        </v-select>
  
        <!-- Programa / Carrera -->
        <v-text-field
          class="mt-4"
          variant="outlined"
          color="primary"
          label="Programa / Carrera *"
          placeholder="Ej: MAESTRÍA EN SALUD PÚBLICA"
          v-model="programaCarrera"
        >
          <template #prepend-inner>
            <UserIcon size="20" stroke-width="1.5" />
          </template>
        </v-text-field>
  
        <!-- Financiamiento de Estudio -->
        <v-select
          class="mt-4"
          variant="outlined"
          color="primary"
          label="Financiamiento de Estudio"
          :items="['SOLICITÉ CRÉDITO EDUCATIVO', 'BECA', 'PROPIO']"
          v-model="financiamiento"
        >
          <template #prepend-inner>
            <CreditCardIcon size="20" stroke-width="1.5" />
          </template>
        </v-select>
  
        <!-- Carreras Activas (switch) -->
        <v-switch
          class="mt-4"
          color="primary"
          label="Carreras activas en este momento"
          v-model="carrerasActivas"
        >
          <template #prepend-inner>
            <CheckIcon size="20" stroke-width="1.5" />
          </template>
        </v-switch>
      </v-col>
    </v-row>
  
     <!-- Sección de Idiomas -->
      <v-container fluid class="gradient-section pa-6">
     <v-row class="mt-6">
    <!-- Encabezado de la Sección Idiomas -->
    <v-col cols="12">
      <h6 class="text-h6 mb-2 d-flex align-center">
        <LanguageIcon size="20" stroke-width="1.5" class="mr-2" />
        Idiomas
      </h6>
      <v-divider class="mb-3" />
    </v-col>

    <!-- Dropdown para seleccionar idiomas (múltiple) -->
    <v-col cols="12" sm="6">
      <v-select
        multiple
        variant="outlined"
        color="primary"
        class="white-select"
        
        label="Seleccione los idiomas"
        :items="idiomaOpciones"
        v-model="selectedIdiomas"
        chips
      >
        <template #prepend-inner>
          <LanguageIcon size="20" stroke-width="1.5" />
        </template>
      </v-select>
    </v-col>

    <!-- Por cada idioma seleccionado, se muestra una línea con el nombre y los switches -->
    <v-col
  v-for="(lang) in selectedIdiomas"
  :key="lang"
  cols="12"
  class="mt-0"
>
  <v-row class="d-flex align-center">
    <!-- Nombre del idioma -->
    <v-col cols="12" sm="2" class="d-flex align-center py-0" >
      <strong class="lang-title">{{ lang }}</strong>
    </v-col>

    <!-- Switches juntos en una sola columna -->
    <v-col cols="12" sm="6">
      <div class="d-flex flex-wrap switch-group">
        <!-- Switch Lee -->
        <v-switch
          dense
          hide-details
          v-model="idiomasInfo[lang].lee"
          inset
          color="primary"
          class="mr-4"
        >
          <template #prepend-inner>
            <Book size="16" stroke-width="1.5" />
          </template>
          <template #label>
            <span class="label-switch">Lee</span>
          </template>
        </v-switch>

        <!-- Switch Escribe -->
        <v-switch
          dense
          hide-details
          v-model="idiomasInfo[lang].escribe"
          inset
          color="primary"
          class="mr-4"
        >
          <template #prepend-inner>
            <PencilIcon size="16" stroke-width="1.5" />
          </template>
          <template #label>
            <span class="label-switch">Escribe</span>
          </template>
        </v-switch>

        <!-- Switch Habla -->
        <v-switch
          dense
          hide-details
          v-model="idiomasInfo[lang].habla"
          inset
          color="primary"
        >
          <template #prepend-inner>
            <MicrophoneIcon size="16" stroke-width="1.5" />
          </template>
          <template #label>
            <span class="label-switch">Habla</span>
          </template>
        </v-switch>
      </div>
    </v-col>
  </v-row>
</v-col>


  </v-row>
</v-container>
  </template>

<style scoped>
/* Margins */
.mt-2 {
  margin-top: 0.5rem !important;
}
.mt-4 {
  margin-top: 1rem !important;
}
.mt-6 {
  margin-top: 1.5rem !important;
}
.mb-2 {
  margin-bottom: 0.5rem !important;
}
.mb-3 {
  margin-bottom: 1rem !important;
}
.mb-4 {
  margin-bottom: 1rem !important;
}

/* Spacing */
.mr-2 {
  margin-right: 0.5rem;
}
.gap-4 {
  gap: 1rem;
}

/* Flex utilities */
.d-flex {
  display: flex;
}
.align-center {
  align-items: center;
}

.switch-group {
  gap: 0.5rem;
  align-items: center;

}

.lang-title{
font-size: 18px;
font-weight: 600;


}

/* .gradient-section{
  background:linear-gradient(135deg, #4039a6, #4c45c0);
  border-radius: 12px; 
  color: white;
} */

/* .label-switch{
  color:white;
}
 */


</style>
