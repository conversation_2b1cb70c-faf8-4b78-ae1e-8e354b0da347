<template>
    <v-row>
      <!-- Encabezado de la sección Preguntas -->
      <v-col cols="12">
        <h6 class="text-h6 d-flex align-center mb-2">
          <HelpIcon size="20" stroke-width="1.5" class="mr-2" />
          Preguntas
        </h6>
        <v-divider class="mb-4" />
      </v-col>
  
      <!-- Iterar sobre la lista de preguntas -->
      <v-col
        v-for="question in questions"
        :key="question.id"
        cols="12"
        class="mb-4"
      >
        <!-- Enunciado de la pregunta -->
        <p class="mb-2 font-weight-medium">
          {{ question.id }}. {{ question.text }}
        </p>
  
        <!-- Grupo de Radio (una sola respuesta) -->
        <v-radio-group
          v-model="answers[question.id]"
          class="ml-4"
        >
          <!-- Iterar sobre las opciones de respuesta -->
          <v-radio
            v-for="(option, idx) in question.options"
            :key="idx"
            :label="option.label"
            :value="option.value"
          />
        </v-radio-group>
      </v-col>
    </v-row>
  </template>
  
  <script setup lang="ts">
  import { reactive } from 'vue'
  import { HelpIcon } from 'vue-tabler-icons'
  
  // Cada pregunta puede tener un ID, un texto y un listado de opciones
  interface Question {
    id: number
    text: string
    options: { label: string; value: number }[]
  }
  
  // Definimos el arreglo de preguntas
  const questions: Question[] = [
    {
      id: 1,
      text: '¿Tiene dificultad para ver, incluso cuando usa lentes?',
      options: [
        { label: '1. No, ninguna dificultad.', value: 1 },
        { label: '2. Sí, cierta dificultad.', value: 2 },
        { label: '3. Sí, mucha dificultad.', value: 3 },
        { label: '4. No puedo ver en lo absoluto.', value: 4 },
        { label: '5. No puedo realizar esta actividad.', value: 5 }
      ]
    },
    {
      id: 2,
      text: '¿Tiene dificultad para oír, incluso cuando usa audífonos?',
      options: [
        { label: '1. No, ninguna dificultad.', value: 1 },
        { label: '2. Sí, cierta dificultad.', value: 2 },
        { label: '3. Sí, mucha dificultad.', value: 3 },
        { label: '4. No puedo oír en lo absoluto.', value: 4 },
        { label: '5. No puedo realizar esta actividad.', value: 5 }
      ]
    },
    {
      id: 3,
      text: '¿Tiene dificultad para caminar o subir escalones?',
      options: [
        { label: '1. No, ninguna dificultad.', value: 1 },
        { label: '2. Sí, cierta dificultad.', value: 2 },
        { label: '3. Sí, mucha dificultad.', value: 3 },
        { label: '4. No puedo caminar en lo absoluto.', value: 4 },
        { label: '5. No puedo realizar esta actividad.', value: 5 }
      ]
    },
    // Agrega más preguntas según necesites...
  ]
  
  // Objeto reactivo que almacena la respuesta seleccionada para cada pregunta
  // La clave es el "id" de la pregunta, y el valor es el "value" de la opción elegida
  const answers = reactive<Record<number, number>>({})
  
  </script>
  
  <style scoped>
  .mb-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-4 {
    margin-bottom: 1rem !important;
  }
  .ml-4 {
    margin-left: 1rem !important;
  }
  .d-flex {
    display: flex;
  }
  .align-center {
    align-items: center;
  }
  .font-weight-medium {
    font-weight: 500;
  }
  .mr-2 {
    margin-right: 0.5rem;
  }
  </style>
  