<template>
  <v-container fluid>
    <!-- Sección: Datos Básicos -->
    <v-row>
      <v-col cols="12">
        <h6 class="text-h6 mb-1 d-flex align-center">
          <UserIcon size="18" stroke-width="1.5" class="mr-1" />
          Datos Básicos
        </h6>
        <v-divider class="mb-1"></v-divider>
      </v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          variant="outlined"
          density="compact"
          color="primary"
          label="Nombres"
        >
          <template #prepend-inner>
            <UserIcon size="18" stroke-width="1.5" />
          </template>
        </v-text-field>
      </v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          variant="outlined"
          density="compact"
          color="primary"
          label="Apellidos"
        >
          <template #prepend-inner>
            <UserIcon size="18" stroke-width="1.5" />
          </template>
        </v-text-field>
      </v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          class="mt-1"
          variant="outlined"
          density="compact"
          color="primary"
          label="Fecha"
          type="date"
        >
          <template #prepend-inner>
            <CalendarIcon size="18" stroke-width="1.5" />
          </template>
        </v-text-field>
      </v-col>
    </v-row>

    <!-- Sección: Información de Admisión -->
    <v-row class="mt-1">
      <v-col cols="12">
        <h6 class="text-h6 mb-1 d-flex align-center">
          <SchoolIcon size="18" stroke-width="1.5" class="mr-1" />
          Información de Admisión
        </h6>
        <v-divider class="mb-1"></v-divider>
      </v-col>

      <v-col cols="12" sm="6">
        <v-select
          variant="outlined"
          density="compact"
          color="primary"
          label="Tipo Admisión"
          :items="['NUEVO INGRESO', 'REINGRESO']"
        >
          <template #prepend-inner>
            <SchoolIcon size="18" stroke-width="1.5" />
          </template>
        </v-select>

        <v-select
          class="mt-1"
          variant="outlined"
          density="compact"
          color="primary"
          label="Nacionalidad"
          :items="['DOMINICANA', 'COLOMBIANA', 'OTRA']"
        >
          <template #prepend-inner>
            <FlagIcon size="18" stroke-width="1.5" />
          </template>
        </v-select>
      </v-col>

      <v-col cols="12" sm="6">
        <v-select
          variant="outlined"
          density="compact"
          color="primary"
          label="Sexo"
          :items="['MASCULINO', 'FEMENINO']"
        >
          <template #prepend-inner>
            <GenderBigenderIcon size="18" stroke-width="1.5" />
          </template>
        </v-select>

        <v-select
          class="mt-1"
          variant="outlined"
          density="compact"
          color="primary"
          label="Estado Civil"
          :items="['SOLTERO(A)', 'CASADO(A)', 'OTRO']"
        >
          <template #prepend-inner>
            <HeartIcon size="18" stroke-width="1.5" />
          </template>
        </v-select>
      </v-col>
    </v-row>

    <!-- Sección: Documentos / Nacimiento -->
    <v-row class="mt-1">
      <v-col cols="12">
        <h6 class="text-h6 mb-1 d-flex align-center">
          <IdBadgeIcon size="18" stroke-width="1.5" class="mr-1" />
          Documentos y Nacimiento
        </h6>
        <v-divider class="mb-1"></v-divider>
      </v-col>

      <!-- Fila 1: País de Nacimiento y Municipio -->
      <v-row class="w-100">
        <v-col cols="12" sm="6">
          <v-select
            variant="outlined"
            density="compact"
            color="primary"
            label="País de Nacimiento *"
            :items="['REPÚBLICA DOMINICANA', 'COLOMBIA', 'OTRO']"
          >
            <template #prepend-inner>
              <WorldIcon size="18" stroke-width="1.5" />
            </template>
          </v-select>
        </v-col>
        <v-col cols="12" sm="6">
          <v-select
            variant="outlined"
            density="compact"
            color="primary"
            label="Municipio *"
            :items="['Municipio 1', 'Municipio 2']"
          >
            <template #prepend-inner>
              <MapPinIcon size="18" stroke-width="1.5" />
            </template>
          </v-select>
        </v-col>
      </v-row>

      <!-- Fila 2: Cédula Identidad y Pasaporte -->
      <v-row class="w-100 mt-1">
        <v-col cols="12" sm="6">
          <v-text-field
            variant="outlined"
            density="compact"
            color="primary"
            label="Cédula Identidad"
            placeholder="Ingrese cédula"
          >
            <template #prepend-inner>
              <IdBadgeIcon size="18" stroke-width="1.5" />
            </template>
          </v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
          <v-text-field
            variant="outlined"
            density="compact"
            color="primary"
            label="Pasaporte"
            placeholder="Ingrese pasaporte"
          >
            <template #prepend-inner>
              <PlaneIcon size="18" stroke-width="1.5" />
            </template>
          </v-text-field>
        </v-col>
      </v-row>
    </v-row>

    <!-- Sección: Información Médica -->
    <v-row class="mt-1">
      <v-col cols="12">
        <h6 class="text-h6 mb-1 d-flex align-center">
          <HeartRateIcon size="18" stroke-width="1.5" class="mr-1" />
          Información Médica
        </h6>
        <v-divider class="mb-1"></v-divider>
      </v-col>

      <v-col cols="12" sm="6">
        <v-select
          variant="outlined"
          density="compact"
          color="primary"
          label="Tipo de Sangre"
          :items="['A+', 'A-', 'B+', 'B-', 'O+', 'O-', 'AB+', 'AB-']"
        >
          <template #prepend-inner>
            <DropletIcon size="18" stroke-width="1.5" />
          </template>
        </v-select>

        <v-text-field
          class="mt-1"
          variant="outlined"
          density="compact"
          color="primary"
          label="Enfermedad"
          placeholder="Describa la enfermedad"
        >
          <template #prepend-inner>
            <FirstAidKitIcon size="18" stroke-width="1.5" />
          </template>
        </v-text-field>
      </v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          variant="outlined"
          density="compact"
          color="primary"
          label="Alergias"
          placeholder="Describa las alergias"
        >
          <template #prepend-inner>
            <AlertCircleIcon size="18" stroke-width="1.5" />
          </template>
        </v-text-field>

        <v-text-field
          class="mt-1"
          variant="outlined"
          density="compact"
          color="primary"
          label="Discapacidad"
          placeholder="Detalle la discapacidad"
        >
          <template #prepend-inner>
            <WheelchairIcon size="18" stroke-width="1.5" />
          </template>
        </v-text-field>
      </v-col>
    </v-row>

    <!-- Sección: Comentarios Finales -->
    <v-row class="mt-1">
      <v-col cols="12">
        <h6 class="text-h6 mb-1 d-flex align-center">
          <MessageIcon size="18" stroke-width="1.5" class="mr-1" />
          Comentarios / Información Adicional
        </h6>
        <v-divider class="mb-1"></v-divider>
      </v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          variant="outlined"
          density="compact"
          color="primary"
          label="Tratamiento"
          placeholder="Medicamentos, terapias..."
        >
          <template #prepend-inner>
            <PillIcon size="18" stroke-width="1.5" />
          </template>
        </v-text-field>
      </v-col>

      <v-col cols="12" sm="6">
        <v-textarea
          variant="outlined"
          density="compact"
          color="primary"
          label="Otros comentarios"
          rows="3"
          placeholder="Información adicional"
        >
          <template #prepend-inner>
            <Message2Icon size="18" stroke-width="1.5" />
          </template>
        </v-textarea>
      </v-col>
    </v-row>
  </v-container>
</template>

<style scoped>
/* Se usan márgenes reducidos */
.mb-1 {
  margin-bottom: 0.5rem !important;
}
.mt-1 {
  margin-top: 0.5rem !important;
}
.text-h6 {
  font-size: 1rem !important;
  font-weight: 600 !important;
}
.mr-1 {
  margin-right: 0.5rem;
}
</style>
