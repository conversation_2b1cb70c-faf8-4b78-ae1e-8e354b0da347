<template>
    <v-container fluid>
      <!-- <PERSON><PERSON>ón Dirección y Contacto -->
      <v-row>
        <!-- Columna: Dirección -->
        <v-col cols="12" sm="6">
          <h6 class="text-h6 mb-2 d-flex align-center">
            <MapPinIcon size="20" stroke-width="1.5" class="mr-2" />
            Dirección
          </h6>
          <v-divider class="mb-3" />
  
          <!-- <PERSON><PERSON> (Select) -->
          <v-select
            variant="outlined"
            density="compact"
            color="primary"
            label="País *"
            :items="['REPÚBLICA DOMINICANA', 'COLOMBIA', 'OTRO']"
          >
            <template #prepend-inner>
              <WorldIcon size="16" stroke-width="1.5" />
            </template>
          </v-select>
  
          <!-- Pro<PERSON>cia (Select) -->
          <v-select
            class="mt-3"
            variant="outlined"
            density="compact"
            color="primary"
            label="Provincia *"
            :items="['SANTO DOMINGO', 'LA VEGA', 'SANTIAGO', 'OTRO']"
          >
            <template #prepend-inner>
              <MapIcon size="16" stroke-width="1.5" />
            </template>
          </v-select>
  
          <!-- Municipio (Select) -->
          <v-select
            class="mt-3"
            variant="outlined"
            density="compact"
            color="primary"
            label="Municipio *"
            :items="['Municipio 1', 'Municipio 2']"
          >
            <template #prepend-inner>
              <MapPinIcon size="16" stroke-width="1.5" />
            </template>
          </v-select>
  
          <!-- Calle y No. (TextField) -->
          <v-text-field
            class="mt-3"
            variant="outlined"
            density="compact"
            color="primary"
            label="Calle y No. *"
            maxlength="200"
            show-size
          >
            <template #prepend-inner>
              <RoadIcon size="16" stroke-width="1.5" />
            </template>
          </v-text-field>
  
          <!-- Teléfono (TextField) -->
          <v-text-field
            class="mt-3"
            variant="outlined"
            density="compact"
            color="primary"
            label="Teléfono"
          >
            <template #prepend-inner>
              <PhoneCallIcon size="16" stroke-width="1.5" />
            </template>
          </v-text-field>
        </v-col>
  
        <!-- Columna: Contacto / Redes Sociales -->
        <v-col cols="12" sm="6">
          <h6 class="text-h6 mb-2 d-flex align-center">
            <PhoneCallIcon size="16" stroke-width="1.5" class="mr-2" />
            Contacto
          </h6>
          <v-divider class="mb-3" />
  
          <!-- Celular -->
          <v-text-field
            variant="outlined"
            density="compact"
            color="primary"
            label="Celular *"
            maxlength="20"
            show-size
          >
            <template #prepend-inner>
              <DeviceMobileIcon size="16" stroke-width="1.5" />
            </template>
          </v-text-field>
  
          <!-- Email con hint y persistent-hint -->
          <v-text-field
            class="mt-3"
            variant="outlined"
            density="compact"
            color="primary"
            label="Email *"
            hint="Utilice un email al que tenga acceso; allí enviaremos su clave."
            persistent-hint
          >
            <template #prepend-inner>
              <MailIcon size="16" stroke-width="1.5" />
            </template>
          </v-text-field>
  
          <!-- Facebook -->
          <v-text-field
            class="mt-3"
            variant="outlined"
            density="compact"
            color="primary"
            label="Facebook"
          >
            <template #prepend-inner>
              <BrandFacebookIcon size="16" stroke-width="1.5" />
            </template>
          </v-text-field>
  
          <!-- Instagram -->
          <v-text-field
            class="mt-3"
            variant="outlined"
            density="compact"
            color="primary"
            label="Instagram"
          >
            <template #prepend-inner>
              <BrandInstagramIcon size="16" stroke-width="1.5" />
            </template>
          </v-text-field>
  
          <!-- X (antes Twitter) -->
          <v-text-field
            class="mt-3"
            variant="outlined"
            density="compact"
            color="primary"
            label="X"
          >
            <template #prepend-inner>
              <BrandXingIcon size="16" stroke-width="1.5" />
            </template>
          </v-text-field>
        </v-col>
      </v-row>
  
      <!-- Sección Contactos Adicionales -->
      <v-row class="mt-5">
        <v-col cols="12">
          <h6 class="text-h6 mb-2">Contactos Adicionales</h6>
          <v-divider class="mb-4" />
  
          <v-row
            v-for="(contacto, index) in contactos"
            :key="index"
            class="mb-3"
            align="center"
          >
            <v-col cols="5">
              <v-text-field
                v-model="contacto.parentesco"
                variant="outlined"
                density="compact"
                color="primary"
                label="Parentesco"
                placeholder="Ej. Madre, Hermano, etc."
              >
                <template #prepend-inner>
                  <UserIcon size="16" stroke-width="1.5" />
                </template>
              </v-text-field>
            </v-col>
  
            <v-col cols="5">
              <v-text-field
                v-model="contacto.numero"
                variant="outlined"
                density="compact"
                color="primary"
                label="Número"
                placeholder="Ingresa el número"
              >
                <template #prepend-inner>
                  <PhoneCallIcon size="16" stroke-width="1.5" />
                </template>
              </v-text-field>
            </v-col>
  
            <v-col cols="2" class="d-flex justify-center">
              <!-- Botón para eliminar contacto: outlined con tamaño small -->
              <v-btn outlined small color="error" @click="eliminarContacto(index)">
                <v-icon size="16">mdi-close</v-icon>
              </v-btn>
            </v-col>
          </v-row>
  
          <!-- Botón para agregar contacto: outlined y small para mayor visibilidad -->
          <div class="text-end">
            <v-btn
              outlined
              color="primary"
              :disabled="contactos.length >= maxContactos"
              @click="agregarContacto"
            >
              <v-icon left size="16">mdi-plus</v-icon>
              Agregar contacto
            </v-btn>
          </div>
  
          <p v-if="contactos.length >= maxContactos" class="caption mt-2 text-center">
            Límite máximo de {{ maxContactos }} contactos alcanzado.
          </p>
        </v-col>
      </v-row>
    </v-container>
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue'
  import {
    MapPinIcon,
    WorldIcon,
    MapIcon,
    RoadIcon,
    PhoneCallIcon,
    DeviceMobileIcon,
    MailIcon,
    BrandFacebookIcon,
    BrandInstagramIcon,
    BrandXingIcon,
    UserIcon
  } from 'vue-tabler-icons'
  
  // Configuración para contactos adicionales
  const maxContactos = 4
  const contactos = ref<Array<{ parentesco: string; numero: string }>>([
    { parentesco: '', numero: '' }
  ])
  
  const agregarContacto = () => {
    if (contactos.value.length < maxContactos) {
      contactos.value.push({ parentesco: '', numero: '' })
    }
  }
  
  const eliminarContacto = (index: number) => {
    contactos.value.splice(index, 1)
  }
  </script>
  
  <style scoped>
  /* Espaciados moderados */
  .mt-3 {
    margin-top: 0.75rem !important;
  }
  .mt-5 {
    margin-top: 1.25rem !important;
  }
  .mb-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-3 {
    margin-bottom: 0.75rem !important;
  }
  .mb-4 {
    margin-bottom: 1rem !important;
  }
  
  /* Estilos de tipografía */
  .text-h6 {
    font-size: 1rem !important;
    font-weight: 600 !important;
  }
  
  .caption {
    color: #757575;
    font-size: 0.85rem;
  }
  </style>
  