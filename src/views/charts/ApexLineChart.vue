<script setup lang="ts">
import { computed } from 'vue';
import { ref } from 'vue';


import { getPrimary, getSecondary } from '@/utils/UpdateColors';
// template breadcrumb
const page = ref({ title: 'Line Chart' });
const breadcrumbs = ref([
    {
        text: 'Dashboard',
        disabled: false,
        href: '#'
    },
    {
        text: 'Line Chart',
        disabled: true,
        href: '#'
    }
]);
const chartOptions = computed(() => {
    return {
        chart: {
            height: 350,
            type: 'line',
            foreColor: '#adb0bb',
            fontFamily: `inherit`,
            offsetX:-5,
            zoom: {
                type: 'x',
                enabled: true
            },
            toolbar: {
                show: false
            },
            shadow: {
                enabled: true,
                color: '#000',
                top: 18,
                left: 7,
                blur: 10,
                opacity: 1
            }
        },
        colors: [getPrimary.value, getSecondary.value],
        markers: {
            size: 1
        },
        xaxis: {
            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
            title: {
                text: 'Month'
            }
        },
        grid: {
            show: false,
            padding:{
                left:20
            }

        },
        dataLabels: {
            enabled: true
        },
        stroke: {
            curve: 'straight',
            width: '2'
        },
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            floating: true,
            offsetY: -25,
            offsetX: -5
        },
        tooltip: {
            theme: 'dark',
            x: {
                format: 'dd/MM/yy HH:mm'
            }
        }
    };
});
const lineChart = {
    series: [
        {
            name: 'High - 2024',
            data: [28, 29, 33, 36, 32, 32, 33]
        },
        {
            name: 'Low - 2024',
            data: [12, 11, 14, 18, 17, 13, 13]
        }
    ]
};
</script>

<template>
    <!-- ---------------------------------------------------- -->
    <!-- Line Chart -->
    <!-- ---------------------------------------------------- -->
    <BaseBreadcrumb :title="page.title" :breadcrumbs="breadcrumbs"></BaseBreadcrumb>
    <v-row>
        <v-col cols="12">
            <UiParentCard title="Line Chart">
                <div class="mx-n2">
                <apexchart type="line"  height="350" :options="chartOptions" :series="lineChart.series"> </apexchart>
            </div>
            </UiParentCard>
        </v-col>
    </v-row>
</template>
