<script setup lang="ts">
import { ref, watch } from 'vue'
import type { SolicitudAdmisionDto, EducacionDto, IdiomaDto } from '@/utils/models/Academico/SolicitudAdmisionDto'
import { SchoolIcon, MapPinIcon, ClockIcon, CertificateIcon, UserIcon, LanguageIcon, BookIcon, PlusIcon } from 'vue-tabler-icons'

// Props
const props = defineProps<{
  item: SolicitudAdmisionDto,
  datosMaestros: any;
  catalogos: any

}>()
const emit = defineEmits(['update:item'])

// EDUCACION - Separación formal y ejecutiva
const formalEducacion = ref<EducacionDto[]>([])
const ejecutivoEducacion = ref<EducacionDto[]>([])

function updateEducacionFilters() {
  formalEducacion.value = props.item.educacion.filter(e => e.tipo === 'Formal')
  ejecutivoEducacion.value = props.item.educacion.filter(e => e.tipo === 'Ejecutiva')
}
watch(() => props.item.educacion, updateEducacionFilters, { immediate: true, deep: true })

// Formal Education Modal
const showFormalModal = ref(false)
const isEditingFormal = ref(false)
const editingIndexFormal = ref(-1)
const nuevaInstitucion = ref<EducacionDto>({ tipo: 'Formal', id: 0, centroEstudio: '', nivelEstudio: '', titulo: '', anio: undefined, promedio: undefined, cursoNombre: '', cursoFecha: '', solicitudId: props.item.id ?? 0 })

function openFormalModal(edit = false, index = -1) {
  isEditingFormal.value = edit
  editingIndexFormal.value = index
  nuevaInstitucion.value = edit && index >= 0 ? { ...formalEducacion.value[index] } : { tipo: 'Formal', id: 0, centroEstudio: '', nivelEstudio: '', titulo: '', anio: undefined, promedio: undefined, cursoNombre: '', cursoFecha: '', solicitudId: props.item.id ?? 0 }
  showFormalModal.value = true
}
function saveInstitucion() {
  if (!nuevaInstitucion.value.centroEstudio || !nuevaInstitucion.value.titulo) return
  if (isEditingFormal.value && editingIndexFormal.value >= 0) {
    const realIndex = props.item.educacion.findIndex((e, i) => e.tipo === 'Formal' && i === editingIndexFormal.value)
    if (realIndex >= 0) props.item.educacion[realIndex] = { ...nuevaInstitucion.value }
  } else {
    props.item.educacion.push({ ...nuevaInstitucion.value })
  }
  updateEducacionFilters()
  showFormalModal.value = false
  emit('update:item', { ...props.item })
}
function removeInstitucion(index: number) {
  const formalItem = formalEducacion.value[index]
  const realIndex = props.item.educacion.findIndex(e => e.tipo === 'Formal' && e.centroEstudio === formalItem.centroEstudio && e.titulo === formalItem.titulo)
  if (realIndex >= 0) props.item.educacion.splice(realIndex, 1)
  updateEducacionFilters()
  emit('update:item', { ...props.item })
}

// EJECUTIVO
const showEjecutivoModal = ref(false)
const isEditingEjecutivo = ref(false)
const editingIndexEjecutivo = ref(-1)
const nuevoEjecutivo = ref<EducacionDto>({ tipo: 'Ejecutiva', centroEstudio: '', nivelEstudio: '', titulo: '', anio: undefined, promedio: undefined, cursoNombre: '', cursoFecha: '', solicitudId: props.item.id ?? 0 })
function openEjecutivoModal(edit = false, index = -1) {
  isEditingEjecutivo.value = edit
  editingIndexEjecutivo.value = index
  nuevoEjecutivo.value = edit && index >= 0 ? { ...ejecutivoEducacion.value[index] } : { tipo: 'Ejecutiva', centroEstudio: '', nivelEstudio: '', titulo: '', anio: undefined, promedio: undefined, cursoNombre: '', cursoFecha: '', solicitudId: props.item.id ?? 0 }
  showEjecutivoModal.value = true
}
function saveEjecutivo() {
  if (!nuevoEjecutivo.value.centroEstudio || !nuevoEjecutivo.value.titulo) return
  if (isEditingEjecutivo.value && editingIndexEjecutivo.value >= 0) {
    const realIndex = props.item.educacion.findIndex((e, i) => e.tipo === 'Ejecutiva' && i === editingIndexEjecutivo.value)
    if (realIndex >= 0) props.item.educacion[realIndex] = { ...nuevoEjecutivo.value }
  } else {
    props.item.educacion.push({ ...nuevoEjecutivo.value })
  }
  updateEducacionFilters()
  showEjecutivoModal.value = false
  emit('update:item', { ...props.item })
}
function removeEjecutivo(index: number) {
  const ejecutivoItem = ejecutivoEducacion.value[index]
  const realIndex = props.item.educacion.findIndex(e => e.tipo === 'Ejecutiva' && e.centroEstudio === ejecutivoItem.centroEstudio && e.titulo === ejecutivoItem.titulo)
  if (realIndex >= 0) props.item.educacion.splice(realIndex, 1)
  updateEducacionFilters()
  emit('update:item', { ...props.item })
}

// IDIOMAS
const showIdiomaModal = ref(false)
const isEditingIdioma = ref(false)
const editingIndexIdioma = ref(-1)
const nuevoIdioma = ref<IdiomaDto>({ idIdioma: 0, puedeLeer: false, puedeEscribir: false, puedeHablar: false, nivelLectura: 0, nivelEscritura: 0, nivelConversacion: 0 })
function openIdiomaModal(edit = false, index = -1) {
  console.log('openIdiomaModal', edit, index)
  isEditingIdioma.value = edit
  editingIndexIdioma.value = index
  nuevoIdioma.value = edit && index >= 0 ? { ...props.item.idiomas[index] } : { idIdioma: 0, puedeLeer: false, puedeEscribir: false, puedeHablar: false, nivelLectura: 0, nivelEscritura: 0, nivelConversacion: 0 }
  showIdiomaModal.value = true
}
function saveIdioma() {
  if (!nuevoIdioma.value.idIdioma) return
  if (isEditingIdioma.value && editingIndexIdioma.value >= 0) props.item.idiomas[editingIndexIdioma.value] = { ...nuevoIdioma.value }
  else props.item.idiomas.push({ ...nuevoIdioma.value })
  showIdiomaModal.value = false
  emit('update:item', { ...props.item })
}
function removeIdioma(index: number) {
  props.item.idiomas.splice(index, 1)
  emit('update:item', { ...props.item })
}

// Helpers
const idiomaCatalogo = ref(props.datosMaestros.idiomas || [])
const nivelesIdioma = [
  { title: 'Básico', value: 1 },
  { title: 'Intermedio', value: 2 },
  { title: 'Avanzado', value: 3 }
]
function getNombreIdioma(idIdioma: number) {
  const idioma = idiomaCatalogo.value.find((i: { id: number; }) => i.id === idIdioma)
  return idioma ? idioma.descripcion : 'Desconocido'
}
function getNivelTexto(nivel: number) {
  return nivelesIdioma.find(n => n.value === nivel)?.title || '-'
}
</script>

<template>
  <v-card class="pa-6 mb-4" elevation="2">
    <v-row>
      <v-col cols="12">
        <v-card class="pa-6 mb-6" elevation="2">
          <h6 class="text-h6 mb-4 d-flex align-center">
            <SchoolIcon size="24" stroke-width="1.5" class="mr-2"/>
            Información Académica Principal
          </h6>
          <v-row>
            <v-col cols="12" sm="6">
              <v-autocomplete class="mt-4" variant="outlined" color="primary" label="Recinto *" 
              v-model="item.campusId"
              :items="props.datosMaestros.sedes"
               item-value="id"
              item-title="descripcion" 
              :search-input.sync="catalogos.sedesSearch"
              @update:search="catalogos.buscarSedes"
              
              >
                <template #prepend-inner><MapPinIcon size="20" stroke-width="1.5"/></template>
              </v-autocomplete>
            </v-col>
            <v-col cols="12" sm="6">
              <v-autocomplete class="mt-4" variant="outlined" color="primary" label="Tanda / Horario *" :items="['MATUTINA', 'VESPERTINA', 'NOCTURNA']" v-model="item.tratamiento" >
                <template #prepend-inner><ClockIcon size="20" stroke-width="1.5"/></template>
              </v-autocomplete>
            </v-col>
            <v-col cols="12" sm="6">
              <v-autocomplete variant="outlined" color="primary" label="Tipo de Carrera *"
               v-model="item.modalidadCarreraId"
               :items="props.datosMaestros.tiposCarrera" 
              item-value="id" item-title="descripcion" 

              :search-input.sync="catalogos.tiposCarreraSearch"
              @update:search="catalogos.buscarTiposCarrera"
              >
                <template #prepend-inner><CertificateIcon size="20" stroke-width="1.5"/></template>
              </v-autocomplete>
            </v-col>
            <v-col cols="12" sm="6">
              <v-autocomplete variant="outlined" color="primary" label="Programa / Carrera *" placeholder="Ej: MAESTRÍA EN SALUD PÚBLICA" :items="props.datosMaestros.carreras" item-value="id" item-title="descripcion" v-model="item.carreraId">
                <template #prepend-inner><UserIcon size="20" stroke-width="1.5"/></template>
              </v-autocomplete>
            </v-col>
          </v-row>
        </v-card>
      </v-col>

      <!-- Educación Formal -->
      <v-col cols="12">
        <v-card class="pa-6 mb-6" elevation="2">
          <h6 class="text-h6 mb-4 d-flex align-center">
            <SchoolIcon size="24" stroke-width="1.5" class="mr-2"/>
            Educación Formal
          </h6>
          <v-btn color="success" class="mb-4" @click="openFormalModal()">
            <PlusIcon size="20" class="mr-2"/> Agregar
          </v-btn>
          <v-data-table
            v-if="formalEducacion.length > 0"
            :headers="[
              { title: 'Nombre Institución', value: 'centroEstudio' },
              { title: 'Nivel', value: 'nivelEstudio' },
              { title: 'Título', value: 'titulo' },
              { title: 'Año', value: 'anio' },
              { title: 'Índice', value: 'promedio' },
              { title: 'Acciones', value: 'actions', sortable: false }
            ]"
            :items="formalEducacion"
            :items-per-page="10"
            class="elevation-0"
          >
            <template #item.actions="{ index }">
              <v-btn icon flat @click="openFormalModal(true, index)">
                <v-icon color="primary">mdi-pencil</v-icon>
              </v-btn>
              <v-btn icon flat @click="removeInstitucion(index)">
                <v-icon color="error" size="18">mdi-delete</v-icon>
              </v-btn>
            </template>
          </v-data-table>
          <v-dialog v-model="showFormalModal" max-width="800px" persistent>
            <v-card class="pa-6">
              <v-card-title class="d-flex align-center">
                <SchoolIcon size="24" class="mr-2"/>
                {{ isEditingFormal ? 'Editar Institución' : 'Agregar Institución' }}
              </v-card-title>
              <v-card-text>
                <v-row>
                  <v-col cols="12">
                    <v-text-field v-model="nuevaInstitucion.centroEstudio" label="Nombre Institución *" variant="outlined" prepend-inner-icon="mdi-school" class="mb-2" />
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-text-field v-model="nuevaInstitucion.nivelEstudio" label="Nivel" variant="outlined" prepend-inner-icon="mdi-school-outline" class="mb-2" />
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-text-field v-model="nuevaInstitucion.titulo" label="Título" variant="outlined" prepend-inner-icon="mdi-certificate-outline" class="mb-2" />
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-text-field v-model="nuevaInstitucion.anio" label="Año" type="number" variant="outlined" prepend-inner-icon="mdi-calendar" class="mb-2" />
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-text-field v-model="nuevaInstitucion.promedio" label="Índice" type="number" variant="outlined" prepend-inner-icon="mdi-counter" class="mb-2" />
                  </v-col>
                </v-row>
              </v-card-text>
              <v-card-actions class="pa-4">
                <v-spacer/>
                <v-btn color="error" variant="flat" @click="showFormalModal = false">Cancelar</v-btn>
                <v-btn color="primary" variant="flat" @click="saveInstitucion">Guardar</v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </v-card>
      </v-col>
       <!-- Educación Ejecutiva -->
      <v-col cols="12">
        <v-card class="pa-6 mb-6" elevation="2">
          <h6 class="text-h6 mb-4 d-flex align-center">
            <BookIcon size="24" stroke-width="1.5" class="mr-2"/>
            Educación Ejecutiva / Cursos
          </h6>
          <v-btn color="warning" class="mb-4" @click="openEjecutivoModal()">
            <PlusIcon size="20" class="mr-2"/> Agregar Curso
          </v-btn>
          <v-data-table
            v-if="ejecutivoEducacion.length > 0"
            :headers="[
              { title: 'Título/Curso', value: 'cursoNombre' },
              { title: 'Fecha', value: 'cursoFecha' },
              { title: 'Acciones', value: 'actions', sortable: false }
            ]"
            :items="ejecutivoEducacion"
            :items-per-page="10"
            class="elevation-0"
          >
            <template #item.cursoFecha="{ item }">
              <span v-if="item.cursoFecha">{{ new Date(item.cursoFecha).toLocaleDateString('es-ES') }}</span>
              <span v-else class="text-grey">-</span>
            </template>
            <template #item.actions="{ index }">
              <v-btn icon flat @click="openEjecutivoModal(true, index)">
                <v-icon color="primary">mdi-pencil</v-icon>
              </v-btn>
              <v-btn icon flat @click="removeEjecutivo(index)">
                <v-icon color="error" size="18">mdi-delete</v-icon>
              </v-btn>
            </template>
          </v-data-table>
          <!-- Modal ejecutiva... igual que antes, puedes usar el tuyo si ya funciona bien -->
          <v-dialog v-model="showEjecutivoModal" max-width="600px" persistent>
            <v-card class="pa-6">
              <v-card-title class="d-flex align-center">
                <BookIcon size="24" class="mr-2"/>
                {{ isEditingEjecutivo ? 'Editar Curso' : 'Agregar Curso' }}
              </v-card-title>
              <v-card-text>
                <v-row>
                  <v-col cols="12">
                    <v-text-field v-model="nuevoEjecutivo.cursoNombre" label="Curso*" variant="outlined" prepend-inner-icon="mdi-school" class="mb-2" />
                  </v-col>
                  <v-col cols="12">
                    <v-text-field v-model="nuevoEjecutivo.cursoFecha" label="Fecha Curso *" variant="outlined" prepend-inner-icon="mdi-book-open-page-variant" class="mb-2" />
                  </v-col>
                </v-row>
              </v-card-text>
              <v-card-actions class="pa-4">
                <v-spacer/>
                <v-btn color="error" variant="flat" @click="showEjecutivoModal = false">Cancelar</v-btn>
                <v-btn color="primary" variant="flat" @click="saveEjecutivo">Guardar</v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </v-card>
      </v-col>

      <!-- Idiomas -->
      <v-col cols="12">
        <v-card class="pa-6" elevation="2">
          <h6 class="text-h6 mb-4 d-flex align-center">
            <LanguageIcon size="24" stroke-width="1.5" class="mr-2"/>
            Idiomas
          </h6>
          <v-btn color="success" class="mb-4" @click="openIdiomaModal()">
            <PlusIcon size="20" class="mr-2"/> Agregar
          </v-btn>
          <v-data-table
            v-if="item.idiomas.length > 0"
            :headers="[
              { title: 'Idioma', value: 'idIdioma' },
              { title: 'Leer', value: 'puedeLeer' },
              { title: 'Escribir', value: 'puedeEscribir' },
              { title: 'Hablar', value: 'puedeHablar' },
              { title: 'Nivel Lectura', value: 'nivelLectura' },
              { title: 'Nivel Escritura', value: 'nivelEscritura' },
              { title: 'Nivel Conversación', value: 'nivelConversacion' },
              { title: 'Acciones', value: 'actions', sortable: false }
            ]"
            :items="item.idiomas"
            :items-per-page="10"
            class="elevation-0"
          >
            <template #item.idIdioma="{ item }">
              <v-chip color="primary" text-color="white" size="small" label>
                {{ item.idIdioma ? getNombreIdioma(item.idIdioma) : '-' }}
              </v-chip>
            </template>
            <template #item.puedeLeer="{ item }">
              <v-icon :color="item.puedeLeer ? 'success' : 'grey'">{{ item.puedeLeer ? 'mdi-check' : 'mdi-close' }}</v-icon>
            </template>
            <template #item.puedeEscribir="{ item }">
              <v-icon :color="item.puedeEscribir ? 'success' : 'grey'">{{ item.puedeEscribir ? 'mdi-check' : 'mdi-close' }}</v-icon>
            </template>
            <template #item.puedeHablar="{ item }">
              <v-icon :color="item.puedeHablar ? 'success' : 'grey'">{{ item.puedeHablar ? 'mdi-check' : 'mdi-close' }}</v-icon>
            </template>
            <template #item.nivelLectura="{ item }">
              <v-chip :color="item.nivelLectura === 3 ? 'success' : item.nivelLectura === 2 ? 'warning' : 'info'" text-color="white" size="small">
                {{ getNivelTexto(item.nivelLectura ?? 0) }}
              </v-chip>
            </template>
            <template #item.nivelEscritura="{ item }">
              <v-chip :color="item.nivelEscritura === 3 ? 'success' : item.nivelEscritura === 2 ? 'warning' : 'info'" text-color="white" size="small">
                {{ getNivelTexto(item.nivelEscritura ?? 0) }}
              </v-chip>
            </template>
            <template #item.nivelConversacion="{ item }">
              <v-chip :color="item.nivelConversacion === 3 ? 'success' : item.nivelConversacion === 2 ? 'warning' : 'info'" text-color="white" size="small">
                {{ getNivelTexto(item.nivelConversacion ?? 0) }}
              </v-chip>
            </template>
            <template #item.actions="{ index }">
              <v-btn icon flat @click="openIdiomaModal(true, index)">
                <v-icon color="primary">mdi-pencil</v-icon>
              </v-btn>
              <v-btn icon flat @click="removeIdioma(index)">
                <v-icon color="error" size="18">mdi-delete</v-icon>
              </v-btn>
            </template>
          </v-data-table>
          <!-- Modal idiomas... igual que antes, puedes usar el tuyo si ya funciona bien -->
           <!-- Modal para agregar/editar idioma -->
<v-dialog v-model="showIdiomaModal" max-width="600px" persistent>
  <v-card>
    <v-card-title class="d-flex align-center pa-6 pb-4">
      <LanguageIcon size="24" class="mr-3" />
      <span class="text-h5">{{ isEditingIdioma ? 'Editar Idioma' : 'Agregar Idioma' }}</span>
    </v-card-title>
    <v-card-text class="pa-6">
      <v-row>
        <!-- Selección de idioma -->
        <v-col cols="12">
          <v-select
            v-model="nuevoIdioma.idIdioma"
            :items="idiomaCatalogo"
            item-title="descripcion"
            item-value="id"
            label="Seleccionar Idioma *"
            variant="outlined"
            prepend-inner-icon="mdi-translate"
            :rules="[v => !!v || 'Seleccione un idioma']"
          />
        </v-col>
        <!-- Habilidades -->
        <v-col cols="12">
          <v-card variant="outlined" class="pa-4 mb-4">
            <v-card-subtitle class="text-subtitle-1 font-weight-medium mb-3">
              <CheckIcon size="18" class="mr-2" />
              Habilidades
            </v-card-subtitle>
            <v-row>
              <v-col cols="12" sm="4">
                <v-checkbox v-model="nuevoIdioma.puedeLeer" label="Puede Leer" color="primary" hide-details />
              </v-col>
              <v-col cols="12" sm="4">
                <v-checkbox v-model="nuevoIdioma.puedeEscribir" label="Puede Escribir" color="primary" hide-details />
              </v-col>
              <v-col cols="12" sm="4">
                <v-checkbox v-model="nuevoIdioma.puedeHablar" label="Puede Hablar" color="primary" hide-details />
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <!-- Niveles de competencia -->
        <v-col cols="12">
          <v-card variant="outlined" class="pa-4">
            <v-card-subtitle class="text-subtitle-1 font-weight-medium mb-3">
              <LanguageIcon size="18" class="mr-2" />
              Niveles de Competencia
            </v-card-subtitle>
            <v-row>
              <v-col cols="12" sm="4">
                <v-select
                  v-model="nuevoIdioma.nivelLectura"
                  :items="nivelesIdioma"
                  label="Nivel de Lectura"
                  variant="outlined"
                  prepend-inner-icon="mdi-book-open-variant"
                  :disabled="!nuevoIdioma.puedeLeer"
                  :rules="[v => (!nuevoIdioma.puedeLeer || !!v) || 'Seleccione el nivel']"
                />
              </v-col>
              <v-col cols="12" sm="4">
                <v-select
                  v-model="nuevoIdioma.nivelEscritura"
                  :items="nivelesIdioma"
                  label="Nivel de Escritura"
                  variant="outlined"
                  prepend-inner-icon="mdi-pencil"
                  :disabled="!nuevoIdioma.puedeEscribir"
                  :rules="[v => (!nuevoIdioma.puedeEscribir || !!v) || 'Seleccione el nivel']"
                />
              </v-col>
              <v-col cols="12" sm="4">
                <v-select
                  v-model="nuevoIdioma.nivelConversacion"
                  :items="nivelesIdioma"
                  label="Nivel de Conversación"
                  variant="outlined"
                  prepend-inner-icon="mdi-account-voice"
                  :disabled="!nuevoIdioma.puedeHablar"
                  :rules="[v => (!nuevoIdioma.puedeHablar || !!v) || 'Seleccione el nivel']"
                />
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <!-- Nota informativa -->
        <v-col cols="12">
          <v-alert type="info" variant="tonal" class="mt-2" density="compact">
            <template #prepend>
              <v-icon>mdi-information</v-icon>
            </template>
            <strong>Nota:</strong> Los niveles solo se activan cuando la habilidad está seleccionada.
          </v-alert>
        </v-col>
      </v-row>
    </v-card-text>
    <v-card-actions class="pa-6 pt-0">
      <v-spacer />
      <v-btn color="grey" variant="outlined" @click="showIdiomaModal = false">
        <v-icon left>mdi-close</v-icon>Cancelar
      </v-btn>
      <v-btn
        color="primary"
        variant="flat"
        @click="saveIdioma"
        :disabled="!nuevoIdioma.idIdioma"
      >
        <v-icon left>mdi-content-save</v-icon>Guardar
      </v-btn>
    </v-card-actions>
  </v-card>
</v-dialog>

        </v-card>
      </v-col>

      <!-- CRM (CrmDto) -->
      <v-col cols="12">
        <v-card class="pa-6 mb-6" elevation="2">
          <h6 class="text-h6 mb-4 d-flex align-center">
            <UserIcon size="24" stroke-width="1.5" class="mr-2"/>
            Información CRM
          </h6>
          <v-data-table
            :headers=" [
              { title: 'Cohorte', value: 'cohorteNombre' },
              { title: 'Programa', value: 'nombrePrograma' },
              { title: 'Código Cohorte', value: 'codigoCohortePrograma' },
              { title: 'Contacto CRM', value: 'idContactoCrm' },
              { title: 'Oportunidad CRM', value: 'idOportunidadCrm' },
              { title: 'Estudiante SISBBA', value: 'idEstudianteSisbba' }
            ]"
            :items="props.item.infoCrm || []"
            :items-per-page="5"
            class="elevation-0"
            hide-default-footer
          />
        </v-card>
      </v-col>
    </v-row>
  </v-card>
</template>

<style scoped>
.v-card {
  border-radius: 8px;
  background: var(--v-theme-surface);
  color: var(--v-theme-on-surface);
  transition: box-shadow 0.3s, background 0.3s, color 0.3s;
}
.v-card:hover {
  box-shadow: 0 6px 12px rgba(0,0,0,0.08)!important;
}
.text-h6 {
  font-weight: 500;
  color: var(--v-theme-on-surface);
}
.v-data-table {
  background: transparent !important;
  border: 1px solid var(--v-theme-outline-variant, #4444);
  border-radius: 6px;
}
.v-data-table th, .v-data-table td {
  font-size: 13px !important;
  color: var(--v-theme-on-surface);
}
.v-data-table th {
  font-weight: 600 !important;
  color: var(--v-theme-on-surface) !important;
  border-bottom: 1px solid var(--v-theme-outline-variant, #4444) !important;
}
.v-data-table td {
  border-bottom: 1px solid var(--v-theme-outline-variant, #4444);
}
.v-data-table tbody tr:hover {
  background: var(--v-theme-surface-variant, #232323);
}
.v-dialog .v-card {
  border-radius: 12px;
  background: var(--v-theme-surface);
  color: var(--v-theme-on-surface);
}
.v-card-title {
  font-size: 1.12rem;
  font-weight: 500;
  background: var(--v-theme-surface-variant, #232323);
  border-bottom: 1px solid var(--v-theme-outline-variant, #4444);
  padding: 16px 24px;
  color: var(--v-theme-on-surface);
}
.v-card-text {
  padding: 24px !important;
}
.v-card-actions {
  background: var(--v-theme-surface-variant, #232323);
  border-top: 1px solid var(--v-theme-outline-variant, #4444);
  padding: 16px 24px;
}
.mt-4 { margin-top: 1rem !important; }
.mb-4 { margin-bottom: 1rem !important; }
.mb-6 { margin-bottom: 1.5rem !important; }
.mr-2 { margin-right: 0.5rem !important; }
.pa-6 { padding: 1.5rem !important; }
@media (max-width: 600px) {
  .v-data-table th, .v-data-table td { font-size: 11px !important; padding: 6px !important; }
  .v-card { padding: 1rem !important; }
}
</style>
