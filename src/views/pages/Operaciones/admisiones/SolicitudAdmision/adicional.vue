<script setup lang="ts">
  import type { SolicitudAdmisionDto, EducacionDto, IdiomaDto } from '@/utils/models/Academico/SolicitudAdmisionDto'
  import { ref } from 'vue'
  import { defineExpose } from 'vue'
  import { HelpCircleIcon, BroadcastIcon, TargetIcon } from 'vue-tabler-icons'
  
  // Opciones predefinidas para "¿Cómo se enteró de nosotros?"
  const enteradoOpciones = [
    'Internet',
    'Recomendación',
    'Publicidad',
    'Redes Sociales',
    'Otro'
  ]
  
  // Modelo para cada campo
  const comoEntero = ref('')
  const motivo = ref('')

// al editar tomamos los datos del componente padre
const props = defineProps<{
  item: SolicitudAdmisionDto,
  datosMaestros: any;
  catalogos: any

}>()


defineEmits(['update:item'])
  </script>
<template>
  <v-container fluid>
    <v-row>
      <!-- Encabezado de la Sección Adicional -->
      <v-col cols="12">
        <h6 class="text-h6 mb-2 d-flex align-center">
          <HelpCircleIcon size="20" stroke-width="1.5" class="mr-2"/>
          Información Adicional
        </h6>
        <v-divider class="mb-3" />
      </v-col>
  
      <!-- Campo: ¿Cómo se enteró de nosotros? -->
      <v-col cols="12" sm="6">
        <v-combobox
          variant="outlined"
          color="primary"
          label="¿Cómo se enteró de nosotros? *"
          :items="props.datosMaestros.redesSociales"
          item-value="id"
          item-title="descripcion"
          v-model="item.redSocialId"
         :search-input.sync="catalogos.searchTextCanales"
          @update:search="catalogos.buscarCanales"

          clearable
        >
          <template #prepend-inner>
            <BroadcastIcon size="20" stroke-width="1.5" />
          </template>
        </v-combobox>
      </v-col>
  
      <!-- Campo: ¿Cuál fue su motivo para elegirnos? -->
      <v-col cols="12" sm="6">
        <v-textarea
          variant="outlined"
          color="primary"
          label="¿Cuál fue su motivo para elegirnos? *"
          placeholder="Escriba su motivo aquí..."
          maxlength="300"
          counter="300"
          rows="5"
          v-model="item.objetivoIngreso"
        >
          <template #prepend-inner>
            <TargetIcon size="20" stroke-width="1.5" />
          </template>
        </v-textarea>
      </v-col>
    </v-row>
  </v-container>
  </template>
  

  <style scoped>
  .mb-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-3 {
    margin-bottom: 1rem !important;
  }
  .mr-2 {
    margin-right: 0.5rem;
  }
  .text-h6 {
    font-size: 1rem;
    font-weight: 500;
  }
  </style>
  