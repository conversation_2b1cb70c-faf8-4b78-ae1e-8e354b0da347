
<script setup lang="ts">
import {
  BriefcaseIcon,
  BuildingSkyscraperIcon,
  UserIcon,
  PhoneCallIcon,
  MapPinIcon
} from 'vue-tabler-icons'
import type { SolicitudAdmisionDto } from '@/utils/models/Academico/SolicitudAdmisionDto'

const emit = defineEmits(['update:item'])
const props = defineProps<{
  item: SolicitudAdmisionDto
  datosMaestros: any
}>()
</script>

<template>
    <v-card class="pa-6 mb-4" elevation="2">
    <v-row>
      <v-col cols="12" sm="6">
        <v-text-field 
          :model-value="props.item.empresaTrabajo"
          variant="outlined" 
          density="compact" 
          color="primary" 
          label="Nombre de la Empresa"
          maxlength="100"
          counter
          placeholder="Ej: Banco Popular Dominicano"
        >
          <template #prepend-inner>
            <BuildingSkyscraperIcon size="16" stroke-width="1.5" />
          </template>
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field 
          :model-value="props.item.cargoTrabajo"
          variant="outlined" 
          density="compact" 
          color="primary" 
          label="Cargo/Posición"
          maxlength="80"
          counter
          placeholder="Ej: Analista de Sistemas"
        >
          <template #prepend-inner>
            <UserIcon size="16" stroke-width="1.5" />
          </template>
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field 
          :model-value="item.telefonoTrabajo"
          variant="outlined" 
          density="compact" 
          color="primary" 
          label="Teléfono del Trabajo"
          maxlength="15"
          placeholder="Ej: (************* ext. 123"
        >
          <template #prepend-inner>
            <PhoneCallIcon size="16" stroke-width="1.5" />
          </template>
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-textarea 
          :model-value="item.direccionTrabajo"
          variant="outlined" 
          density="compact" 
          color="primary" 
          label="Dirección del Trabajo"
          rows="3"
          maxlength="200"
          counter
          placeholder="Ej: Av. John F. Kennedy Km. 7½, Torre Empresarial, Piso 5"
        >
          <template #prepend-inner>
            <MapPinIcon size="16" stroke-width="1.5" />
          </template>
        </v-textarea>
      </v-col>
    </v-row>
    </v-card>
</template>

