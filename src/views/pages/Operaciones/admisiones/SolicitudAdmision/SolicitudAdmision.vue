<script setup lang="ts">
import { ref, onMounted, computed, watch } from "vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { SolicitudAdmisionDto } from '@/utils/models/Academico/SolicitudAdmisionDto';
import wTabsCard from "@/components/apps/wTabsCard.vue";
import Generales from "./generales.vue";
import Personal from "./personal.vue";
import Laboral from "./laboral.vue";
import Academico from "./academico.vue";
import Adicional from "./adicional.vue";
import Pregunta from "./pregunta.vue";
import Documentos from "./documentos.vue";
import WFormPanel from "@/components/apps/WFormPanel.vue";
import wFormData from "@/components/apps/wFormData.vue";
import wDialogMessage from "@/components/apps/wDialogMessage.vue";


// Servicios existentes para la carga de datos maestros
import SolicitudAdmisionService from '@/services/Academicos/SolicitudAdmisionService';
import paisService from '@/services/Generales/PaisesServices';	
import provinciaService from '@/services/Generales/ProvinciaServices';
import sexoService from '@/services/Generales/SexoServices';
import estadoCivilService from '@/services/Generales/EstadoCivilServices';
import tipoDocumentoService from '@/services/Generales/TipoDocumentoIdentificacionService';
import nacionalidadService from '@/services/Generales/NacionalidadService';
import estadoCiudadanoService from '@/services/Generales/EstadoCiudadano';
import carreraService from '@/services/Academicos/CarreraService';
import formaPagoService from '@/services/Financieros/formaPagoFinancierosService';
const formasPago = formaPagoService;
import canalEnteradoService from '@/services/Generales/MotivoService';
import admitidoComoService from '@/services/Academicos/AdmitidoComoService';
import monedaService from '@/services/Financieros/monedaPagoFinancierosService';
import documentoService from '@/services/Academicos/DocumentosService';
import redSocialService from '@/services/Generales/RedSocialService';
import estadosSolicitudAmisionService from '@/services/Academicos/EstadoSolictiudAdmisionService';
import SedesServices from "@/services/Planta_Fisica/SedesServices";
import idiomasServices from "@/services/Generales/IdiomasServices";
import documentosService from "@/services/Academicos/DocumentosService";
import tipoCarreraService from "@/services/Academicos/TipoCarreraService";
import TipoSangreService from "@/services/Generales/TipoSangreServices";



const headers = [
  { title: 'Código', key: 'id' },
  { title: 'Nombre', key: 'nombreCompleto' },
  { title: 'Correo', key: 'correoElectronico' },
  { title: 'Carrera', key: 'carreraNombre' },
  { title: 'Estado', key: 'estadoSolicitudNombre' },
   { title: 'Fecha Admision', key: 'fechaRegistroFormateada' },
  { title: 'Acciones', key: 'acciones', sortable: false }
];

const tabs = [
  { label: "Personal", value: "personal", icon: "mdi-account" },
  { label: "Generales", value: "generales", icon: "mdi-account-box" },
  { label: "Laboral", value: "laboral", icon: "mdi-briefcase" },
  { label: "Académico", value: "academico", icon: "mdi-school" },
  { label: "Adicional", value: "adicional", icon: "mdi-note-text" },
  { label: "Preguntas", value: "pregunta", icon: "mdi-help-circle" },
  { label: "Documentos", value: "documentos", icon: "mdi-file-document" }
];


const solicitudes = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

// Datos maestros
interface DatosMaestros {
  paises: any[];
  provincias: any[];
  sexos: any[];
  estadosCiviles: any[];
  tiposDocumento: any[];
  nacionalidades: any[];
  estadosCiudadanos: any[];
  carreras: any[];
  formasPago: any[];
  canales: any[];
  admitidoComo: any[];
  monedas: any[];
  documentosRequeridos: any[];
  redesSociales: any[];
  estadosSolicitud: any[];
  sedes: any[];
  idiomas: any[];
  documentos: any[];
  tiposCarrera: any[];
  preguntas?: any[]; // Opcional, si se usa en el futuro
  tiposSangre: any[]

}

const datosMaestros = ref<DatosMaestros>({
  paises: [],
  provincias: [],
  sexos: [],
  estadosCiviles: [],
  tiposDocumento: [],
  nacionalidades: [],
  estadosCiudadanos: [],
  carreras: [],
  formasPago: [],
  canales: [],
  admitidoComo: [],
  monedas: [],
  documentosRequeridos: [],
  redesSociales: [],
  estadosSolicitud: [],
  sedes: [] ,// Inicializado como vacío
  idiomas: [], // Puedes inicializarlo si lo necesitas
  documentos: [] ,// Puedes inicializarlo si lo necesitas
  tiposCarrera: [], // Opcional, si se usa en el futuro
  preguntas: [], // Opcional, si se usa en el futuro
  tiposSangre: []
})

const searchTextPaises = ref("");
const loadingPaises = ref(false);

const searchTextProvincias = ref("");
const loadingProvincia = ref(false);

const searchTextSexos = ref("");
const loadingSexos = ref(false);

const searchTextEstadosCiviles = ref("");
const loadingEstadosCiviles = ref(false);

const searchTextTiposDocumento = ref("");
const loadingTiposDocumento = ref(false);

const searchTextNacionalidades = ref("");
const loadingNacionalidad = ref(false);

const searchTextEstadosCiudadanos = ref("");
const loadingEstadosCiudadanos = ref(false);

const searchTextCarreras = ref("");
const loadingCarreras = ref(false);

const searchTextFormasPago = ref("");
const loadingFormasPago = ref(false);

const searchTextCanales = ref("");
const loadingCanales = ref(false);

const searchTextAdmitidoComo = ref("");
const loadingAdmitidoComo = ref(false);

const searchTextMonedas = ref("");
const loadingMonedas = ref(false);

const searchTextDocumentosRequeridos = ref("");
const loadingDocumentosRequeridos = ref(false);

const searchTextRedesSociales = ref("");
const loadingRedesSociales = ref(false);

const searchTextEstadosSolicitud = ref("");
const loadingEstadosSolicitud = ref(false);

const searchTextSedes = ref("");
const loadingSedes = ref(false);

const searchTextIdiomas = ref("");
const loadingIdiomas = ref(false);

const searchTextDocumentos = ref("");
const loadingDocumentos = ref(false);

const searchTextTiposCarrera = ref("");
const loadingTiposCarrera = ref(false);

const searthTextTiposSangre  = ref("");
const loadingTipoosSangre = ref(false);


// Funciones de búsqueda remota para cada catálogo
async function buscarPaises(texto = "") {
  const res = await paisService.getPaises(texto, 1, 10);
  datosMaestros.value.paises = agregarSeleccione(res.items, 'Seleccione país...');
}

async function buscarProvincias(texto = "") {
  const res = await provinciaService.getProvincias(texto, 1, 10);
  datosMaestros.value.provincias = agregarSeleccione(res.items, 'Seleccione provincia...');
}

async function buscarSexos(texto = "") {
  const res = await sexoService.getSexo(texto, 1, 10);
  datosMaestros.value.sexos = agregarSeleccione(res.items, 'Seleccione sexo...');
}

async function buscarEstadosCiviles(texto = "") {
  const res = await estadoCivilService.getEstadosCiviles(texto, 1, 10);
  datosMaestros.value.estadosCiviles = agregarSeleccione(res.items, 'Seleccione estado civil...');
}

async function buscarTiposDocumento(texto = "") {
  const res = await tipoDocumentoService.getTiposDocumentoIdentificacion(texto, 1, 10);
  datosMaestros.value.tiposDocumento = agregarSeleccione(res.items, 'Seleccione tipo de documento...');
}

async function buscarNacionalidades(texto = "") {
  const res = await nacionalidadService.getNacionalidades(texto, 1, 10);
  datosMaestros.value.nacionalidades = agregarSeleccione(res.items, 'Seleccione nacionalidad...');
}

async function buscarEstadosCiudadanos(texto = "") {
  const res = await estadoCiudadanoService.getEstadosCiudadano(texto, 1, 10);
  datosMaestros.value.estadosCiudadanos = agregarSeleccione(res.items, 'Seleccione estado ciudadano...');
}

async function buscarCarreras(texto = "") {
  const res = await carreraService.getCarreras(texto, 1, 10);
  datosMaestros.value.carreras = agregarSeleccione(res.items, 'Seleccione carrera...');
}

async function buscarFormasPago(texto = "") {
  const res = await formaPagoService.searchItem(texto, 1, 10);
  datosMaestros.value.formasPago = agregarSeleccione(res.items, 'Seleccione forma de pago...');
}

async function buscarCanales(texto = "") {
  const res = await canalEnteradoService.getMotivos(texto, 1, 10);
  datosMaestros.value.canales = agregarSeleccione(res.items, 'Seleccione canal...');
}

async function buscarAdmitidoComo(texto = "") {
  const res = await admitidoComoService.getAdmitidosComo(texto, 1, 10);
  datosMaestros.value.admitidoComo = agregarSeleccione(res.items, 'Seleccione admitido como...');
}

async function buscarMonedas(texto = "") {
  const res = await monedaService.searchItem(texto, 1, 10);
  datosMaestros.value.monedas = agregarSeleccione(res.items, 'Seleccione moneda...');
}

async function buscarDocumentosRequeridos(texto = "") {
  const res = await documentoService.getDocumentos(texto, 1, 10);
  datosMaestros.value.documentosRequeridos = agregarSeleccione(res.items, 'Seleccione documento requerido...');
}

async function buscarRedesSociales(texto = "") {
  const res = await redSocialService.getRedesSociales(texto, 1, 10);
  datosMaestros.value.redesSociales = agregarSeleccione(res.items, 'Seleccione red social...');
}

async function buscarEstadosSolicitud(texto = "") {
  const res = await estadosSolicitudAmisionService.getEstadosSolicitudAdmision(texto, 1, 10);
  datosMaestros.value.estadosSolicitud = agregarSeleccione(res.items, 'Seleccione estado...');
}

async function buscarSedes(texto = "") {
  const res = await SedesServices.getSedes(texto, 1, 10);
  datosMaestros.value.sedes = agregarSeleccione(res.items, 'Seleccione recinto...');
}

async function buscarIdiomas(texto = "") {
  const res = await idiomasServices.getIdiomas(texto, 1, 10);
  datosMaestros.value.idiomas = agregarSeleccione(res.items, 'Seleccione idioma...');
}

async function buscarDocumentos(texto = "") {
  const res = await documentosService.getDocumentos(texto, 1, 10);
  datosMaestros.value.documentos = agregarSeleccione(res.items, 'Seleccione documento...');
}

async function buscarTiposCarrera(texto = "") {
  const res = await tipoCarreraService.getTiposCarreras(texto, 1, 10);
  datosMaestros.value.tiposCarrera = agregarSeleccione(res.items, 'Seleccione tipo de carrera...');
}

async function buscarTiposSangre(texto = "") {
  const res = await TipoSangreService.getTipoSangre(texto, 1, 10);
  datosMaestros.value.tiposSangre = agregarSeleccione(res.items, 'Seleccione tipo de sangre...');
}

// hacer el watch para cuando se digiete en el input de busqueda
watch(() => searchTextPaises.value, (newValue) => {
  if (newValue.length >= 2 || newValue.length === 0) {
    buscarPaises(newValue);
  }
});
watch(() => searchTextProvincias.value, (newValue) => {
  if (newValue.length >= 2 || newValue.length === 0) {
    buscarProvincias(newValue);
  }
});

watch(() => searchTextSexos.value, (newValue) => {
  if (newValue.length >= 2 || newValue.length === 0) {
    buscarSexos(newValue);
  }
});

watch(() => searchTextEstadosCiviles.value, (newValue) => {
  if (newValue.length >= 2 || newValue.length === 0) {
    buscarEstadosCiviles(newValue);
  }
});

watch(() => searchTextTiposDocumento.value, (newValue) => {
  if (newValue.length >= 2 || newValue.length === 0) {
    buscarTiposDocumento(newValue);
  }
});

watch(() => searchTextNacionalidades.value, (newValue) => {
  if (newValue.length >= 2 || newValue.length === 0) {
    buscarNacionalidades(newValue);
  }
  console.log('Nacionalidades buscadas:', datosMaestros.value.nacionalidades);
});

watch(() => searchTextEstadosCiudadanos.value, (newValue) => {
  if (newValue.length >= 2 || newValue.length === 0) {
    buscarEstadosCiudadanos(newValue);
  }
});
watch(() => searchTextCarreras.value, (newValue) => {
  if (newValue.length >= 2 || newValue.length === 0) {
    buscarCarreras(newValue);
  }
});

watch(() => searchTextFormasPago.value, (newValue) => {
  if (newValue.length >= 2 || newValue.length === 0) {
    buscarFormasPago(newValue);
  }
});
watch(() => searchTextCanales.value, (newValue) => {
  if (newValue.length >= 2 || newValue.length === 0) {
    buscarCanales(newValue);
  }
});
watch(() => searchTextAdmitidoComo.value, (newValue) => {
  if (newValue.length >= 2 || newValue.length === 0) {
    buscarAdmitidoComo(newValue);
  }
});
watch(() => searchTextMonedas.value, (newValue) => {
  if (newValue.length >= 2 || newValue.length === 0) {
    buscarMonedas(newValue);
  }
});
watch(() => searchTextDocumentosRequeridos.value, (newValue) => {
  if (newValue.length >= 2 || newValue.length === 0) {
    buscarDocumentosRequeridos(newValue);
  }
});

watch(() => searchTextRedesSociales.value, (newValue) => {
  if (newValue.length >= 2 || newValue.length === 0) {
    buscarRedesSociales(newValue);
  }
});


// Función para buscar preguntas, si se usa en el futuro
async function buscarPreguntas(texto = "") {
  //const res = await SolicitudAdmisionService.getPreguntas(texto, 1, 10);
  //datos vacios 

  const res = { items: [] }; // Simulación de respuesta vacía
  datosMaestros.value.preguntas = res.items;
}

//Cataglos para cada tab

const catalogosPersonal = computed(() => ({
  paises: datosMaestros.value.paises,
  searchTextPaises,
  buscarPaises,
  
  provincias: datosMaestros.value.provincias,
  searchTextProvincias,
  loadingProvincia,
  buscarProvincias,

  sexos: datosMaestros.value.sexos,
  searchTextSexos,
  loadingSexos,
  buscarSexos,

  estadosCiviles: datosMaestros.value.estadosCiviles,
  searchTextEstadosCiviles,
  loadingEstadosCiviles,
  buscarEstadosCiviles,

  tiposDocumento: datosMaestros.value.tiposDocumento,
  searchTextTiposDocumento,
  loadingTiposDocumento,
  buscarTiposDocumento,

  nacionalidades: datosMaestros.value.nacionalidades,
  searchTextNacionalidades,
  loadingNacionalidad,
  buscarNacionalidades,

  estadosCiudadanos: datosMaestros.value.estadosCiudadanos,
  searchTextEstadosCiudadanos,
  loadingEstadosCiudadanos,
  buscarEstadosCiudadanos,

  carreras: datosMaestros.value.carreras,
  searchTextCarreras,
  loadingCarreras,
  buscarCarreras,
  
  formasPago: datosMaestros.value.formasPago,
  searchTextFormasPago,
  loadingFormasPago,
  buscarFormasPago,



  canales: datosMaestros.value.canales,
  searchTextCanales,
  loadingCanales,
  buscarCanales,


  admitidoComo: datosMaestros.value.admitidoComo,
  searchTextAdmitidoComo,
  loadingAdmitidoComo,
  buscarAdmitidoComo,


  monedas: datosMaestros.value.monedas,
  searchTextMonedas,
  loadingMonedas,
  buscarMonedas,

  documentosRequeridos: datosMaestros.value.documentosRequeridos,
  searchTextDocumentosRequeridos,
  loadingDocumentosRequeridos,
  buscarDocumentosRequeridos,


  redesSociales: datosMaestros.value.redesSociales,
  searchTextRedesSociales,
  loadingRedesSociales,
  buscarRedesSociales,

  estadosSolicitud: datosMaestros.value.estadosSolicitud,
  searchTextEstadosSolicitud,
  loadingEstadosSolicitud,
  buscarEstadosSolicitud,


  tiposSangre: datosMaestros.value.tiposSangre,
  searthTextTiposSangre,
  loadingTipoosSangre,
  buscarTiposSangre: buscarTiposSangre

}));


//Catalogos para el tab Generales

const catalogosGenerales = computed(() => ({
  paises: datosMaestros.value.paises,
  searchTextPaises,
  buscarPaises,
  
  provincias: datosMaestros.value.provincias,
  searchTextProvincias,
  loadingProvincia,
  buscarProvincias,
}));



// Al montar, carga los catálogos default (los 10 primeros)
onMounted(async () => {
  await Promise.all([
    buscarPaises(),
    buscarProvincias(),
    buscarSexos(),
    buscarEstadosCiviles(),
    buscarTiposDocumento(),
    buscarNacionalidades(),
    buscarEstadosCiudadanos(),
    buscarCarreras(),
    buscarFormasPago(),
    buscarCanales(),
    buscarAdmitidoComo(),
    buscarMonedas(),
    buscarDocumentosRequeridos(),
    buscarRedesSociales(),
    buscarEstadosSolicitud(),
    buscarSedes(),
    buscarIdiomas(),
    buscarDocumentos(),
    buscarTiposCarrera(),
    buscarPreguntas(),
    buscarTiposSangre()

  ]);
});

function onBuscarNacionalidades(search: string) {
  // Sólo busca si hay más de 2 letras, o según tu preferencia
  if (search.length >= 2 || search.length === 0) {
    buscarNacionalidades(search);
  }
}

// Estado de las solicitudes filtradas solo la que pueden modificarse si es que la solicitud no está matriculada

const estados = [
  { text: "Registrado", value: 1 },
  { text: "Aprobado", value: 2 },
  { text: "Pendiente", value: 3 },
  { text: "Rechazado", value: 4 },
];





function getEstadoLabel(id: number): string {
  switch (id) {
    case 1: return "Pendiente";
    case 2: return "Solicitada";
    case 3: return "Admitido";
    case 4: return "Diferido";
    case 5: return "Rechazado";
    case 6: return "Matriculado";
    case 7: return "Cancelada";
    case 8: return "Validada";
    case 9: return "Aceptada";
    case 10: return "Pendiente de Pago";
    default: return "Desconocido";
  }
}

function getEstadoColor(id: number): string {
  switch (id) {
    case 1: return "info";         // PENDIENTE
    case 2: return "primary";      // SOLICITADA
    case 3: return "success";      // ADMITIDO
    case 4: return "warning";      // DIFERIDO
    case 5: return "error";        // RECHAZADO
    case 6: return "blue-darken-2"; // MATRICULADO
    case 7: return "red-darken-2"; // CANCELADA
    case 8: return "blue";         // VALIDADA
    case 9: return "teal";         // ACEPTADA
    case 10: return "orange";      // PENDIENTE PAGO
    default: return "grey";        // Desconocido
  }
}


let item = ref<SolicitudAdmisionDto | null>(null);
const loading = ref(false);
const showForm = ref(false);
const activeTab = ref(0);

const personalRef  = ref<InstanceType<typeof Personal> | null>(null)
const academicoRef = ref<InstanceType<typeof Academico> | null>(null);
const adicionalRef = ref<InstanceType<typeof Adicional> | null>(null);
const laboralRef   = ref<InstanceType<typeof Laboral> | null>(null);
const generalesRef = ref<InstanceType<typeof Generales> | null>(null);
const preguntaRef  = ref<InstanceType<typeof Pregunta> | null>(null);


// Función para cargar todo el catálogo de un servicio
// Define la forma que deben tener las funciones de servicio
type ServicioFn<T> = (value: string, pageNumber: number, pageSize: number) => Promise<{
  items: T[],
  totalRecords: number
}>;


onMounted(async () => {
  await searchItem("");
});


async function searchItem(value = '', page = 1, size = 10) {
  try {
    loading.value = true;
    const res = await SolicitudAdmisionService.getSolicitudes(value, page, size);
    res.items = res.items.map((item: { fechaRegistro: string | Date | null | undefined; }) => ({
      ...item,
      fechaRegistroFormateada: formatFecha(item.fechaRegistro)
    }));
    solicitudes.value = res;
  } catch (error) {
    console.error('Error al cargar solicitudes:', error);
  } finally {
    loading.value = false;
  }
}



const loadingEdicion = ref(false);



//Al editar una solicitud, se carga el item en el formulario
 async function editItem(solicitud: SolicitudAdmisionDto) {
  item.value = JSON.parse(JSON.stringify(solicitud));
 // item.value = await SolicitudAdmisionService.getSolicitudById(solicitud.id!);

 // Si el item tiene un estado de solicitud, filtramos los estados que permiten modificar

   if (item.value?.estadoSolicitudNombre != "MATRICULADO") {
    // Si el estado es "Matriculado", excluimos los estados que permiten modificar
    datosMaestros.value.estadosSolicitud = datosMaestros.value.estadosSolicitud.filter(
      (estado) => estado.permiteModificar === true // Excluir el estado "Matriculado"
    );


  }
  else {
    // Si el estado es "Matriculado", mostramos todos los estados
    datosMaestros.value.estadosSolicitud = datosMaestros.value.estadosSolicitud.filter(
      (estado) => estado.permiteModificar === false // Mostrar solo los estados que permiten modificar
    );
  }  

  // llamar a los metodos de buscarlos catalogos por item descripcion y no debe mostrar el form hasta que no traiga el dato
 loadingEdicion.value = true; // 1. Activa loading

  if (item.value) {

  await Promise.all([
    buscarPaises(item.value.paisOrigenNombre?.toString() || ""),
    buscarProvincias(item.value.ciudadOrigenNombre?.toString() || ""),
    buscarSexos(item.value.sexoNombre?.toString() || ""),
    buscarEstadosCiviles(item.value.estadoCivilNombre?.toString() || ""),
    buscarTiposDocumento(item.value.tipoDocumentoIdentidadNombre?.toString() || ""),
    buscarNacionalidades(item.value.nacionalidadNombre?.toString() || ""),
    buscarEstadosCiudadanos(item.value.estadoCiudadanoNombre?.toString() || ""),
    buscarCarreras(item.value.carreraNombre?.toString() || ""),
    buscarFormasPago(item.value.formaPagoNombre?.toString() || ""),
    buscarCanales(item.value.canalEnteradoNombre?.toString() || ""),
    buscarAdmitidoComo(item.value.admitidoComoNombre?.toString() || ""),
    buscarMonedas(item.value.monedaPagoNombre?.toString() || ""),
    buscarRedesSociales(item.value.redSocialNombre?.toString() || ""),
    //buscarEstadosSolicitud(item.value.estadoCiudadanoNombre?.toString() || ""),
    buscarSedes(item.value.campusNombre?.toString() || ""),
    // await buscarIdiomas(item.value.idiomas.map(i => i.id).join(",") || ""),
    // await buscarDocumentos(item.value.documentos.map(d => d.id).join(",") || ""),
    buscarTiposCarrera(item.value.modalidadCarreraNombre?.toString() || ""),
    buscarTiposSangre(item.value.tipoSangreNombre?.toString() || "")
  ]);
    
  }

    loadingEdicion.value = false; // 3. Desactiva loading

  showForm.value = true;
  activeTab.value = 0;
} 



function newItem() {
  item.value = {
    id: undefined,
    empresaId: 1,
    campusId: 1,
    codigoReferencia: "",
    codigoMatricula: "",
    nombreCompleto: "",
    apellidoCompleto: "",
    nacimiento: new Date(),
    paisOrigenId: 0,
    ciudadOrigenId: 0,
    ciudadTexto: "",
    tipoSangreId: 0,
    infoAlergias: "",
    enfermedadPadece: "",
    nacionalidadId: 0,
    sexoId: 0,
    estadoCivilId: 0,
    estadoCiudadanoId: 0,
    documentoIdentidad: "",
    tipoDocumentoIdentidad: 0,
    telefonoCelular: "",
    correoElectronico: "",
    empresaTrabajo: "",
    cargoTrabajo: "",
    telefonoTrabajo: "",
    direccionTrabajo: "",
    tipoIngresoId: 0,
    cohorteId: 0,
    carreraId: 0,
    modalidadCarreraId: 0,
    admitidoComoId: 0,
    horarioId: 0,
    formaPagoId: 0,
    canalEnteradoId: 0,
    objetivoIngreso: "",
    fechaProceso: "",
    fechaAceptacion: "",
    fechaRegistro: "",
    monedaPagoId: 0,
    tieneBeca: false,
    porcentajeDescuento: 0,
    estadoSolicitudId: 1, // Estado por defecto "Registrado"
    tratamiento: "",
    condicionMedica: "",
    discapacidad: "",
    comentariosAdicionales: "",
    fechaGraduacion: "",
    redSocialId: 0,
    documentos: [],
    idiomas: [],
    educacion: [],
    infoCrm: [],
    direcciones: [{
      paisId: 0,
      provinciaId: 0,
      direccion: "",
      telefono: ""
    }],
    preguntas: [],
  };

  showForm.value = true;
  activeTab.value = 0;
}



const alertType = ref<'success' | 'error' | 'warning'>('success');
const alertVisible = ref(false);
const alertMessage = ref('');

const estadoSolicitudId = computed<null | undefined>({
  get: () => item.value?.estadoSolicitudId == null ? null : undefined,
  set: (val) => {
    if (item.value) item.value.estadoSolicitudId = val ?? null;
  }
});


async function saveItem() {

//Si esta matriculado, no se puede cambiar el estado
  if (item.value?.estadoSolicitudId === 6) {
    alertType.value = 'error';
    alertMessage.value = 'No se puede cambiar el estado de una solicitud ya matriculada.';
    alertVisible.value = true;
    return;
  }

  if (!item.value) return;
  // Aquí puedes agregar validaciones antes de enviar el formulario
  if (!item.value.nombreCompleto || !item.value.correoElectronico) {
    alertType.value = 'error';
    alertMessage.value = 'Por favor, complete todos los campos requeridos.';
    alertVisible.value = true;
 
  }

  //Enviar el formulario

  try {
    loading.value = true;
    if (item.value.id) {
      // Actualizar solicitud existente
      await updateSolicitud(item.value);
      alertType.value = 'success';
      alertMessage.value = 'Solicitud actualizada correctamente.';
    } else {
      // Crear nueva solicitud
      await createSolicitud(item.value);
      alertType.value = 'success';
      alertMessage.value = 'Solicitud creada correctamente.';
    }
    showForm.value = false;
    await searchItem();
  } catch (error) {
    console.error('Error al guardar la solicitud:', error);
    alertType.value = 'error';
    alertMessage.value = 'Error al guardar la solicitud. Por favor, inténtelo de nuevo.';
  } finally {
    loading.value = false;
    alertVisible.value = true;
  }

}


function formatFecha(fecha: string | Date | null | undefined): string {
  if (!fecha) return '';
  const date = typeof fecha === 'string' ? new Date(fecha) : fecha;
  if (!date || isNaN(date.getTime())) return '';
  return date.toLocaleDateString('es-ES');
}




async function createSolicitud(solicitud: SolicitudAdmisionDto) {
  //vamos a llmar el api directamente
  try {
    const response = await SolicitudAdmisionService.createSolicitud(solicitud);
    return response;
  } catch (error) {
    console.error('Error al crear la solicitud:', error);
    throw error; // Re-lanzar el error para manejarlo en el componente
  }
}

async function updateSolicitud(solicitud: SolicitudAdmisionDto) {
  //vamos a llmar el api directamente
  try {
    const response = await SolicitudAdmisionService.updateSolicitud(item.value?.id!, solicitud);
    if (response.succeeded) {
      // Actualizar el item localmente si la actualización fue exitosa
      item.value = { ...item.value, ...solicitud };
    }
    return response;
  } catch (error) {
    console.error('Error al actualizar la solicitud:', error);
    throw error; // Re-lanzar el error para manejarlo en el componente
  }
}

// Agregado: Función para agregar la opción "Seleccione..." a un arreglo
function agregarSeleccione(arr: { id: number; descripcion?: string; }[], label = 'Seleccione...') {
  if (!arr.length || arr[0]?.id !== 0) {
    arr.unshift({ id: 0, descripcion: label });
  }
  return arr;
}
</script>

<template>
  <div>
    <wFormData
      :panel="true"
      :icon="'mdi-file-document'"
      :title="'Solicitudes de Admisión'"
      :headers="headers"
      :items="solicitudes.items"
      :loading="loading"
      :pageNumber="solicitudes.pageNumber"
      :pageSize="solicitudes.pageSize"
      :totalPages="solicitudes.totalPages"
      :totalRecords="solicitudes.totalRecords"
      :breadcrumbs="[]"
      :text="'Administrar Solicitudes de Admisión'"
      :dialogWidth="'800px'"
      :filters="null"
      :filtervalue="''"
      @editItem="editItem"
      @searchItem="searchItem"
      @newItem="newItem"
      @update="saveItem"
      @cancel="showForm = false"
    >
      <template #editItemPanel>
        <!-- <div v-if="showForm"> -->
          <!-- Mantén el margin top para separarlo del header, pero quita paddings internos aquí -->
         <v-card class="mt-4">
        <!-- Header mejorado -->
         <div v-if="item?.codigoReferencia" class="solicitud-header-box pa-4 mb-2">
          <v-row align="center" justify="space-between" class="no-gutters">
            <!-- Código de Solicitud y Cohorte -->
            <v-col cols="12" sm="6" md="4">
              <div class="d-flex align-center">
                <v-icon color="primary" class="mr-2 solicitud-header-icon">mdi-format-list-numbered</v-icon>
                <span class="solicitud-header-label">
                  <strong>No. Solicitud:</strong> {{ item.id }}
                </span>
              </div>
              <div v-if="item.infoCrm && item.infoCrm.length > 0" class="cohorte-label mt-1">
                <v-icon color="primary" size="18" class="mr-1">mdi-calendar-range</v-icon>
                <span><strong>Cohorte:</strong> {{ item.infoCrm[0].cohorteNombre }}</span>
              </div>
            </v-col>
            <!-- Matrícula y Estado de Solicitud -->
            <v-col cols="12" sm="6" md="4" class="text-right">
              <div class="d-flex flex-column align-end">
                <!-- Matrícula solo si está en estado Matriculado -->
                <div
                  v-if="item.estadoSolicitudId === 6 && item.codigoMatricula"
                  class="matricula-label mb-1"
                >
                  <v-icon color="success" size="18" class="mr-1">mdi-school</v-icon>
                  <span><strong>Matrícula:</strong> {{ item.codigoMatricula }}</span>
                </div>
                <div class="d-flex align-center">
                  <v-chip
                    :color="getEstadoColor(item.estadoSolicitudId ?? 0)"
                    text-color="white"
                    class="mr-2 solicitud-header-chip"
                    label
                    prepend-icon="mdi-information-outline"
                  >
                    {{ getEstadoLabel(item.estadoSolicitudId ?? 0) }}
                  </v-chip>
                  <!-- Menú para editar estado -->
                  <v-menu offset-y :close-on-content-click="false">
                    <template #activator="{ props }">
                      <v-btn
                        :disabled="item?.estadoSolicitudId === 6"
                        icon v-bind="props" variant="text" size="small"
                      >
                        <v-icon>mdi-pencil</v-icon>
                      </v-btn>
                    </template>
                    <v-card class="pa-3" width="250">
                      <v-select
                        v-model="estadoSolicitudId"
                        :items="datosMaestros.estadosSolicitud"
                        item-value="id"
                        item-title="descripcion"
                        label="Cambiar Estado"
                        dense
                        variant="outlined"
                        hide-details
                        :return-object="false"
                      />
                    </v-card>
                  </v-menu>
                </div>
              </div>
            </v-col>
          </v-row>
        </div> 
            <v-divider class="my-3" />
            <!-- Tabs de la Solicitud -->
           <wTabsCard :tabs="tabs">
              <template #personal>
                <Personal 
                  v-if="item"  v-model:item="item"  :datosMaestros="datosMaestros" :catalogos="catalogosPersonal"/>
              </template>
              <template #generales>
                <Generales v-if="item" v-model:item="item" :datosMaestros="datosMaestros" :catalogos="catalogosGenerales" />
              </template>
              <template #laboral>
                <Laboral v-if="item" v-model:item="item" :datosMaestros="datosMaestros" />
              </template>
              <template #academico>
                <Academico v-if="item" v-model:item="item" :datosMaestros="datosMaestros" :catalogos="catalogosPersonal" />
              </template>
              <template #adicional>
                <Adicional v-if="item" v-model:item="item" :datosMaestros="datosMaestros" :catalogos="catalogosPersonal"/>
              </template>
              <template #pregunta>
                <Pregunta v-if="item" v-model:item="item" :datosMaestros="datosMaestros" />
              </template>
              <template #documentos>
                <div style="width: 100%;">
                <Documentos v-if="item" v-model:item="item" :datosMaestros="datosMaestros" />
                </div>
              </template>
            </wTabsCard>
         </v-card>
        <!-- </div> -->
      </template>
    </wFormData>
    <!-- componente del alert personalizado -->
    <wDialogMessage
      v-model="alertVisible"
      :message="alertMessage"
      :type="alertType"
    />
  </div>
</template>

<style scoped>
.solicitud-header-box {
  background: var(--v-theme-surface);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(60,60,60,0.07);
  border: 1px solid var(--v-theme-outline-variant, #e4e6ef);
  margin-bottom: 10px;
  position: relative;
  transition: background 0.3s, border 0.3s;
}
.solicitud-header-label {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--v-theme-on-surface);
}
.solicitud-header-icon {
  font-size: 1.4rem;
}
.matricula-label {
  font-size: 1rem;
  color: #388e3c;
  font-weight: 500;
  display: flex;
  align-items: center;
}
.solicitud-header-chip {
  font-size: 0.98rem !important;
  font-weight: 600 !important;
  height: 32px !important;
  min-width: 90px;
}
.cohorte-label {
  color: #526488;
  font-size: 1rem;
  margin-top: 4px;
} 
.header-bar {
  height: 4px;
  width: 100%;
  background: linear-gradient(90deg, #3264fe 0%, #83aaff 100%);
  border-radius: 12px 12px 0 0;
  position: absolute;
  top: 0; left: 0;
  z-index: 1;
}
</style>
