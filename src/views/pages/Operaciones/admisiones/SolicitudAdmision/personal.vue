<script setup lang="ts">
import type { SolicitudAdmisionDto } from '@/utils/models/Academico/SolicitudAdmisionDto';
import { ref } from 'vue'
import { computed } from 'vue'
import { defineProps, defineEmits } from 'vue'
import {
  MapPinIcon,
  WorldIcon,
  UserIcon,
  HeartIcon
  
} from 'vue-tabler-icons'

const rawNacimiento = ref("1985-04-20T00:00:00")

const fechaNacimiento = computed({
  get: () => rawNacimiento.value?.substring(0, 10) ?? '',
  set: (val) => {
    rawNacimiento.value = val; // Aquí podrías convertirlo a DateTime si necesitas
  }
})



// al editar tomamos los datos del componente padre
const props = defineProps<{
  item: SolicitudAdmisionDto,
    datosMaestros: any;
    catalogos: any

}>()

/* function onBuscarNacionalidades(val: string | any[]) {
  // Si quieres limitar la búsqueda a mínimo 2 letras, puedes hacerlo aquí
  if (val.length >= 2 || val.length === 0) {
    props.catalogos.buscarNacionalidades (val);
  }
}

 */


defineEmits(['update:item'])
//const emit = defineEmits(['update:item']) emit('update:item', { ...props.item })


</script>
<template>
  <v-card class="pa-6 mb-4" elevation="2">
    <v-row>
      <!-- Grupo: Datos Básicos -->
      <v-col cols="12">
        <h6 class="text-h6 mb-1 d-flex align-center">
          <UserIcon size="16" stroke-width="1.5" class="mr-1" />
          Datos Básicos
        </h6>
        <v-divider class="mb-1" />
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field v-model="item.nombreCompleto" variant="outlined" density="compact" color="primary" label="Nombres">
          <template #prepend-inner><UserIcon size="18" stroke-width="1.5" /></template>
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field v-model="item.apellidoCompleto" variant="outlined" density="compact" color="primary" label="Apellidos">
          <template #prepend-inner><UserIcon size="18" stroke-width="1.5" /></template>
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field v-model="fechaNacimiento" type="date" label="Fecha de Nacimiento" variant="outlined" density="compact" color="primary" placeholder="dd/mm/aaaa">
          <template #prepend-inner><CalendarIcon size="18" stroke-width="1.5" /></template>
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete v-model="item.paisOrigenId" variant="outlined" density="compact" color="primary" label="País de Nacimiento *"
          :items="props.datosMaestros.paises"
          item-value="id"
          item-title="descripcion"
          :search-input.sync="catalogos.paisesSearch"
          :loading="catalogos.paisesLoading"
          @update:search="catalogos.buscarPaises"
        >
          <template #prepend-inner><WorldIcon size="18" stroke-width="1.5" /></template>
        </v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.nacionalidadId"
          variant="outlined"
          density="compact"
          color="primary"
          label="Nacionalidad"
          :items="props.datosMaestros.nacionalidades"
          :loading="catalogos.nacionalidadesLoading"
          item-value="id"
          item-title="descripcion"
          :search-input.sync="catalogos.nacionalidadesSearch"
          @update:search="catalogos.buscarNacionalidades"
        >
          <template #prepend-inner>
            <FlagIcon size="18" stroke-width="1.5" />
          </template>
        </v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.sexoId"
          variant="outlined"
          density="compact"
          color="primary"
          label="Sexo"
          :items="props.datosMaestros.sexos"
          item-value="id"
          item-title="descripcion"
          :search-input.sync="catalogos.sexosSearch"
          :loading="catalogos.sexosLoading"
          @update:search="catalogos.buscarSexos"
        >
          <template #prepend-inner>
            <GenderBigenderIcon size="18" stroke-width="1.5" />
          </template>
        </v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete v-model="item.tipoDocumentoIdentidad" variant="outlined" density="compact" color="primary" label="Tipo Documento de Identidad"
          :items="props.datosMaestros.tiposDocumento"
          item-value="id"
          item-title="descripcion"
          :search-input.sync="catalogos.tiposDocumentoSearch"
          :loading="catalogos.tiposDocumentoLoading"
          @update:search="catalogos.buscarTiposDocumento"
        >
          <template #prepend-inner><MapPinIcon size="18" stroke-width="1.5" /></template>
        </v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field v-model="item.documentoIdentidad" variant="outlined" density="compact" color="primary" label="Documento de Identidad" placeholder="Ingrese Documento de Identidad">
          <template #prepend-inner><PlaneIcon size="18" stroke-width="1.5" /></template>
        </v-text-field>
      </v-col>
            <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.estadoCivilId"
          variant="outlined"
          density="compact"
          color="primary"
          label="Estado Civil"
          :items="props.datosMaestros.estadosCiviles"
          item-value="id"
          item-title="descripcion"
          :search-input.sync="catalogos.estadosCivilesSearch"
          :loading="catalogos.estadosCivilesLoading"
          @update:search="catalogos.buscarEstadosCiviles"
        >
          <template #prepend-inner>
            <HeartIcon size="18" stroke-width="1.5" />
          </template>
        </v-autocomplete>
      </v-col>

      <!-- Grupo: Datos Médicos -->
      <v-col cols="12" class="mt-3">
        <h6 class="text-h6 mb-1 d-flex align-center">
          <HeartIcon size="18" stroke-width="1.5" class="mr-1" />
          Datos Médicos
        </h6>
        <v-divider class="mb-1" />
      </v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete v-model="item.tipoSangreId"
          variant="outlined" density="compact" color="primary" label="Tipo de Sangre"
          :items="props.datosMaestros.tiposSangre"
          item-value="id"
          item-title="descripcion"
          :search-input.sync="catalogos.tiposSangreSearch"
          :loading="catalogos.tiposSangreLoading"
          @update:search="catalogos.buscarTiposSangre"
        >
          <template #prepend-inner><DropletIcon size="18" stroke-width="1.5" /></template>
        </v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field v-model="item.enfermedadPadece" variant="outlined" density="compact" color="primary" label="Enfermedad" placeholder="Describa la enfermedad">
          <template #prepend-inner><FirstAidKitIcon size="18" stroke-width="1.5" /></template>
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field v-model="item.infoAlergias" variant="outlined" density="compact" color="primary" label="Alergias" placeholder="Describa las alergias">
          <template #prepend-inner><AlertCircleIcon size="18" stroke-width="1.5" /></template>
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field v-model="item.discapacidad" variant="outlined" density="compact" color="primary" label="Discapacidad" placeholder="Detalle la discapacidad">
          <template #prepend-inner><WheelchairIcon size="18" stroke-width="1.5" /></template>
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field v-model="item.tratamiento" variant="outlined" density="compact" color="primary" label="Tratamiento" placeholder="Medicamentos, terapias...">
          <template #prepend-inner><PillIcon size="18" stroke-width="1.5" /></template>
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-textarea v-model="item.comentariosAdicionales" variant="outlined" density="compact" color="primary" label="Otros comentarios" rows="3" placeholder="Información adicional">
          <template #prepend-inner><Message2Icon size="18" stroke-width="1.5" /></template>
        </v-textarea>
      </v-col>
      <v-col cols="12">
        <v-autocomplete
          v-model="item.admitidoComoId"
          variant="outlined"
          density="compact"
          color="primary"
          label="Admitido Como"
          :items="props.datosMaestros.admitidoComo"
          item-value="id"
          item-title="descripcion"
          :search-input.sync="catalogos.admitidoComoSearch"
          :loading="catalogos.admitidoComoLoading"
          :update:search="catalogos.buscarAdmitidoComo"
        >
          <template #prepend-inner>
            <UserIcon size="18" stroke-width="1.5" />
          </template>
        </v-autocomplete>
      </v-col>
    </v-row>
  </v-card>
</template>


<style scoped>
.mb-1 { margin-bottom: 0.5rem !important; }
.mt-1 { margin-top: 0.5rem !important; }
.text-h6 { font-size: 1rem !important; font-weight: 600 !important; }
.mr-1 { margin-right: 0.5rem; }

/* Compatibilidad con modo oscuro y claro SOLO para el card O para el card */
.v-card {
  background-color: var(--v-theme-surface) !important;
  color: var(--v-theme-on-surface) !important;
  transition: background 0.3s, color 0.3s;
}

</style>