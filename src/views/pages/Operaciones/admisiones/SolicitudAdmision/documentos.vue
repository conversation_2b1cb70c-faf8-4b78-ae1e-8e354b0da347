<script setup lang="ts">
import { ref, computed } from 'vue'
import type { SolicitudAdmisionDto, DocumentoDto } from '@/utils/models/Academico/SolicitudAdmisionDto'

const emit = defineEmits(['update:item'])
const props = defineProps<{
  item: SolicitudAdmisionDto,
  datosMaestros: {
    documentos: any[]
  }
}>()

const estadosDocumento = [
  { value: 1, text: 'Aprobado', color: 'success' },
  { value: 2, text: 'Pendiente', color: 'warning' },
  { value: 3, text: 'Rechazado', color: 'error' }
]

// Modal de comentario
const showComentarioModal = ref(false)
const documentoSeleccionado = ref<{index: number, nuevoEstado: number, estadoAnterior: number} | null>(null)
const comentarioTemp = ref('')

// Iconos por estado
function getIconoEstado(estadoId: number) {
  switch(estadoId) {
    case 1: return 'mdi-check-circle'      // Aprobado
    case 2: return 'mdi-clock-outline'     // Pendiente  
    case 3: return 'mdi-close-circle'      // Rechazado
    default: return 'mdi-help-circle'      // Desconocido
  }
}

// Nombre visible del documento según el catálogo
function nombreDocumento(id: number) {
  const doc = props.datosMaestros.documentos.find(d => d.id === id)
  return doc ? doc.descripcion || doc.nombre : `Doc. #${id}`
}

// Cambiar estado - ahora abre modal para comentario
function setEstado(index: number, nuevoEstado: number) {
  const estadoAnterior = props.item.documentos[index].estadoDocumentoId || 0
  
  // Guardar la información para el modal
  documentoSeleccionado.value = { index, nuevoEstado, estadoAnterior }
  comentarioTemp.value = props.item.documentos[index].comentario || ''
  
  // Abrir modal para comentario
  showComentarioModal.value = true
}

// Confirmar cambio de estado con comentario
function confirmarCambioEstado() {
  if (!documentoSeleccionado.value) return
  
  const { index, nuevoEstado } = documentoSeleccionado.value
  const documentosActualizados = [...props.item.documentos]
  
  documentosActualizados[index] = { 
    ...documentosActualizados[index], 
    estadoDocumentoId: nuevoEstado,
    comentario: comentarioTemp.value
  }
  
  // Emitir el cambio completo
  emit('update:item', { 
    ...props.item, 
    documentos: documentosActualizados 
  })
  
  // Cerrar modal y limpiar
  showComentarioModal.value = false
  documentoSeleccionado.value = null
  comentarioTemp.value = ''
  
  console.log('Estado actualizado:', {
    index,
    nuevoEstado,
    comentario: comentarioTemp.value,
    documento: documentosActualizados[index]
  })
}

// Cancelar cambio de estado
function cancelarCambioEstado() {
  showComentarioModal.value = false
  documentoSeleccionado.value = null
  comentarioTemp.value = ''
}

// Obtener texto del estado
function getEstadoTexto(estadoId: number) {
  return estadosDocumento.find(e => e.value === estadoId)?.text || 'Sin estado'
}

// Obtener color del estado
function getEstadoColor(estadoId: number) {
  return estadosDocumento.find(e => e.value === estadoId)?.color || 'grey'
}

// Subir documento modal
const showUpload = ref(false)
const uploadTab = ref(0)
const archivo = ref<File|null>(null)
const nuevoDocumento = ref<DocumentoDto>({
  solicitudId: props.item.id || 0,
  documentoId: 0,
  comentario: '',
  enlaceArchivo: '',
  estadoDocumentoId: 2 // Pendiente
})

// Simular subida y agregar a la lista
async function subirDocumento() {
  let url = nuevoDocumento.value.enlaceArchivo
  // Si el usuario sube archivo, simular "subida" y poner un enlace falso
  if (archivo.value) {
    url = URL.createObjectURL(archivo.value)
  }
  if (!nuevoDocumento.value.documentoId || !url) {
    alert('Completa el tipo y archivo/enlace')
    return
  }
  emit('update:item', {
    ...props.item,
    documentos: [
      ...props.item.documentos,
      {
        ...nuevoDocumento.value,
        enlaceArchivo: url
      }
    ]
  })
  // Resetear todo
  nuevoDocumento.value = {
    solicitudId: props.item.id || 0,
    documentoId: 0,
    comentario: '',
    enlaceArchivo: '',
    estadoDocumentoId: 2
  }
  archivo.value = null
  showUpload.value = false
}

// Ver archivo
function viewArchivo(url: string) {
  if (url) window.open(url, '_blank')
}
// Descargar archivo
function downloadArchivo(url: string, nombre: string) {
  if (url) {
    const link = document.createElement('a')
    link.href = url
    link.download = nombre
    link.target = '_blank'
    link.click()
  }
}
</script>

<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card class="pa-6 mb-6" elevation="2">
          <div class="d-flex align-center mb-2">
            <v-icon color="primary" class="mr-2">mdi-file-document-multiple</v-icon>
            <span class="text-h6">Gestión de Documentos</span>
            <v-spacer/>
            <v-btn color="primary" variant="flat" @click="showUpload = true">
              <v-icon left>mdi-upload</v-icon> Subir Documento
            </v-btn>
          </div>
          <v-divider class="mb-4"/>
          <v-data-table
            :headers=" [
              { title: 'Documento', value: 'nombre' },
              { title: 'Estado Actual', value: 'estadoActual' },
              { title: 'Cambiar Estado', value: 'estadoDocumentoId' },
              { title: 'Comentario', value: 'comentario' },
              { title: 'Acciones', value: 'actions', sortable: false }
            ]"
            :items="props.item.documentos.map((doc, i) => ({
              ...doc,
              nombre: doc.documentoId ? nombreDocumento(doc.documentoId) : 'Sin documento'
            }))"
            :items-per-page="8"
            class="elevation-0"
            density="comfortable"
          >
            <template #item.enlaceArchivo="{ item }">
              <div>
                <a v-if="item.enlaceArchivo" :href="item.enlaceArchivo" target="_blank" class="text-primary text-decoration-underline" style="max-width:180px;overflow:hidden;text-overflow:ellipsis;display:inline-block;">
                  {{ item.enlaceArchivo.length > 38 ? item.enlaceArchivo.slice(0, 35) + '...' : item.enlaceArchivo }}
                </a>
                <span v-else class="text-grey text-caption">Sin enlace</span>
              </div>
            </template>
            
            <!-- Estado actual (solo visual) -->
            <template #item.estadoActual="{ item }">
              <v-chip 
                :color="getEstadoColor(item.estadoDocumentoId || 0)" 
                text-color="white" 
                size="small"
              >
                <v-icon left size="14">
                  {{ getIconoEstado(item.estadoDocumentoId || 0) }}
                </v-icon>
                {{ getEstadoTexto(item.estadoDocumentoId || 0) }}
              </v-chip>
            </template>
            
            <!-- Selector para cambiar estado -->
            <template #item.estadoDocumentoId="{ item, index }">
              <v-select
                :model-value="item.estadoDocumentoId"
                :items="estadosDocumento"
                item-title="text"
                item-value="value"
                density="compact"
                variant="outlined"
                hide-details
                style="min-width:130px"
                @update:model-value="setEstado(index, $event)"
              >
                <template #selection>
                  <span class="text-caption text-grey">Cambiar a...</span>
                </template>
                
                <!-- Opciones del dropdown con iconos -->
                <template #item="{ props: itemProps, item: opcion }">
                  <v-list-item v-bind="itemProps" :title="opcion.title">
                    <template #prepend>
                      <v-icon :color="opcion.raw.color">
                        {{ getIconoEstado(opcion.raw.value) }}
                      </v-icon>
                    </template>
                  </v-list-item>
                </template>
              </v-select>
            </template>
            
            <!-- Comentario (solo lectura) -->
            <template #item.comentario="{ item }">
              <div style="max-width: 200px;">
                <span v-if="item.comentario" class="text-caption">
                  {{ item.comentario.length > 50 ? item.comentario.slice(0, 47) + '...' : item.comentario }}
                </span>
                <span v-else class="text-grey text-caption">Sin comentario</span>
              </div>
            </template>
            
            <template #item.actions="{ item }">
              <v-btn icon variant="text" :disabled="!item.enlaceArchivo" color="primary" @click="viewArchivo(item.enlaceArchivo || '')">
                <v-icon size="18">mdi-eye</v-icon>
              </v-btn>
              <v-btn icon variant="text" :disabled="!item.enlaceArchivo" color="teal" @click="downloadArchivo(item.enlaceArchivo ?? '', item.nombre)">
                <v-icon size="18">mdi-download</v-icon>
              </v-btn>
            </template>
          </v-data-table>
          <div v-if="!props.item.documentos.length" class="text-center py-8 text-grey">
            <v-icon size="48" class="mb-2">mdi-file-hidden</v-icon>
            <div>No hay documentos subidos</div>
          </div>
        </v-card>
      </v-col>
    </v-row>

    <!-- MODAL PARA COMENTARIO AL CAMBIAR ESTADO -->
    <v-dialog v-model="showComentarioModal" max-width="600px" persistent>
      <v-card>
        <v-card-title class="d-flex align-center pa-6 pb-4">
          <v-icon left class="mr-3">mdi-comment-text</v-icon>
          <span class="text-h5">Cambiar Estado del Documento</span>
        </v-card-title>
        
        <v-card-text class="pa-6" v-if="documentoSeleccionado">
          <!-- Información del cambio -->
          <v-row class="mb-4">
            <v-col cols="12">
              <v-card variant="outlined" class="pa-4">
                <div class="d-flex align-center justify-space-between">
                  <div>
                    <div class="text-subtitle-2 mb-2">Estado Actual:</div>
                    <v-chip 
                      :color="getEstadoColor(documentoSeleccionado.estadoAnterior)" 
                      text-color="white" 
                      size="small"
                    >
                      <v-icon left size="14">
                        {{ getIconoEstado(documentoSeleccionado.estadoAnterior) }}
                      </v-icon>
                      {{ getEstadoTexto(documentoSeleccionado.estadoAnterior) }}
                    </v-chip>
                  </div>
                  
                  <v-icon size="32" color="grey">mdi-arrow-right</v-icon>
                  
                  <div>
                    <div class="text-subtitle-2 mb-2">Nuevo Estado:</div>
                    <v-chip 
                      :color="getEstadoColor(documentoSeleccionado.nuevoEstado)" 
                      text-color="white" 
                      size="small"
                    >
                      <v-icon left size="14">
                        {{ getIconoEstado(documentoSeleccionado.nuevoEstado) }}
                      </v-icon>
                      {{ getEstadoTexto(documentoSeleccionado.nuevoEstado) }}
                    </v-chip>
                  </div>
                </div>
              </v-card>
            </v-col>
          </v-row>
          
          <!-- Campo de comentario -->
          <v-row>
            <v-col cols="12">
              <v-textarea
                v-model="comentarioTemp"
                label="Comentario / Observaciones"
                variant="outlined"
                rows="4"
                maxlength="500"
                counter
                placeholder="Agregue comentarios sobre este cambio de estado..."
                prepend-inner-icon="mdi-comment-text-outline"
              />
            </v-col>
          </v-row>
          
          <!-- Ayuda contextual -->
          <v-alert
            v-if="documentoSeleccionado.nuevoEstado === 3"
            type="warning"
            variant="tonal"
            class="mt-2"
            density="compact"
          >
            <template #prepend>
              <v-icon>mdi-information</v-icon>
            </template>
            <strong>Documento rechazado:</strong> Se recomienda especificar el motivo del rechazo para que el solicitante pueda corregirlo.
          </v-alert>
        </v-card-text>
        
        <v-card-actions class="pa-6 pt-0">
          <v-spacer/>
          <v-btn 
            color="grey" 
            variant="outlined" 
            @click="cancelarCambioEstado"
          >
            <v-icon left>mdi-close</v-icon>
            Cancelar
          </v-btn>
          <v-btn 
            :color="getEstadoColor(documentoSeleccionado?.nuevoEstado || 0)" 
            variant="flat" 
            @click="confirmarCambioEstado"
          >
            <v-icon left>{{ getIconoEstado(documentoSeleccionado?.nuevoEstado || 0) }}</v-icon>
            Confirmar Cambio
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- SUBIR DOCUMENTO MODAL -->
    <v-dialog v-model="showUpload" max-width="500">
      <v-card>
        <v-card-title class="d-flex align-center">
          <v-icon class="mr-2">mdi-upload</v-icon>
          Subir Nuevo Documento
        </v-card-title>
        <v-card-text>
          <v-form>
            <v-select
              v-model="nuevoDocumento.documentoId"
              :items="props.datosMaestros.documentos"
              item-title="descripcion"
              item-value="id"
              label="Tipo de Documento *"
              variant="outlined"
              density="compact"
              prepend-inner-icon="mdi-file-document"
              class="mb-4"
            />
            <v-tabs v-model="uploadTab" bg-color="grey-lighten-4" class="mb-2">
              <v-tab>Archivo</v-tab>
              <v-tab>Enlace externo</v-tab>
            </v-tabs>
            <v-window v-model="uploadTab">
              <v-window-item value="0">
                <v-file-input
                  label="Archivo PDF/JPG/PNG"
                  show-size
                  accept="application/pdf,image/*"
                  v-model="archivo"
                  variant="outlined"
                  prepend-inner-icon="mdi-upload"
                  class="mb-3"
                />
              </v-window-item>
              <v-window-item value="1">
                <v-text-field
                  label="Enlace del documento"
                  v-model="nuevoDocumento.enlaceArchivo"
                  variant="outlined"
                  prepend-inner-icon="mdi-link"
                  placeholder="https://..."
                  class="mb-3"
                />
              </v-window-item>
            </v-window>
            <v-select
              v-model="nuevoDocumento.estadoDocumentoId"
              :items="estadosDocumento"
              item-title="text"
              item-value="value"
              label="Estado"
              variant="outlined"
              density="compact"
              class="mb-3"
              prepend-inner-icon="mdi-flag"
            />
            <v-textarea
              v-model="nuevoDocumento.comentario"
              label="Comentario"
              variant="outlined"
              rows="2"
              class="mb-2"
              maxlength="250"
            />
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer/>
          <v-btn flat @click="showUpload = false">
            Cancelar
          </v-btn>
          <v-btn color="primary" variant="flat" @click="subirDocumento">
            <v-icon left>mdi-upload</v-icon>
            Guardar
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<style scoped>
.text-h6 { font-weight: 500; color: #2d3748; }
.v-data-table { background: transparent !important; border: 1px solid #edf2f7; border-radius: 6px; }
.v-data-table th, .v-data-table td { font-size: 13px !important; color: #4a5568; }
</style>
