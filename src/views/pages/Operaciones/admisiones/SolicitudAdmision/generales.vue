<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import type { DireccionDto, SolicitudAdmisionDto } from '@/utils/models/Academico/SolicitudAdmisionDto';
import {
  MapPinIcon,
  WorldIcon,
  MapIcon,
  RoadIcon,
  PhoneCallIcon,
  DeviceMobileIcon,
  MailIcon,
  BrandFacebookIcon,
  BrandInstagramIcon,
  BrandXingIcon,
  UserIcon
} from 'vue-tabler-icons'

// Campos principales de dirección y contacto

const facebook = ref('')
const instagram = ref('')
const twitter = ref('')

// Contactos adicionales
const maxContactos = 4
const contactos = ref<Array<{ parentesco: string; numero: string; email: string }>>([])

const dialogContacto = ref(false)
const editIndex = ref<number | null>(null)
const contactoForm = reactive<{ parentesco: string; numero: string; email: string }>({
  parentesco: '',
  numero: '',
  email: ''
})

function abrirDialogoContacto(index: number | null = null) {
  if (index !== null) {
    contactoForm.parentesco = contactos.value[index].parentesco
    contactoForm.numero = contactos.value[index].numero
    contactoForm.email = contactos.value[index].email
    editIndex.value = index
  } else {
    contactoForm.parentesco = ''
    contactoForm.numero = ''
    contactoForm.email = ''
    editIndex.value = null
  }
  dialogContacto.value = true
}

function guardarContacto() {
  if (!contactoForm.parentesco || !contactoForm.numero) return
  if (editIndex.value !== null) {
    contactos.value[editIndex.value] = { ...contactoForm }
  } else {
    if (contactos.value.length < maxContactos) {
      contactos.value.push({ ...contactoForm })
    }
  }
  dialogContacto.value = false
}

function eliminarContacto(index: number) {
  contactos.value.splice(index, 1)
}



// al editar tomamos los datos del componente padre
const props = defineProps<{
  item: SolicitudAdmisionDto,
  datosMaestros: any;
  catalogos: any

}>()

if (!props.item.direcciones) props.item.direcciones = [{
  paisId: 0,
  provinciaId: 0,
  direccion: '',
  telefono: ''
}]
if (!props.item.direcciones[0]) props.item.direcciones[0] = { paisId: 0, provinciaId: 0 , direccion: '', telefono: '' }

const direccionPrincipal = computed({
  get: () => props.item.direcciones[0],
  set: val => props.item.direcciones[0] = { ...val }
})





const emit = defineEmits(['update:item'])

// Función para actualizar campos
function updateField(field: string, value: any) {
  emit('update:item', { 
    ...props.item, 
    [field]: value 
  })
}
</script>

<template>
  <v-container fluid>
    <v-row>
      <!-- SECCIÓN: DIRECCIÓN -->
      <v-col cols="12">
        <v-card class="pa-6 mb-4" elevation="2">
          <h6 class="text-h6 mb-4 d-flex align-center">
            <MapPinIcon size="20" stroke-width="1.5" class="mr-2" />
            Dirección de Residencia
          </h6>
          <v-divider class="mb-4"></v-divider>
          
          <v-row>
            <v-col cols="12" sm="6">
              <v-autocomplete
                v-model="direccionPrincipal.paisNombre"
                :update:model-value="direccionPrincipal.paisId"
                variant="outlined"
                density="compact"
                color="primary"
                label="País *"
                :items="props.datosMaestros.paises"
                item-value="id"
                item-title="descripcion"
                :search-input.sync="catalogos.paisSearch"
                @update:search="catalogos.buscarPaises"
                :loading="catalogos.paisLoading"
              >
                <template #prepend-inner>
                  <WorldIcon size="16" stroke-width="1.5" />
                </template>
              </v-autocomplete>
            </v-col>
            <v-col cols="12" sm="6">
              <v-autocomplete
                v-model="direccionPrincipal.provinciaNombre"
                :update:model-value="direccionPrincipal.provinciaId"
                  variant="outlined"
                density="compact"
                color="primary"
                label="Provincia *"
                :items="props.datosMaestros.provincias"
                item-value="id"
                item-title="descripcion"
                 :search-input.sync="catalogos.provinciaSearch"
                 @update:search="catalogos.buscarProvincias"
                :loading="catalogos.provinciaLoading"
              >
                <template #prepend-inner>
                  <MapIcon size="16" stroke-width="1.5" />
                </template>
              </v-autocomplete>
            </v-col>
            <v-col cols="12" sm="6">
              <v-text-field
                :model-value="direccionPrincipal.direccion"
                @update:model-value="direccionPrincipal.direccion = $event"
                variant="outlined"
                density="compact"
                color="primary"
                label="Calle y Número *"
                maxlength="200"
                counter
                placeholder="Ej: Av. Principal #123, Sector Los Pinos"
              >
                <template #prepend-inner>
                  <RoadIcon size="16" stroke-width="1.5" />
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" sm="6">
              <v-text-field
                :model-value="direccionPrincipal.telefono"
                @update:model-value="direccionPrincipal.telefono = $event"
                variant="outlined"
                density="compact"
                color="primary"
                label="Teléfono Residencial"
                maxlength="15"
                placeholder="Ej: (*************"
              >
                <template #prepend-inner>
                  <PhoneCallIcon size="16" stroke-width="1.5" />
                </template>
              </v-text-field>
            </v-col>
          </v-row>
        </v-card>
      </v-col>

      <!-- SECCIÓN: CONTACTO -->
      <v-col cols="12">
        <v-card class="pa-6 mb-4" elevation="2">
          <h6 class="text-h6 mb-4 d-flex align-center">
            <PhoneCallIcon size="20" stroke-width="1.5" class="mr-2" />
            Información de Contacto
          </h6>
          <v-divider class="mb-4"></v-divider>
          
          <v-row>
            <v-col cols="12" sm="6">
              <v-text-field
                :model-value="props.item.telefonoCelular"
                variant="outlined"
                density="compact"
                color="primary"
                label="Teléfono Celular *"
                maxlength="15"
                placeholder="Ej: (*************"
              >
                <template #prepend-inner>
                  <DeviceMobileIcon size="16" stroke-width="1.5" />
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" sm="6">
              <v-text-field
                :model-value="props.item.correoElectronico"
                variant="outlined"
                density="compact"
                color="primary"
                label="Correo Electrónico *"
                type="email"
                placeholder="<EMAIL>"
                hint="Utilice un email al que tenga acceso; allí enviaremos información importante."
                persistent-hint
              >
                <template #prepend-inner>
                  <MailIcon size="16" stroke-width="1.5" />
                </template>
              </v-text-field>
            </v-col>
          </v-row>
        </v-card>
      </v-col>

      <!-- SECCIÓN: REDES SOCIALES -->
      <v-col cols="12">
        <v-card class="pa-6 mb-4" elevation="2">
          <h6 class="text-h6 mb-4 d-flex align-center">
            <BrandFacebookIcon size="20" stroke-width="1.5" class="mr-2" />
            Redes Sociales (Opcional)
          </h6>
          <v-divider class="mb-4"></v-divider>
          
          <v-row>
            <v-col cols="12" sm="4">
              <v-text-field
                v-model="facebook"
                variant="outlined"
                density="compact"
                color="primary"
                label="Facebook"
                placeholder="@usuario o URL completa"
              >
                <template #prepend-inner>
                  <BrandFacebookIcon size="16" stroke-width="1.5" />
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" sm="4">
              <v-text-field
                v-model="instagram"
                variant="outlined"
                density="compact"
                color="primary"
                label="Instagram"
                placeholder="@usuario o URL completa"
              >
                <template #prepend-inner>
                  <BrandInstagramIcon size="16" stroke-width="1.5" />
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" sm="4">
              <v-text-field
                v-model="twitter"
                variant="outlined"
                density="compact"
                color="primary"
                label="X (Twitter)"
                placeholder="@usuario o URL completa"
              >
                <template #prepend-inner>
                  <BrandXingIcon size="16" stroke-width="1.5" />
                </template>
              </v-text-field>
            </v-col>
          </v-row>
        </v-card>
      </v-col>

      <!-- SECCIÓN: CONTACTOS ADICIONALES -->
      <v-col cols="12">
        <v-card class="pa-6 mb-4" elevation="2">
          <h6 class="text-h6 mb-4 d-flex align-center">
            <UserIcon size="20" stroke-width="1.5" class="mr-2" />
            Contactos de Emergencia
            <v-spacer />
            <v-btn
              color="primary"
              size="small"
              :disabled="contactos.length >= maxContactos"
              @click="abrirDialogoContacto()"
            >
              <v-icon left size="16">mdi-plus</v-icon>
              Agregar Contacto
            </v-btn>
          </h6>
          <v-divider class="mb-4"></v-divider>
          
          <v-row v-if="contactos.length === 0">
            <v-col cols="12">
              <v-card variant="outlined" class="pa-8 text-center">
                <v-icon size="48" color="grey-lighten-1" class="mb-3">mdi-account-plus</v-icon>
                <h6 class="text-h6 mb-2 text-grey">No hay contactos de emergencia</h6>
                <p class="text-body-2 text-grey mb-4">
                  Agregue al menos un contacto de emergencia para casos necesarios.
                </p>
                <v-btn 
                  color="primary" 
                  variant="outlined"
                  @click="abrirDialogoContacto()"
                >
                  <v-icon left>mdi-plus</v-icon>
                  Agregar Primer Contacto
                </v-btn>
              </v-card>
            </v-col>
          </v-row>

          <v-row v-else>
            <v-col cols="12">
              <v-table density="compact" class="contactos-table">
                <thead>
                  <tr>
                    <th class="text-left">Parentesco</th>
                    <th class="text-left">Teléfono</th>
                    <th class="text-left">Email</th>
                    <th class="text-center" width="120">Acciones</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(contacto, index) in contactos" :key="index">
                    <td>
                      <div class="d-flex align-center">
                        <v-icon size="16" color="primary" class="mr-2">mdi-account</v-icon>
                        {{ contacto.parentesco }}
                      </div>
                    </td>
                    <td>
                      <div class="d-flex align-center">
                        <v-icon size="16" color="success" class="mr-2">mdi-phone</v-icon>
                        {{ contacto.numero }}
                      </div>
                    </td>
                    <td>
                      <div class="d-flex align-center" v-if="contacto.email">
                        <v-icon size="16" color="info" class="mr-2">mdi-email</v-icon>
                        {{ contacto.email }}
                      </div>
                      <span v-else class="text-grey">-</span>
                    </td>
                    <td class="text-center">
                      <v-btn 
                        icon 
                        size="small" 
                        color="primary" 
                        variant="text" 
                        @click="abrirDialogoContacto(index)"
                      >
                        <v-icon size="16">mdi-pencil</v-icon>
                      </v-btn>
                      <v-btn 
                        icon 
                        size="small" 
                        color="error" 
                        variant="text" 
                        @click="eliminarContacto(index)"
                      >
                        <v-icon size="16">mdi-delete</v-icon>
                      </v-btn>
                    </td>
                  </tr>
                </tbody>
              </v-table>
              
              <v-alert 
                v-if="contactos.length >= maxContactos" 
                type="info" 
                variant="tonal" 
                class="mt-4"
                density="compact"
              >
                <template #prepend>
                  <v-icon>mdi-information</v-icon>
                </template>
                Ha alcanzado el límite máximo de {{ maxContactos }} contactos de emergencia.
              </v-alert>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>

    <!-- MODAL PARA CONTACTOS -->
    <v-dialog v-model="dialogContacto" max-width="500px" persistent>
      <v-card>
        <v-card-title class="d-flex align-center pa-6 pb-4">
          <v-icon left class="mr-3">mdi-account-plus</v-icon>
          <span class="text-h5">
            {{ editIndex !== null ? 'Editar Contacto' : 'Agregar Contacto de Emergencia' }}
          </span>
        </v-card-title>
        
        <v-card-text class="pa-6">
          <v-row>
            <v-col cols="12">
              <v-text-field
                v-model="contactoForm.parentesco"
                label="Parentesco o Relación *"
                variant="outlined"
                density="compact"
                color="primary"
                placeholder="Ej: Madre, Padre, Hermano, Amigo..."
                maxlength="50"
                counter
              >
                <template #prepend-inner>
                  <v-icon size="16">mdi-account-heart</v-icon>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12">
              <v-text-field
                v-model="contactoForm.numero"
                label="Número de Teléfono *"
                variant="outlined"
                density="compact"
                color="primary"
                placeholder="Ej: (*************"
                maxlength="15"
                counter
              >
                <template #prepend-inner>
                  <v-icon size="16">mdi-phone</v-icon>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12">
              <v-text-field
                v-model="contactoForm.email"
                label="Correo Electrónico (Opcional)"
                variant="outlined"
                density="compact"
                color="primary"
                type="email"
                placeholder="<EMAIL>"
              >
                <template #prepend-inner>
                  <v-icon size="16">mdi-email</v-icon>
                </template>
              </v-text-field>
            </v-col>
          </v-row>
        </v-card-text>
        
        <v-card-actions class="pa-6 pt-0">
          <v-spacer />
          <v-btn 
            color="grey" 
            variant="outlined" 
            @click="dialogContacto = false"
          >
            <v-icon left>mdi-close</v-icon>
            Cancelar
          </v-btn>
          <v-btn 
            color="primary" 
            variant="flat" 
            @click="guardarContacto"
            :disabled="!contactoForm.parentesco || !contactoForm.numero"
          >
            <v-icon left>mdi-content-save</v-icon>
            Guardar
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<style scoped>
.v-card {
  border-radius: 8px;
  background: var(--v-theme-surface);
  color: var(--v-theme-on-surface);
  transition: box-shadow 0.3s, background 0.3s, color 0.3s;
}

.v-card:hover {
  box-shadow: 0 6px 12px rgba(0,0,0,0.08) !important;
}

.text-h6 {
  font-weight: 500;
  color: var(--v-theme-on-surface);
}

.contactos-table {
  border: 1px solid var(--v-theme-outline-variant, #4444);
  border-radius: 6px;
  overflow: hidden;
  background: var(--v-theme-surface);
}

.contactos-table th {
  background-color: var(--v-theme-surface-variant, #232323);
  font-weight: 600;
  font-size: 13px;
  color: var(--v-theme-on-surface);
}

.contactos-table td {
  font-size: 13px;
  color: var(--v-theme-on-surface);
}

@media (max-width: 600px) {
  .v-card {
    padding: 1rem !important;
  }
  .text-h6 {
    font-size: 1.1rem !important;
  }
}
</style>