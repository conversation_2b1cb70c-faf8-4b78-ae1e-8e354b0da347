<script setup lang="ts">
  import { reactive } from 'vue'
  import { defineExpose } from 'vue'
  import type {  SolicitudAdmisionDto } from '@/utils/models/Academico/SolicitudAdmisionDto';
  import type { PreguntaEncabezadoDto } from '@/utils/models/Academico/SolicitudAdmisionPregunta.Dto';
  
  interface OpcionDto {
    descripcion: string;
    valor: number;
  }

  interface PreguntaDto extends PreguntaEncabezadoDto {
    opciones?: OpcionDto[];
  }
  
  import { HelpIcon, InfoCircleIcon } from 'vue-tabler-icons'
  
  // Cada pregunta puede tener un ID, un texto y un listado de opciones
  interface Question {
    id: number
    text: string
    options: { label: string; value: number }[]
  }

  // al editar tomamos los datos del componente padre
const props = defineProps<{
  item: SolicitudAdmisionDto,
  datosMaestros: any;
}>()

// Si no hay preguntas en props.item.preguntas, inicializa con dos vacías:
const questions: Question[] = (props.item.preguntas && props.item.preguntas.length > 0)
  ? props.item.preguntas.map((p: any) => ({
      id: p.id || 0,
      text: p.pregunta || '',
      options: (p.opciones ?? []).map((o: any) => ({
        label: o.descripcion || '',
        value: o.valor ?? 0
      }))
    }))
  : [];
  
  // Definimos el arreglo de preguntas
/*   const questions: Question[] = [
    {
      id: 1,
      text: '¿Tiene dificultad para ver, incluso cuando usa lentes?',
      options: [
        { label: '1. No, ninguna dificultad.', value: 1 },
        { label: '2. Sí, cierta dificultad.', value: 2 },
        { label: '3. Sí, mucha dificultad.', value: 3 },
        { label: '4. No puedo ver en lo absoluto.', value: 4 },
        { label: '5. No puedo realizar esta actividad.', value: 5 }
      ]
    },
    {
      id: 2,
      text: '¿Tiene dificultad para oír, incluso cuando usa audífonos?',
      options: [
        { label: '1. No, ninguna dificultad.', value: 1 },
        { label: '2. Sí, cierta dificultad.', value: 2 },
        { label: '3. Sí, mucha dificultad.', value: 3 },
        { label: '4. No puedo oír en lo absoluto.', value: 4 },
        { label: '5. No puedo realizar esta actividad.', value: 5 }
      ]
    },
    {
      id: 3,
      text: '¿Tiene dificultad para caminar o subir escalones?',
      options: [
        { label: '1. No, ninguna dificultad.', value: 1 },
        { label: '2. Sí, cierta dificultad.', value: 2 },
        { label: '3. Sí, mucha dificultad.', value: 3 },
        { label: '4. No puedo caminar en lo absoluto.', value: 4 },
        { label: '5. No puedo realizar esta actividad.', value: 5 }
      ]
    },
    // Agrega más preguntas según necesites...
  ]
   */
  // Objeto reactivo que almacena la respuesta seleccionada para cada pregunta
  // La clave es el "id" de la pregunta, y el valor es el "value" de la opción elegida
  const answers = reactive<Record<number, number>>({})


  defineExpose({
  getDatos: () => ({
    respuestas: { ...answers }
  })
})
  
  </script>
  


<template>
  <v-card class="pa-6 mb-4" elevation="2">
    <v-row>
      <!-- Encabezado de la sección Preguntas -->
      <v-col cols="12">
        <h6 class="text-h6 d-flex align-center mb-2">
          <HelpIcon size="20" stroke-width="1.5" class="mr-2" />
          Preguntas
        </h6>
        <v-divider class="mb-4" />
      </v-col>

      <!-- Mostrar mensaje si no hay preguntas -->
      <v-col
        v-if="questions.length === 0"
        cols="12"
        class="d-flex flex-column align-center justify-center py-10"
      >
        <InfoCircleIcon size="48" stroke-width="1.5" class="mb-2" color="#90a4ae" />
        <span class="text-subtitle-1" style="color: #90a4ae;">
          No existen preguntas para mostrar
        </span>
      </v-col>

      <!-- Iterar sobre la lista de preguntas -->
      <v-col
        v-for="question in questions"
        :key="question.id"
        cols="12"
        class="mb-4"
        v-else
      >
        <!-- Enunciado de la pregunta -->
        <p class="mb-2 font-weight-medium">
          {{ question.id }}. {{ question.text }}
        </p>

        <!-- Grupo de Radio (una sola respuesta) -->
        <v-radio-group
          v-model="answers[question.id]"
          class="ml-4"
        >
          <!-- Iterar sobre las opciones de respuesta -->
          <v-radio
            v-for="(option, idx) in question.options"
            :key="idx"
            :label="option.label"
            :value="option.value"
          />
        </v-radio-group>
      </v-col>
    </v-row>
  </v-card>
</template>
  
  
  <style scoped>
  .mb-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-4 {
    margin-bottom: 1rem !important;
  }
  .ml-4 {
    margin-left: 1rem !important;
  }
  .d-flex {
    display: flex;
  }
  .align-center {
    align-items: center;
  }
  .font-weight-medium {
    font-weight: 500;
  }
  .mr-2 {
    margin-right: 0.5rem;
  }
  </style>
