<script setup lang="ts">
import { ref } from "vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import wFormData from "@/components/apps/wFormData.vue";
import { head } from "lodash";

const headers: Array<any> = [
  {
    text: "ID",
    value: "id",
    align: "start",
    sortable: true,
  },
  {
    text: "Nombre",
    value: "nombre",
    align: "start",
    sortable: true,
  },
  {
    text: "Fecha Inicio",
    value: "fechaInicio",
    align: "start",
    sortable: true,
  },
  {
    text: "Fecha Fin",
    value: "fechaFin",
    align: "start",
    sortable: true,
  },
];

const cohorte = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let loading = ref(false);
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Cohorte'"
    :filters="null"
    :headers="headers"
    :items="cohorte.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="cohorte.pageNumber"
    :pageSize="cohorte.pageSize"
    :totalPages="cohorte.totalPages"
    :totalRecords="cohorte.totalRecords"
  >
    <template #editItemPanel>
      <h1>jdsj</h1>
    </template>
  </wFormData>
</template>
