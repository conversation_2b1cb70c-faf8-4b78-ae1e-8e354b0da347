<template>
  <wFormData
    :panel="true"
    :loading="loading"
    title="Expediente Estudiante"
    :filters="null"
    :headers="headers"
    :items="estudiantes.items"
    icon="mdi-folder-open-outline"
    :text="``"
    dialogWidth="1300px"
    :filtervalue="null"
    :pageNumber="estudiantes.pageNumber"
    :pageSize="estudiantes.pageSize"
    :totalPages="estudiantes.totalPages"
    :totalRecords="estudiantes.totalRecords"
    @editItem="editItem"
    @searchItem="searchItem"
  >
    <template #editItemPanel>
      <v-row>
        <v-col cols="12" sm="12" md="4" lg="3">
          <wProfileCard
            :codigo="estudiante.matricula"
            :firstName="estudiantePersona.nombres"
            :lastName="estudiantePersona.apellidos"
            iconIdentificacion="mdi-school"
            :identificacion="estudianteCarreradd.carreraDescripcion"
            email="<EMAIL>"
            :status="estudianteCarreradd.estadoEstudainteDescripcion"
            :activo="estudianteCarreradd.activo"
          />
        </v-col>
        <v-col cols="12" sm="12" md="8" lg="9">
          <wTabsCard :tabs="tabs">
            <template #personal>
              <tpersonal :persona="estudiante.persona" />
            </template>

            <template #academico>
              <tacademicos />
            </template>

            <template #calificaciones>
              <tcalificaciones />
            </template>

            <template #documentos>
              <div>Contenido de la pestaña Documentación</div>
            </template>
          </wTabsCard>
        </v-col>
      </v-row>
    </template>
  </wFormData>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import wFormData from "@/components/apps/wFormData.vue";
import wProfileCard from "@/components/apps/wProfileCard.vue";
import wTabsCard from "@/components/apps/wTabsCard.vue";
import tpersonal from "@/views/pages/Operaciones/Academicos/estudiantes/tabs/personal.vue";
import tacademicos from "@/views/pages/Operaciones/Academicos/estudiantes/tabs/academicos.vue";
import tcalificaciones from "@/views/pages/Operaciones/Academicos/estudiantes/tabs/calificaciones.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { EstudianteService } from "@/services/Academicos/EstudianteService";
import type { estudianteCarrera } from "@/utils/models/Academico/estudianteCarrera";
import type { Estudiante } from "@/utils/models/Academico/estudiante";
import type Persona from "@/utils/models/Generales/persona";

// Estado reactivo
const loading = ref(false);

const headers = ref([
  { title: "", key: "foto", align: "start", sortable: true },
  { title: "Matrícula", key: "matricula", align: "start", sortable: true },
  { title: "Nombre", key: "persona.nombres", align: "start", sortable: true },
  {
    title: "Apellido",
    key: "persona.apellidos",
    align: "start",
    sortable: true,
  },
  { title: "Acciones", key: "acciones", align: "start", sortable: true },
]);

const tabs = [
  { label: "Personal", value: "personal", icon: "mdi-account", content: "" },
  { label: "Académicos", value: "academico", icon: "mdi-school", content: "" },
  {
    label: "Calificaciones",
    value: "calificaciones",
    icon: "mdi-pen-plus",
    content: "",
  },
  {
    label: "Documentación",
    value: "documentos",
    icon: "mdi-file-document-check",
    content: "",
  },
];

const estudiantes = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 0,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});
const estudianteCarreradd = ref<estudianteCarrera>({} as estudianteCarrera);
const estudiante = ref<Estudiante>({} as Estudiante);
const estudiantePersona = ref<Persona>({} as Persona);

// Montaje del componente
onMounted(async () => {
  await searchItem("");
});

// Función de búsqueda
async function searchItem(value: string, pageNumber = 1, pageSize = 10) {
  loading.value = true;
  const response = await EstudianteService.getEstudiantes(
    value,
    pageNumber,
    pageSize
  );
  estudiantes.value = response;
  loading.value = false;
}

// Función de edición (falta integrar en lógica del componente si es necesario)

function editItem(item: Estudiante) {
  Object.assign(estudiantePersona.value, item.persona);
  Object.assign(estudiante.value, item);

  // Copiar la primera carrera del estudiante si existe
  const primeraCarrera = item.estudianteCarreras?.[0];
  if (primeraCarrera) {
    Object.assign(estudianteCarreradd.value, primeraCarrera);
  }

  console.log(estudianteCarreradd.value);
}
</script>
