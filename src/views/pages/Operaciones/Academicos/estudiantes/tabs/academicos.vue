<template>
  <v-row>
    <v-col xs12>
      <v-toolbar color="primary" class="rounded" density="compact" flat>
        <v-btn icon="mdi-school"></v-btn>
        <v-toolbar-title>Carreras</v-toolbar-title>
        <v-spacer></v-spacer> <v-btn><v-icon>mdi-plus</v-icon> Agregar</v-btn>
      </v-toolbar>
    </v-col>
    <v-col cols="12">
      <v-card elevation="5" rounded="md" class="pa-4 position-relative">
        <!-- Botón flotante arriba a la derecha -->
        <v-btn
          icon="mdi-pencil"
          variant="text"
          class="position-absolute top-0 right-0 mt-2 mr-2"
        />

        <v-row>
          <v-col cols="12" sm="8">
            <v-card-title class="d-flex align-center pb-2">
              <v-chip
                color="success"
                size="x-small"
                class="mr-2"
                variant="flat"
              >
                Activo
              </v-chip>
              INGENIERÍA EN SISTEMAS
            </v-card-title>

            <h3 class="text-subtitle-1 font-weight-medium mb-2">
              PLAN DE SISTEMAS COMPUTACIONALES V1
            </h3>
            <div><v-icon size="x-samll">mdi-account-group-outline</v-icon> <strong>Cohorte:</strong> 2-2025</div>
            <div><v-icon size="x-samll">mdi-office-building</v-icon> <strong>Recinto:</strong> DISTRITO NACIONAL</div>
          </v-col>

          <v-col cols="12" sm="4" class="text-center">
            <v-progress-circular
              :model-value="20"
              :rotate="360"
              :size="100"
              :width="15"
              color="primary"
            >
              <template #default>20%</template>
            </v-progress-circular>
          </v-col>
          <v-col cols="12">
            <v-divider></v-divider>
          </v-col>
          <v-col cols="12" sm="6">
            <div><strong>Inició:</strong> 2009-03-30</div>
            <div><strong>Se Graduará:</strong> 2013-03-30</div>
            <div><strong>Se Graduó:</strong> 2013-03-30</div>
            <div><strong>Honor Académico:</strong> -</div>
            <div><strong>Admitido Como:</strong> NUEVO INGRESO</div>
          </v-col>

          <v-col cols="12" sm="6">
            <div><strong>Institución:</strong> NT</div>
            <div><strong>Univ. Transf.:</strong> -</div>
            <div><strong>Grado Obtenido:</strong> -</div>
            <div><strong>Cuatrimestre Actual:</strong> 0</div>
          </v-col>
        </v-row>
      </v-card>
    </v-col>
    <!-- Panel Expansion Informativo -->
    <v-col cols="12">
      <div>
        <v-expansion-panels v-model="panel" multiple rounded="md">
          <v-expansion-panel value="cambio estados">
            <template #title>
              <v-icon color="primary"
                >mdi-swap-horizontal-circle-outline</v-icon
              >

              &nbsp; Cambios de Estado</template
            >

            <template #text>
              <wDataTable
                :add="false"
                :panel="false"
                :dialogWidth="'400px'"
                :loading="false"
                :title="'Correos Electrónicos'"
                :headers="headerCambioEstado"
                :items="[]"
                :pageNumber="1"
                :pageSize="10"
                :totalPages="1"
                :totalRecords="0"
              >
              </wDataTable>
            </template>
          </v-expansion-panel>

          <v-expansion-panel value="cambio carrera">
            <template #title>
              <v-icon color="primary">mdi-briefcase-arrow-left-right</v-icon>

              &nbsp; Cambios de carrera</template
            >

            <template #text>
              <wDataTable
                :add="false"
                :panel="false"
                :dialogWidth="'400px'"
                :loading="false"
                :title="'Correos Electrónicos'"
                :headers="headerCambioCarrera"
                :items="[]"
                :pageNumber="1"
                :pageSize="10"
                :totalPages="1"
                :totalRecords="0"
              >
              </wDataTable>
            </template>
          </v-expansion-panel>

          <v-expansion-panel value="cambio recintos">
            <template #title>
              <v-icon color="primary">mdi-city-switch</v-icon>

              &nbsp; Cambios de Recintos</template
            >

            <template #text>
              <wDataTable
                :add="false"
                :panel="false"
                :dialogWidth="'400px'"
                :loading="false"
                :title="'Correos Electrónicos'"
                :headers="headerCambioRecinto"
                :items="[]"
                :pageNumber="1"
                :pageSize="10"
                :totalPages="1"
                :totalRecords="0"
              >
              </wDataTable>
            </template>
          </v-expansion-panel>
        </v-expansion-panels>
      </div>
    </v-col>
  </v-row>
</template>
<script lang="ts">
import wDataTable from "@/components/apps/wDataTable.vue";
export default {
  name: "tacademicos",
  components: {
    wDataTable,
  },
  data() {
    return {
      panel: [],
      headers: [
        { title: "Tipo Documento", key: "tipoDocumentoDescripción" },
        { title: "Documento", key: "documento" },
        { title: "Acciones", key: "acciones" },
      ],

      headersCorreo: [
        { title: "Tipo Correo", key: "tipoCorreoDescripciópn" },
        { title: "Correo", key: "email" },
        { title: "Acciones", key: "acciones" },
      ],

      headersTelefono: [
        { title: "Tipo Teléfono", key: "tipoCorreoDescripciópn" },
        { title: "Teléfono", key: "email" },
        { title: "Acciones", key: "acciones" },
      ],

      headerCambioEstado: [
        { title: "Fecha", key: "tipoCorreoDescripciópn" },
        { title: "Carrera", key: "email" },
        { title: "Estado Anterior", key: "estadoAnterior" },
        { title: "Nuevo Estado", key: "nuevoEstado" },
        { title: "Realizado por", key: "usuario" },
      ],
      headerCambioCarrera: [
        { title: "Fecha", key: "tipoCorreoDescripciópn" },
        { title: "Carrera Anterior", key: "email" },
        { title: "Carrera Nueva", key: "estadoAnterior" },
        { title: "Realizado por", key: "usuario" },
      ],
      headerCambioRecinto: [
        { title: "Fecha", key: "tipoCorreoDescripciópn" },
        { title: "Recinto Anterior", key: "email" },
        { title: "Recinto Nuevo", key: "estadoAnterior" },
        { title: "Realizado por", key: "usuario" },
      ],
    };
  },
};
</script>
