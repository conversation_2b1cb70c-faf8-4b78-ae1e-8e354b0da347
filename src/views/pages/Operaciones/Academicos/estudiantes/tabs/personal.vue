<template>
  <v-col cols="12" class="mx-auto">
    <v-card>
      <v-toolbar color="primary" class="rounded" density="compact" flat>
        <v-btn icon="mdi-account"></v-btn>
        <v-toolbar-title>Datos Personales</v-toolbar-title>
      </v-toolbar>
      <v-card-text>
        <v-form ref="form" lazy-validation>
          <v-row>
            <v-col cols="12" sm="6">
              <v-text-field
                label="Nombres"
                prepend-inner-icon="mdi-account-outline"
                variant="outlined"
                required
                :rules="[(v) => !!v || 'El nombre es requerido']"
                v-model="persona.PersNombres"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6">
              <v-text-field
                label="Apellidos"
                prepend-inner-icon="mdi-account-outline"
                variant="outlined"
                required
                :rules="[(v) => !!v || 'El apellido es requerido']"
                v-model="persona.PersApellidos"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="4">
              <v-text-field
                label="Fecha de Nacimiento"
                prepend-inner-icon="mdi-calendar-outline"
                variant="outlined"
                type="date"
                :rules="[(v) => !!v || 'La fecha es requerida']"
                v-model="persona.PersFechaNacimiento"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="4">
              <v-autocomplete
                label="Sexo"
                prepend-inner-icon="mdi-gender-male-female"
                variant="outlined"
                required
                :rules="[(v) => !!v || 'El sexo es requerido']"
                v-model="persona.SexoID"
                :items="sexos"
                item-title="SexoDescripcion"
                item-value="SexoID"
              ></v-autocomplete>
            </v-col>
            <v-col cols="12" sm="4">
              <v-autocomplete
                label="Estado Civil"
                prepend-inner-icon="mdi-account-group"
                variant="outlined"
                required
                :rules="[(v) => !!v || 'El estado civil es requerido']"
                v-model="persona.EstadoCivilID"
                :items="estadosCiviles"
                item-title="EstadoCivilDescripcion"
                item-value="EstadoCivilID"
              ></v-autocomplete>
            </v-col>
            <v-col cols="12" sm="6">
              <v-autocomplete
                label="País"
                prepend-inner-icon="mdi-map"
                variant="outlined"
                required
                :rules="[(v) => !!v || 'El país es requerido']"
                v-model="persona.PaisID"
                :items="paises"
                item-title="PaisDescripcion"
                item-value="PaisID"
              ></v-autocomplete>
            </v-col>
            <v-col cols="12" sm="6">
              <v-autocomplete
                label="Nacionalidad"
                prepend-inner-icon="mdi-flag"
                variant="outlined"
                required
                :rules="[(v) => !!v || 'La nacionalidad es requerida']"
                v-model="persona.NacionalidadID"
                :items="nacionalidades"
                item-title="NacionalidadDescripcion"
                item-value="NacionalidadID"
              ></v-autocomplete>
            </v-col>
            <v-col cols="12" sm="6">adfasfas
              <v-autocomplete
                label="Ciudad"
                prepend-inner-icon="mdi-city"
                variant="outlined"
                required
                :rules="[(v) => !!v || 'La ciudad es requerida']"
                v-model="persona.CiudadID"
                :items="ciudades"
                item-title="CiudadDescripcion"
                item-value="CiudadID"
              ></v-autocomplete>
            </v-col>
            <v-col cols="12" sm="6">
              <v-autocomplete
                label="Tipo de Sangre"
                prepend-inner-icon="mdi-blood-bag"
                variant="outlined"
                v-model="persona.TipoSangreID"
                :items="tiposSangre"
                item-title="TipoSangreDescripcion"
                item-value="TipoSangreID"
              ></v-autocomplete>
            </v-col>
            <v-col cols="12" sm="6">
              <v-textarea
                label="Alergias"
                prepend-inner-icon="mdi-alert"
                variant="outlined"
                v-model="persona.PersAlergico"
              ></v-textarea>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </v-card>
  </v-col>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { SexoServices } from '@/services/Generales/SexoServices';
import { EstadoCivilService } from '@/services/Generales/EstadoCivilServices';
import { PaisesService } from '@/services/Generales/PaisesServices';
import { NacionalidadService } from '@/services/Generales/NacionalidadServices';
import { CiudadesService } from '@/services/Generales/CiudadesServices';
import { TipoSangreService } from '@/services/Generales/TipoSangreServices';

const props = defineProps<{
  persona: any;
}>();

const sexos = ref([]);
const estadosCiviles = ref([]);
const paises = ref([]);
const nacionalidades = ref([]);
const ciudades = ref([]);
const tiposSangre = ref([]);

onMounted(async () => {
  try {
    const [sexosResponse, estadosCivilesResponse, paisesResponse, nacionalidadesResponse, ciudadesResponse, tiposSangreResponse] = await Promise.all([
      SexoServices.getSexos(),
      EstadoCivilService.getEstadosCiviles(),
      PaisesService.getPaises(),
      NacionalidadService.getNacionalidades(),
      CiudadesService.getCiudades(),
      TipoSangreService.getTiposSangre()
    ]);
    sexos.value = sexosResponse;
    estadosCiviles.value = estadosCivilesResponse;
    paises.value = paisesResponse;
    nacionalidades.value = nacionalidadesResponse;
    ciudades.value = ciudadesResponse;
    tiposSangre.value = tiposSangreResponse;
  } catch (error) {
    console.error('Error fetching data:', error);
  }
});
</script> 