<template>
  <v-col cols="12" class="mx-auto">
    <v-card>
      <v-toolbar color="primary" class="rounded" density="compact" flat>
        <v-btn icon="mdi-pen-plus"></v-btn>
        <v-toolbar-title>Calificaciones</v-toolbar-title>
      </v-toolbar>
      <v-card-text>
        <v-col cols="12">
          <v-autocomplete prepend-inner-icon="mdi-school" label="Carreras"></v-autocomplete>
        </v-col>
        <v-col cols="12">
          <wDataTable
            :add="false"
            :panel="false"
            :dialogWidth="'400px'"
            :loading="false"
            :title="'Correos Electrónicos'"
            :headers="headers"
            :items="[]"
            :pageNumber="1"
            :pageSize="10"
            :totalPages="1"
            :totalRecords="0"
          >
          </wDataTable>
        </v-col>
      </v-card-text>
    </v-card>
  </v-col>
</template>

<script lang="ts">
import wDataTable from "@/components/apps/wDataTable.vue";

export default {
  name: "TCalificaciones",
  components: {
    wDataTable,
  },
  data() {
    return {
      headers: [
        { title: "Fecha", key: "tipoDocumentoDescripción" },
        { title: "Asignatura", key: "documento" },
        { title: "Sección", key: "acciones" },
        { title: "Nota", key: "acciones" },

        { title: "Literal", key: "acciones" },
        { title: "Puntos", key: "acciones" },
      
      ],
    };
  },
};
</script>
