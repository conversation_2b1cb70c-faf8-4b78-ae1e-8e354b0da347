<template>
  <v-col cols="12" class="mx-auto">
    <v-card>
      <v-toolbar color="primary" class="rounded" density="compact" flat>
        <v-btn icon="mdi-account-key"></v-btn>
        <v-toolbar-title>Sedes Asignadas</v-toolbar-title>
      </v-toolbar>
      <v-card-text>
      <v-data-table 
        :headers="headers"
        :items="sedes"
        item-value="id"
        class="elevation-1"
        density="compact"
      >
        <template v-slot:item.usSeDefault="{ item }">
          <v-radio
            :value="item.id"
            v-model="selectedDefaultSedeId"
          ></v-radio>
        </template>
        <template v-slot:item.estatus="{ item }">
          <v-checkbox-btn
            :model-value="item.estatus"
            @update:modelValue="handleEstatusChange(item, $event)"
          ></v-checkbox-btn>
        </template>
      </v-data-table>
    </v-card-text>
    </v-card>
  </v-col>
</template>

<script setup lang="ts">
import { ref, defineProps, watch, defineEmits, onMounted } from 'vue';
import type { PropType } from 'vue';


const headers = ref([
  { title: 'Nombre', key: 'nombre', align: 'center', sortable: true },
  { title: 'Por Defecto', key: 'usSeDefault', align: 'center', sortable: false },
  { title: 'Estatus', key: 'estatus', align: 'center', sortable: false },
]);

const selectedDefaultSedeId = ref<number | null>(null);

interface Sede {
  id: number;
  sedeId: number;
  nombre: string;
  usSeDefault: boolean;
  estatus: boolean;
}

const props = defineProps({
  sedes: {
    type: Array as PropType<Sede[]>,
    default: () => [],
  },
});

const emits = defineEmits(['update:sedes']);

onMounted(() => {
  const defaultSede = props.sedes.find(sede => sede.usSeDefault);
  selectedDefaultSedeId.value = defaultSede ? defaultSede.id : null;
});

watch(() => props.sedes, (newSedes) => {
  const defaultSede = newSedes.find(sede => sede.usSeDefault);
  selectedDefaultSedeId.value = defaultSede ? defaultSede.id : null;
}, { deep: true });

watch(selectedDefaultSedeId, (newVal) => {
  if (newVal !== null) {
    const updatedSedes = props.sedes.map(sede => ({
      ...sede,
      usSeDefault: sede.id === newVal,
    }));
    emits('update:sedes', updatedSedes);
  }
});

const handleEstatusChange = (item: Sede, newVal: boolean) => {
  const updatedSedes = props.sedes.map(sede =>
    sede.id === item.id ? { ...sede, estatus: newVal } : sede
  );
  emits('update:sedes', updatedSedes);
};
</script>

<style scoped>
/* Add any specific styles for this component here */
</style>