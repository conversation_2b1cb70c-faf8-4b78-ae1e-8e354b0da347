<template>
  <v-col cols="12" class="mx-auto">
    <v-card>
      <v-toolbar color="primary" class="rounded" density="compact" flat>
        <v-btn icon="mdi-account-key"></v-btn>
        <v-toolbar-title>Informacion Personal</v-toolbar-title>
      </v-toolbar>
      <v-card-text>
      <v-row>
        <v-col cols="12" sm="6">
          <v-text-field
            label="Nombres"
            v-model="persona.nombres"
            variant="outlined"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
          <v-text-field
            label="Apellidos"
            v-model="persona.apellidos"
            variant="outlined"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
          <v-text-field
                label="Fecha de Nacimiento"
                prepend-inner-icon="mdi-calendar-outline"
                variant="outlined"
                type="date"
                v-model="persona.fechaNacimiento"
              ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
          <v-autocomplete
                label="Sexo"
                prepend-inner-icon="mdi-gender-male-female"
                variant="outlined"
                v-model="persona.sexoId"
                :items="sexos"
                item-title="descripcion"
                item-value="id"
              ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6">
          <v-autocomplete
                label="Nacionalidad"
                prepend-inner-icon="mdi-flag"
                variant="outlined"
                v-model="persona.nacionalidadId"
                :items="nacionalidades"
                item-title="descripcion"
                item-value="id"
              ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6">
          <v-autocomplete
                label="Estado Civil"
                prepend-inner-icon="mdi-heart"
                variant="outlined"
                v-model="persona.estadoCivilId"
                :items="estadosCiviles"
                item-title="descripcion"
                item-value="id"
              ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6">
          <v-autocomplete
                label="Pais"
                prepend-inner-icon="mdi-map"
                variant="outlined"
                v-model="persona.paisId"
                :items="paises"
                item-title="descripcion"
                item-value="id"
              ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6">
          <v-autocomplete
                label="Provincia"
                prepend-inner-icon="mdi-map-marker-radius"
                variant="outlined"
                v-model="persona.personaId"
                :items="provincias"
                item-title="descripcion"
                item-value="id"
              ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6">
          <v-autocomplete
                label="Municipio"
                prepend-inner-icon="mdi-city"
                variant="outlined"
                v-model="persona.municipioId"
                :items="municipios"
                item-title="descripcion"
                item-value="id"
              ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6">
          <v-autocomplete
                label="Ciudad"
                prepend-inner-icon="mdi-city-variant"
                variant="outlined"
                v-model="persona.ciudadId"
                :items="ciudades"
                item-title="descripcion"
                item-value="id"
              ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6">
          <v-autocomplete
                label="Tipo de Sangre"
                prepend-inner-icon="mdi-blood-bag"
                variant="outlined"
                v-model="persona.tipoSangreId"
                :items="tiposSangre"
                item-title="descripcion"
                item-value="id"
              ></v-autocomplete>
        </v-col>
        <v-col cols="12">
          <v-textarea
            label="Alergias"
            v-model="persona.alergias"
            variant="outlined"
          ></v-textarea>
        </v-col>
      </v-row>
    </v-card-text>
    </v-card>
  </v-col>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import type Persona from "@/utils/models/Generales/persona";
import { SexoServices } from '@/services/Generales/SexoServices';
import { EstadoCivilService } from '@/services/Generales/EstadoCivilServices';
import { PaisesService } from '@/services/Generales/PaisesServices';
import { NacionalidadService } from '@/services/Generales/NacionalidadServices';
import { CiudadesService } from '@/services/Generales/CiudadesServices';
import { MunicipioServices } from '@/services/Generales/MunicipioServices';
import { ProvinciaService } from '@/services/Generales/ProvinciaServices';
import { TipoSangreService } from '@/services/Generales/TipoSangreServices';

const props = defineProps<{
  persona: any;
}>();

const sexos = ref([]);
const estadosCiviles = ref([]);
const paises = ref([]);
const nacionalidades = ref([]);
const provincias = ref([]);
const municipios = ref([]);
const ciudades = ref([]);
const tiposSangre = ref([]);

const fetchAutocompleteData = async () => {
  try {
    const [sexosResponse, estadosCivilesResponse, paisesResponse, nacionalidadesResponse, provinciasResponse, municipiosResponse, ciudadesResponse, tiposSangreResponse] = await Promise.all([
      SexoServices.getSexos(''),
      EstadoCivilService.getEstadosCiviles(''),
      PaisesService.getPaises(''),
      NacionalidadService.getNacionalidad(''),
      ProvinciaService.getProvincias(''),
      //MunicipioServices.getMunicipios(''),
      //CiudadesService.getCiudades(''),
      TipoSangreService.getTipoSangre('')
    ]);
    sexos.value = sexosResponse && sexosResponse.items ? sexosResponse.items : sexosResponse;
    estadosCiviles.value = estadosCivilesResponse && estadosCivilesResponse.items ? estadosCivilesResponse.items : estadosCivilesResponse;
    paises.value = paisesResponse && paisesResponse.items ? paisesResponse.items : paisesResponse;
    nacionalidades.value = nacionalidadesResponse && nacionalidadesResponse.items ? nacionalidadesResponse.items : nacionalidadesResponse;
    provincias.value = provinciasResponse && provinciasResponse.items ? provinciasResponse.items : provinciasResponse;
    municipios.value = municipiosResponse && municipiosResponse.items ? municipiosResponse.items : municipiosResponse;
    ciudades.value = ciudadesResponse && ciudadesResponse.items ? ciudadesResponse.items : ciudadesResponse;
    tiposSangre.value = tiposSangreResponse && tiposSangreResponse.items ? tiposSangreResponse.items : tiposSangreResponse;

    console.log('Sexos:', sexos.value);
    console.log('Estados Civiles:', estadosCiviles.value);
    console.log('Paises:', paises.value);
    console.log('Nacionalidades:', nacionalidades.value);
    console.log('Provincias:', provincias.value);
    console.log('Municipios:', municipios.value);
    console.log('Ciudades:', ciudades.value);
    console.log('Tipos de Sangre:', tiposSangre.value);
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};

onMounted(async () => {
  await fetchAutocompleteData();
});

watch(() => props.persona, (newVal) => {
  console.log('Persona prop in personal tab:', newVal);
  if (newVal) {
    fetchAutocompleteData();
  }
}, { deep: true, immediate: true });

defineExpose({
  fetchAutocompleteData
});
</script>