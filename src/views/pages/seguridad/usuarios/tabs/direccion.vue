<template>
  <v-col cols="12" class="mx-auto">
    <v-card>
      <v-toolbar color="primary" class="rounded" density="compact" flat>
        <v-btn icon="mdi-account-key"></v-btn>
        <v-toolbar-title>Direccion</v-toolbar-title>
      </v-toolbar>
      <v-card-text>
        <v-row>
          <v-col cols="12">
            <v-textarea label="Direccion" variant="outlined" v-model="direccionData.direccion"></v-textarea>
          </v-col>
          <v-col cols="12" sm="6">
            <v-autocomplete label="Pais" prepend-inner-icon="mdi-map" variant="outlined"
              :items="paises" item-title="descripcion" item-value="id" v-model="direccionData.paisId"></v-autocomplete>
          </v-col>
          <v-col cols="12" sm="6">
            <v-autocomplete label="Provincia" prepend-inner-icon="mdi-map-marker-radius" variant="outlined"
              :items="provincias" item-title="descripcion" item-value="id" v-model="direccionData.provinciaId"></v-autocomplete>
          </v-col>
          <v-col cols="12" sm="6">
            <v-autocomplete label="Municipio" prepend-inner-icon="mdi-city" variant="outlined"
              :items="municipios" item-title="descripcion" item-value="id" v-model="direccionData.municipioId"></v-autocomplete>
          </v-col>
          <v-col cols="12" sm="6">
            <v-autocomplete label="Ciudad" prepend-inner-icon="mdi-city-variant" variant="outlined"
              :items="ciudades" item-title="descripcion" item-value="id" v-model="direccionData.ciudadId"></v-autocomplete>
          </v-col>
          <v-col cols="12" sm="6">
            <v-text-field label="Código Postal"
              variant="outlined" v-model="direccionData.codigoPostal"></v-text-field>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-col>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { PaisesService } from '@/services/Generales/PaisesServices';
import { ProvinciaService } from '@/services/Generales/ProvinciaServices';
import { MunicipioServices } from '@/services/Generales/MunicipioServices';
import { CiudadesService } from '@/services/Generales/CiudadesServices';

const props = defineProps<{
  direccion: any;
}>();

const emit = defineEmits(['update:direccion']);

const paises = ref([]);
const provincias = ref([]);
const municipios = ref([]);
const ciudades = ref([]);

const direccionData = ref({
  direccion: '',
  paisId: null,
  provinciaId: null,
  municipioId: null,
  ciudadId: null,
  codigoPostal: '',
});

const fetchAutocompleteData = async () => {
  try {
    const [paisesResponse, provinciasResponse] = await Promise.all([
      PaisesService.getPaises(''),
      ProvinciaService.getProvincias(''),
    ]);

    paises.value = paisesResponse && paisesResponse.items ? paisesResponse.items : paisesResponse;
    provincias.value = provinciasResponse && provinciasResponse.items ? provinciasResponse.items : provinciasResponse;

    // Cargar municipios y ciudades si hay provincia seleccionada
    if (direccionData.value.provinciaId) {
      await loadMunicipios(direccionData.value.provinciaId);
    }
    if (direccionData.value.municipioId) {
      await loadCiudades(direccionData.value.municipioId);
    }

    console.log('Paises:', paises.value);
    console.log('Provincias:', provincias.value);
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};

const loadMunicipios = async (provinciaId: number) => {
  try {
    const municipiosResponse = await MunicipioServices.getMunicipios('');
    municipios.value = municipiosResponse && municipiosResponse.items ? municipiosResponse.items : municipiosResponse;
  } catch (error) {
    console.error('Error fetching municipios:', error);
  }
};

const loadCiudades = async (municipioId: number) => {
  try {
    const ciudadesResponse = await CiudadesService.getCiudades('');
    ciudades.value = ciudadesResponse && ciudadesResponse.items ? ciudadesResponse.items : ciudadesResponse;
  } catch (error) {
    console.error('Error fetching ciudades:', error);
  }
};

onMounted(async () => {
  await fetchAutocompleteData();
});

watch(() => props.direccion, (newVal) => {
  console.log('Direccion prop in Direccion tab:', newVal);
  if (newVal) {
    direccionData.value = { ...newVal };
    fetchAutocompleteData();
  }
}, { deep: true, immediate: true });

// Watch para emitir cambios cuando se modifique direccionData
watch(direccionData, (newVal) => {
  emit('update:direccion', { ...newVal });
}, { deep: true });

// Watch para cargar municipios cuando cambie la provincia
watch(() => direccionData.value.provinciaId, (newProvinciaId) => {
  if (newProvinciaId) {
    loadMunicipios(newProvinciaId);
    // Limpiar municipio y ciudad cuando cambie la provincia
    direccionData.value.municipioId = null;
    direccionData.value.ciudadId = null;
    municipios.value = [];
    ciudades.value = [];
  }
});

// Watch para cargar ciudades cuando cambie el municipio
watch(() => direccionData.value.municipioId, (newMunicipioId) => {
  if (newMunicipioId) {
    loadCiudades(newMunicipioId);
    // Limpiar ciudad cuando cambie el municipio
    direccionData.value.ciudadId = null;
    ciudades.value = [];
  }
});

defineExpose({
  fetchAutocompleteData
});
</script>