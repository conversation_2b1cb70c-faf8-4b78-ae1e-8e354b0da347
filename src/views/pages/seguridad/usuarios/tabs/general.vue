<template>
  <v-col cols="12" class="mx-auto">
    <v-card>
      <v-toolbar color="primary" class="rounded" density="compact" flat>
        <v-btn icon="mdi-account-key"></v-btn>
        <v-toolbar-title>Datos Generales</v-toolbar-title>
      </v-toolbar>
      <v-card-text>
        <v-form ref="form" lazy-validation>
          <v-row>
            <v-col cols="12" sm="6">
              <v-text-field
                v-model="usuario.usuario"
                label="Nombre de Usuario"
                prepend-inner-icon="mdi-account-outline"
                variant="outlined"
                required
                :rules="[(v) => !!v || 'El nombre de usuario es requerido']"
              >
                <template v-slot:label>
                  Nombre de Usuario <span class="required-asterisk">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" sm="6">
              <v-text-field
                label="Email"
                prepend-inner-icon="mdi-email-outline"
                variant="outlined"
                required
                :rules="[(v) => !!v || 'El email es requerido']"
                v-model="usuario.correo"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6">
              <v-autocomplete
                label="Rol"
                prepend-inner-icon="mdi-account-cog"
                variant="outlined"
                required
                :rules="[(v) => !!v || 'El rol es requerido']"
                v-model="usuario.rolId"
                :items="roles"
                item-title="nombre"
                item-value="id"
              ></v-autocomplete>
            </v-col>
            <v-col cols="12" sm="6">
              <v-text-field
                label="Fecha de Vencimiento de Clave"
                prepend-inner-icon="mdi-calendar-outline"
                variant="outlined"
                type="date"
                v-model="usuario.fechaVencimientoClave"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6">
              <v-switch
                label="Estatus"
                v-model="usuario.activo"
                color="primary"
              ></v-switch>
            </v-col>
            <v-col cols="12" sm="6">
              <v-switch
                label="Cambiar Clave"
                v-model="usuario.cambiarClave"
                color="primary"
              ></v-switch>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </v-card>
  </v-col>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { RolService } from '@/services/Seguridad/RolService';

const props = defineProps<{
  usuario: any;
}>();

const roles = ref([]);

const fetchRoles = async () => {
  try {
    const response = await RolService.getRoles();
    if (response && response.items) {
      roles.value = response.items;
    } else {
      roles.value = response; // Fallback if it's not a paginated response
    }
  } catch (error) {
    console.error('Error fetching roles:', error);
  }
};

onMounted(async () => {
  await fetchRoles();
});

watch(() => props.usuario, (newVal) => {
  console.log('Usuario in general tab:', newVal);
}, { deep: true, immediate: true });

watch(roles, (newVal) => {
  console.log('Roles in general tab:', newVal);
}, { deep: true, immediate: true });
</script> 