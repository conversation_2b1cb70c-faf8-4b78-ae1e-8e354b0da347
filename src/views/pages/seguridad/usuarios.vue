<template>
  <wFormData
    :panel="true"
    :loading="loading"
    title="Gestión de Usuarios"
    :filters="null"
    :headers="headers"
    :items="usuarios.items"
    icon="mdi-folder-open-outline"
    :text="``"
    dialogWidth="1300px"
    :filtervalue="null"
    :pageNumber="usuarios.pageNumber"
    :pageSize="usuarios.pageSize"
    :totalPages="usuarios.totalPages"
    :totalRecords="usuarios.totalRecords"
    @editItem="editItem"
    @searchItem="searchItem"
    @newItem="newItem"
    @save="saveUsuario"
    @update="updateUsuario"
  >
    <template #editItemPanel>
      <v-row>
        <v-col cols="12" sm="12" md="4" lg="3">
          <wProfileCard
            :codigo="usuario.usuario"
            :firstName="usuario.nombres"
            :lastName="usuario.apellidos"
            iconIdentificacion="mdi-account-details"
            :identificacion="usuario.rolNombre"
            :email="usuario.correo"
            :status="usuario.activo ? 'Activo' : 'Inactivo'"
            :activo="usuario.activo"
            :profilePicture="usuario.persona.fotoUrl"
            :initials="usuario.nombre"
          />
        </v-col>
        <v-col cols="12" sm="12" md="8" lg="9">
          <wTabsCard :tabs="tabs">
            <template #general>
              <tgeneral :usuario="usuario" />
            </template>

            <template #personal>
              <tpersonal :persona="usuario.persona" ref="personalTabRef" />
            </template>

            <template #direccion>
              <tdireccion :direccion="usuario.direccion" ref="direccionTabRef" @update:direccion="updateDireccion" />
            </template>

            <template #sedes>
              <tsedes :sedes="usuario.sedes" @update:sedes="updateSedes" />
            </template>
          </wTabsCard>
        </v-col>
      </v-row>
    </template>
  </wFormData>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import wFormData from "@/components/apps/wFormData.vue";
import wProfileCard from "@/components/apps/wProfileCard.vue";
import wTabsCard from "@/components/apps/wTabsCard.vue";
import tgeneral from "@/views/pages/seguridad/usuarios/tabs/general.vue";
import tpersonal from "@/views/pages/seguridad/usuarios/tabs/personal.vue";
import tdireccion from "@/views/pages/seguridad/usuarios/tabs/direccion.vue";
import tsedes from "@/views/pages/seguridad/usuarios/tabs/tsedes.vue";
import { nextTick } from 'vue';
import type { PaginResponse } from "@/utils/PaginResponse";
import { UsuarioService } from "@/services/Seguridad/UsuarioService";
import type { Usuario } from "@/utils/models/Seguridad/Usuario";
import type Persona from "@/utils/models/Generales/persona";
import type { PersonaDireccion } from "@/utils/models/Generales/PersonaDireccion";
import { useNotifications } from '@/composables/useNotifications';

// Estado reactivo
const loading = ref(false);
const { showSuccess, showError } = useNotifications();

const headers = ref([
 { title: "", key: "foto", align: "center", sortable: false, type: "image" },
 { title: "Usuario", key: "usuario", align: "start", sortable: true },
 { title: "Correo", key: "correo", align: "start", sortable: true },
 { title: "Rol", key: "rolDescripcion", align: "start", sortable: true },
 { title: "Estado", key: "estado", align: "start", sortable: true },
 { title: "Acciones", key: "acciones", align: "start", sortable: true },
]);

const tabs = [
 { label: "General", value: "general", icon: "mdi-account", content: "" },
 { label: "Personal", value: "personal", icon: "mdi-account-box", content: "" },
 { label: "Dirección", value: "direccion", icon: "mdi-map-marker", content: "" },
 { label: "Sedes", value: "sedes", icon: "mdi-domain", content: "" },
];

const usuarios = ref<PaginResponse>({
 pageNumber: 1,
 pageSize: 10,
 totalPages: 0,
 totalRecords: 0,
 items: [],
 succeeded: false,
 errors: null,
 message: null,
});

const usuario = ref<Usuario>({
  persona: {
    direcciones: [] as PersonaDireccion[]
  } as Persona
} as Usuario);
const personalTabRef = ref<InstanceType<typeof tpersonal> | null>(null);


function updateSedes(updatedSedes: any[]) {
  usuario.value.sedes = updatedSedes;
}

function updateDireccion(updatedDireccion: any) {
  usuario.value.direccion = updatedDireccion;
  console.log('Direccion actualizada:', updatedDireccion);
}

// Montaje del componente
onMounted(async () => {
 await searchItem("");
});

// Función de búsqueda
async function searchItem(value: string, pageNumber = 1, pageSize = 10) {
 loading.value = true;
 const response = await UsuarioService.getUsuarios(
   value,
   pageNumber,
   pageSize
 );
 usuarios.value = response;
 loading.value = false;
}

// Función de edición
async function editItem(item: Usuario) {
  const response = await UsuarioService.getUsuarioById(item.id);
  if (response) {
    usuario.value = response; // Directly assign the response to the reactive ref

    await nextTick();
    if (personalTabRef.value && typeof personalTabRef.value.fetchAutocompleteData === 'function') {
      personalTabRef.value.fetchAutocompleteData();
    }
  }
  console.log(usuario.value);
}

// Función para crear nuevo usuario
function newItem() {
  usuario.value = {
    id: 0,
    usuario: "",
    correo: "",
    estado: true,
    nombres: "",
    apellidos: "",
    rolId: 0,
    activo: true,
    fechaVencimientoClave: null,
    cambiarClave: false,
    persona: {
      nombres: "",
      apellidos: "",
      tipoDocumentoId: 1,
      noDocumento: "",
      sexoId: 1,
      estadoCivilId: 1,
      fechaNacimiento: null,
      telefono: "",
      nacionalidadId: 1,
      paisId: 1,
      provinciaId: 1,
      municipioId: 1,
      ciudadId: 1,
      tipoSangreId: 1,
      alergias: "",
      fotoUrl: "",
      direcciones: [] as PersonaDireccion[]
    } as Persona,
    direccion: {
      tipoDireccionId: 0,
      direccion: "",
      referencia: null,
      paisId: 1,
      provinciaId: 1,
      municipioId: 1,
      ciudadId: 1,
      telefono: null,
      codigoPostal: null
    },
    sedes: []
  } as Usuario;
}

// Función para guardar nuevo usuario
async function saveUsuario() {
  if (!validateUsuario()) {
    return;
  }

  loading.value = true;
  try {
    const usuarioData = UsuarioService.transformUsuarioToApiFormat(usuario.value);
    console.log('Datos a enviar:', usuarioData);

    await UsuarioService.createUsuarioCompleto(usuarioData);
    showSuccess('Usuario creado exitosamente');
    await searchItem("");
  } catch (error) {
    console.error('Error al crear usuario:', error);
    showError('Error al crear el usuario');
  } finally {
    loading.value = false;
  }
}

// Función para actualizar usuario existente
async function updateUsuario() {
  if (!validateUsuario()) {
    return;
  }

  loading.value = true;
  try {
    const usuarioData = UsuarioService.transformUsuarioToApiFormat(usuario.value);
    console.log('Datos a actualizar:', usuarioData);

    await UsuarioService.updateUsuarioCompleto(usuario.value.id, usuarioData);
    showSuccess('Usuario actualizado exitosamente');
    await searchItem("");
  } catch (error) {
    console.error('Error al actualizar usuario:', error);
    showError('Error al actualizar el usuario');
  } finally {
    loading.value = false;
  }
}

// Función de validación básica
function validateUsuario(): boolean {
  if (!usuario.value.usuario?.trim()) {
    showError('El nombre de usuario es requerido');
    return false;
  }

  if (!usuario.value.correo?.trim()) {
    showError('El correo electrónico es requerido');
    return false;
  }

  if (!usuario.value.nombres?.trim()) {
    showError('Los nombres son requeridos');
    return false;
  }

  if (!usuario.value.apellidos?.trim()) {
    showError('Los apellidos son requeridos');
    return false;
  }

  if (!usuario.value.rolId || usuario.value.rolId === 0) {
    showError('Debe seleccionar un rol');
    return false;
  }

  return true;
}
</script>