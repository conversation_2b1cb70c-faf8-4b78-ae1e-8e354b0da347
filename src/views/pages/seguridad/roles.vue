<script setup lang="ts">

import { onMounted, ref, watch, computed } from "vue";
import WFormData from "../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { roles } from "@/utils/models/Seguridad/roles";
import rolesService from "@/services/Seguridad/RolService";
import lazyFetch from "@/utils/lazyFetch";

let loading = ref(false);

const headers = [
    {title: "Nombre", key: "nombre"},
    {title: "Descripción", key: "descripcion"},
    {title: "Estatus", key: "activo"},
    {title: "Acciones", key: "acciones", sortable: false}
]

const roles = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});

const roleItem = ref<roles>({
    id: 0,
    companyID: 0,
    nombre: "",
    descripcion: "",
    rolPowerUser: false,
    rolHome: null,
    rolEstudiante: false,
    rolProfesor: false,
    rolFechaCreacion: new Date().toISOString().split('T')[0], // Default to current date
    activo: false,
    rolEstudianteEC: false,
    auditor: false
});

onMounted(async () => {
  await searchItem("");
});

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await rolesService.searchItem(value ,pageNumber,pageSize);
    roles.value = response;
    console.log("Roles loaded:", roles.value.items); // Add this line for debugging
    loading.value = false;
}

function editItem(roleEdit: roles) {
    Object.assign(roleItem.value, roleEdit);
};

async function updateRole(){
    try {
        const response = await apiQuery.put(
            `api/security/roles/${roleItem.value.id}`,
            roleItem.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(roleItem.value.nombre);
        loading.value = false;
    } catch (error) {
        console.error("Error fetching roles:", error);
        throw error;
    }
}

function newItem(){
  roleItem.value = {
    id: 0,
    companyID: 0,
    nombre: "",
    descripcion: "",
    rolPowerUser: false,
    rolHome: null,
    rolEstudiante: false,
    rolProfesor: false,
    rolFechaCreacion: new Date().toISOString().split('T')[0],
    activo: false,
    rolEstudianteEC: false,
    auditor: false
    }
}

async function saveRole(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/security/roles`,
    roleItem.value
  );
  searchItem(roleItem.value.nombre);

  loading.value = false;
}

const customActionsForRoles = computed(() => [
  { icon: 'mdi-shield-account', link: '/seguridad/permisos', tooltip: 'Permisos' }
]);
</script>

<template>
    <WFormData
    :loading="loading"
    :title="'Roles'"
    :filters="null"
    :headers="headers"
    :items="roles.items"
    :icon="'mdi-account-group'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="roles.pageNumber"
    :pageSize="roles.pageSize"
    :totalPages="roles.totalPages"
    :totalRecords="roles.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateRole()"
    @newItem="newItem()"
    @save="saveRole()"
    :customActions="customActionsForRoles"
    >
    <template #editItemPanel>
        <v-col cols="12" sm="12">
            <v-text-field
            label="Nombre"
            v-model="roleItem.nombre"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-text-field
            label="Company ID"
            v-model.number="roleItem.companyID"
            outlined
            dense
            type="number"
            ></v-text-field>
        </v-col>

        <v-col cols="12" sm="6">
            <v-text-field
            label="Home"
            v-model="roleItem.rolHome"
            outlined
            dense
            ></v-text-field>
        </v-col>

        <v-col cols="12" sm="4">
            <v-switch
            color="primary"
            label="Estatus"
            v-model="roleItem.activo"
            outlined
            dense
            ></v-switch>
        </v-col>
        
        <v-col cols="12" sm="4">
            <v-switch
            color="primary"
            label="Power User"
            v-model="roleItem.rolPowerUser"
            outlined
            dense
            ></v-switch>
        </v-col>
        
        <v-col cols="12" sm="4">
            <v-switch
            color="primary"
            label="Estudiante"
            v-model="roleItem.rolEstudiante"
            outlined
            dense
            ></v-switch>
        </v-col>
        <v-col cols="12" sm="4">
            <v-switch
            color="primary"
            label="Profesor"
            v-model="roleItem.rolProfesor"
            outlined
            dense
            ></v-switch>
        </v-col>
        
        <v-col cols="12" sm="4">
            <v-switch
            color="primary"
            label="Estudiante EC"
            v-model="roleItem.rolEstudianteEC"
            outlined
            dense
            ></v-switch>
        </v-col>
        <v-col cols="12" sm="4">
            <v-switch
            color="primary"
            label="Auditor"
            v-model="roleItem.auditor"
            outlined
            dense
            ></v-switch>
        </v-col>
    </template>
    </WFormData>
</template>