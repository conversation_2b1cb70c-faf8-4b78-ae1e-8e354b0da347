<script setup lang="ts">

import { onMounted, ref, watch, computed, nextTick } from "vue";
import WFormData from "../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { roles } from "@/utils/models/Seguridad/roles";
import rolesService from "@/services/Seguridad/RolService";
import lazyFetch from "@/utils/lazyFetch";
import { useNotifications } from '@/composables/useNotifications';

let loading = ref(false);

// Sistema de notificaciones
const { showSuccess, showError } = useNotifications();

// Referencia al componente wFormData
const formDataRef = ref<InstanceType<typeof WFormData> | null>(null);

const headers = [
    {title: "Nombre", key: "nombre"},
    {title: "Descripción", key: "descripcion"},
    {title: "Estatus", key: "activo"},
    {title: "Acciones", key: "acciones", sortable: false}
]

const roles = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});

const roleItem = ref<roles>({
    id: 0,
    companyID: 0,
    nombre: "",
    descripcion: "",
    rolPowerUser: false,
    rolHome: null,
    rolEstudiante: false,
    rolProfesor: false,
    rolFechaCreacion: new Date().toISOString().split('T')[0], // Default to current date
    activo: false,
    rolEstudianteEC: false,
    auditor: false
});

onMounted(async () => {
  await searchItem("");
});

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await rolesService.searchItem(value ,pageNumber,pageSize);
    roles.value = response;
    console.log("Roles loaded:", roles.value.items); // Add this line for debugging
    loading.value = false;
}

function editItem(roleEdit: roles) {
    Object.assign(roleItem.value, roleEdit);
};

// Función de validación para roles
function validateRole(): boolean {
  if (!roleItem.value.nombre?.trim()) {
    showError('El campo Nombre es requerido');
    return false;
  }

  if (!roleItem.value.descripcion?.trim()) {
    showError('El campo Descripción es requerido');
    return false;
  }

  if (!roleItem.value.companyID || roleItem.value.companyID === 0) {
    showError('El campo Company ID es requerido');
    return false;
  }

  return true;
}

// Función centralizada para ocultar modal y volver al listado
function hideModalAndReturnToList() {
  console.log('=== hideModalAndReturnToList iniciando ===');

  if (formDataRef.value) {
    console.log('formDataRef.value disponible');

    // 1. Cambiar estado del panel a modo lista INMEDIATAMENTE
    if (typeof formDataRef.value.setEditMode === 'function') {
      console.log('Forzando setEditMode(false)');
      formDataRef.value.setEditMode(false);
    }

    // 2. Cerrar cualquier modal del dataTable INMEDIATAMENTE
    const dataTableComponent = formDataRef.value.$refs?.dataTable;
    if (dataTableComponent && typeof dataTableComponent.close === 'function') {
      console.log('Cerrando modal del dataTable');
      dataTableComponent.close();
    }

    // 3. Emitir eventos para completar la transición
    if (typeof formDataRef.value.emitSaveDone === 'function') {
      console.log('Emitiendo saveDone');
      formDataRef.value.emitSaveDone();
    }
    if (typeof formDataRef.value.emitUpdateDone === 'function') {
      console.log('Emitiendo updateDone');
      formDataRef.value.emitUpdateDone();
    }

    console.log('Estado final del modo edición:', formDataRef.value.getEditMode?.());
  } else {
    console.log('formDataRef.value no disponible');
  }

  console.log('=== hideModalAndReturnToList completado ===');
}

async function updateRole(){
    if (!validateRole()) {
        return false; // Retorna false para indicar que no se debe cerrar el modal
    }

    loading.value = true;
    try {
        const response = await apiQuery.put(
            `api/security/roles/${roleItem.value.id}`,
            roleItem.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        // Inmediatamente después del éxito, ocultar modal y cambiar a modo lista
        showSuccess('Rol actualizado exitosamente');

        // Ocultar modal INMEDIATAMENTE después del mensaje de éxito
        hideModalAndReturnToList();

        // Actualizar la lista en segundo plano
        await searchItem(roleItem.value.nombre);

        return true; // Retorna true para indicar éxito
    } catch (error) {
        console.error("Error al actualizar rol:", error);
        showError('Error al actualizar el rol');
        return false; // Retorna false para mantener el modal abierto
    } finally {
        loading.value = false;
    }
}

function newItem(){
  roleItem.value = {
    id: 0,
    companyID: 0,
    nombre: "",
    descripcion: "",
    rolPowerUser: false,
    rolHome: null,
    rolEstudiante: false,
    rolProfesor: false,
    rolFechaCreacion: new Date().toISOString().split('T')[0],
    activo: false,
    rolEstudianteEC: false,
    auditor: false
    }
}

async function saveRole(){
  if (!validateRole()) {
    return false; // Retorna false para indicar que no se debe cerrar el modal
  }

  loading.value = true;
  try {
    const response = await apiQuery.post(
      `api/security/roles`,
      roleItem.value
    );

    // Inmediatamente después del éxito, ocultar modal y cambiar a modo lista
    showSuccess('Rol creado exitosamente');

    // Ocultar modal INMEDIATAMENTE después del mensaje de éxito
    hideModalAndReturnToList();

    // Actualizar la lista en segundo plano
    await searchItem(roleItem.value.nombre);

    return true; // Retorna true para indicar éxito
  } catch (error) {
    console.error("Error al crear rol:", error);
    showError('Error al crear el rol');
    return false; // Retorna false para mantener el modal abierto
  } finally {
    loading.value = false;
  }
}

const customActionsForRoles = computed(() => [
  { icon: 'mdi-shield-account', link: '/seguridad/permisos', tooltip: 'Permisos' }
]);

// Funciones wrapper simplificadas - el cierre del modal se maneja en las funciones principales
async function handleSave() {
  await saveRole();
  // El modal se oculta automáticamente después del éxito en saveRole()
}

async function handleUpdate() {
  await updateRole();
  // El modal se oculta automáticamente después del éxito en updateRole()
}
</script>

<template>
    <WFormData
    ref="formDataRef"
    :loading="loading"
    :title="'Roles'"
    :filters="null"
    :headers="headers"
    :items="roles.items"
    :icon="'mdi-account-group'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="roles.pageNumber"
    :pageSize="roles.pageSize"
    :totalPages="roles.totalPages"
    :totalRecords="roles.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="handleUpdate"
    @newItem="newItem()"
    @save="handleSave"
    :customActions="customActionsForRoles"
    >
    <template #editItemPanel>
        <v-col cols="12" sm="12">
            <v-text-field
            label="Nombre *"
            v-model="roleItem.nombre"
            outlined
            dense
            :rules="[v => !!v || 'El nombre es requerido']"
            required
            ></v-text-field>
        </v-col>

        <v-col cols="12" sm="12">
            <v-text-field
            label="Descripción *"
            v-model="roleItem.descripcion"
            outlined
            dense
            :rules="[v => !!v || 'La descripción es requerida']"
            required
            ></v-text-field>
        </v-col>

        <v-col cols="12" sm="6">
            <v-text-field
            label="Company ID *"
            v-model.number="roleItem.companyID"
            outlined
            dense
            type="number"
            :rules="[v => !!v || 'El Company ID es requerido']"
            required
            ></v-text-field>
        </v-col>

        <v-col cols="12" sm="6">
            <v-text-field
            label="Home"
            v-model="roleItem.rolHome"
            outlined
            dense
            ></v-text-field>
        </v-col>

        <v-col cols="12" sm="4">
            <v-switch
            color="primary"
            label="Estatus"
            v-model="roleItem.activo"
            outlined
            dense
            ></v-switch>
        </v-col>
        
        <v-col cols="12" sm="4">
            <v-switch
            color="primary"
            label="Power User"
            v-model="roleItem.rolPowerUser"
            outlined
            dense
            ></v-switch>
        </v-col>
        
        <v-col cols="12" sm="4">
            <v-switch
            color="primary"
            label="Estudiante"
            v-model="roleItem.rolEstudiante"
            outlined
            dense
            ></v-switch>
        </v-col>
        <v-col cols="12" sm="4">
            <v-switch
            color="primary"
            label="Profesor"
            v-model="roleItem.rolProfesor"
            outlined
            dense
            ></v-switch>
        </v-col>
        
        <v-col cols="12" sm="4">
            <v-switch
            color="primary"
            label="Estudiante EC"
            v-model="roleItem.rolEstudianteEC"
            outlined
            dense
            ></v-switch>
        </v-col>
        <v-col cols="12" sm="4">
            <v-switch
            color="primary"
            label="Auditor"
            v-model="roleItem.auditor"
            outlined
            dense
            ></v-switch>
        </v-col>
    </template>
    </WFormData>
</template>