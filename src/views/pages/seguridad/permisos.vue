<template>
  <v-container>
    <v-row>
      <v-col cols="12">
        <v-card class="rounded-card">
          <v-toolbar color="primary" class="rounded-toolbar" density="compact" flat>
            <v-btn icon="mdi-account-key"></v-btn>
            <v-toolbar-title>Permisos para Rol: {{ rolName }}</v-toolbar-title>
          </v-toolbar>
          <v-card-text>
            <v-list class="rounded-list" style="background-color: transparent;">
              <PermissionOption
                v-for="option in options"
                :key="option.id"
                :option="option"
              />
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import PermissionOption from '@/components/seguridad/PermissionOption.vue';
import { apiQuery } from '@/utils/helpers/apiQuery';
import type { OpcionAplicacion } from '@/utils/OpcionAplicacion';
import RolService from '@/services/Seguridad/RolService';

const route = useRoute();
const rolId = ref('');
const rolName = ref('');
const options = ref<OpcionAplicacion[]>([]);

onMounted(async () => {
  rolId.value = route.params.rolId as string;
  try {
    const role = await RolService.getRoleById(rolId.value);
    if (role && role.nombre) {
      rolName.value = role.nombre;
    } else {
      console.log('Role not found or name is empty for ID:', rolId.value);
    }

    const response = await apiQuery.get('api/security/opcionaplicacion');
    if (response) {
      options.value = response.map((option: OpcionAplicacion) => ({ ...option, nivel: 1 }));
      assignNivel(options.value);
      console.log('Fetched options:', options.value);
    } else {
      console.log('API response is empty:', response);
    }
  } catch (error) {
    console.error('Error fetching data:', error);
  }
});

const assignNivel = (opts: OpcionAplicacion[], nivel = 1) => {
  opts.forEach(option => {
    option.nivel = nivel;
    if (option.subOpciones && option.subOpciones.length > 0) {
      assignNivel(option.subOpciones, nivel + 1);
    }
  });
};
</script>

<style scoped>
.rounded-card {
  border-radius: 15px; /* Adjust as needed for desired roundness */
}

.rounded-toolbar {
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
}

.rounded-list {
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;
  overflow: hidden; /* Ensures content respects border-radius */
}
</style>