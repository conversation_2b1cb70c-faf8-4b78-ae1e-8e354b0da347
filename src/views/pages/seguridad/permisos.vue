<template>
  <v-container>
    <v-row>
      <v-col cols="12">
        <v-card class="rounded-card">
          <v-toolbar color="primary" class="rounded-toolbar" density="compact" flat>
            <v-btn icon="mdi-account-key"></v-btn>
            <v-toolbar-title>Permisos para Rol: {{ rolName }}</v-toolbar-title>
          </v-toolbar>
          <v-card-text>
            <v-list class="rounded-list" style="background-color: transparent;">
              <PermissionOption
                v-for="option in options"
                :key="option.id"
                :option="option"
                @update:permission="handlePermissionUpdate"
              />
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted, provide, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import PermissionOption from '@/components/seguridad/PermissionOption.vue';
import { apiQuery } from '@/utils/helpers/apiQuery';
import type { OpcionAplicacion } from '@/utils/OpcionAplicacion';
import RolService from '@/services/Seguridad/RolService';
import OpcionAplicacionService from '@/services/Seguridad/OpcionAplicacionService';
import { useBatchOperations } from '@/composables/useBatchOperations';
import { useNotifications } from '@/composables/useNotifications';

const route = useRoute();
const rolId = ref('');
const rolName = ref('');
const options = ref<OpcionAplicacion[]>([]);

// Batch operations
const { addOperation, isProcessing, getPendingCount, forceBatch, processBatch, setCustomProcessor } = useBatchOperations();
const { showSuccess, showError, showInfo } = useNotifications();

// Procesador personalizado para las operaciones batch
const customBatchProcessor = async (operations: any[]) => {
  const opciones = operations.map(op => op.option);

  showInfo(`Guardando ${opciones.length} cambios...`);
  const result = await OpcionAplicacionService.saveBatchOpcionesAplicacion(opciones);

  if (result.successful > 0) {
    showSuccess(`${result.successful} permisos guardados exitosamente`);
  }

  if (result.failed > 0) {
    showError(`${result.failed} permisos fallaron al guardar`);
  }

  return result;
};

onMounted(async () => {
  // Configurar el procesador personalizado para batch operations
  setCustomProcessor(customBatchProcessor);

  rolId.value = route.params.rolId as string;
  try {
    const role = await RolService.getRoleById(rolId.value);
    if (role && role.nombre) {
      rolName.value = role.nombre;
    } else {
      console.log('Role not found or name is empty for ID:', rolId.value);
    }

    const response = await OpcionAplicacionService.getOpcionesAplicacion();
    if (response) {
      options.value = response.map((option: OpcionAplicacion) => ({ ...option, nivel: 1 }));
      assignNivel(options.value);
      console.log('Fetched options:', options.value);
    } else {
      console.log('API response is empty:', response);
    }
  } catch (error) {
    console.error('Error fetching data:', error);
  }
});

const assignNivel = (opts: OpcionAplicacion[], nivel = 1) => {
  opts.forEach(option => {
    option.nivel = nivel;
    if (option.subOpciones && option.subOpciones.length > 0) {
      assignNivel(option.subOpciones, nivel + 1);
    }
  });
};

const handlePermissionUpdate = () => {
  // Esta función se ejecuta cuando se actualiza un permiso desde el componente hijo
  console.log('Permisos actualizados');
};

// Función para agregar operaciones al batch
const addToBatch = (option: OpcionAplicacion) => {
  addOperation(option);
};

// Proveer la función addToBatch a los componentes hijos
provide('addToBatch', addToBatch);

// Cleanup al desmontar el componente
onUnmounted(() => {
  // Forzar el procesamiento de operaciones pendientes antes de salir
  if (getPendingCount() > 0) {
    forceBatch(customBatchProcessor);
  }
});
</script>

<style scoped>
.rounded-card {
  border-radius: 15px; /* Adjust as needed for desired roundness */
}

.rounded-toolbar {
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
}

.rounded-list {
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;
  overflow: hidden; /* Ensures content respects border-radius */
}
</style>