<script setup lang="ts">
import { ref } from 'vue'
//import BaseBreadcrumb from '@/components/shared/BaseBreadcrumb.vue'
import { UserCircleIcon, LockIcon } from 'vue-tabler-icons'
import { useDecodedUser } from '@/composables/useDecodeUser'


const tab = ref('Account')
const { user } = useDecodedUser()

// Imagen y datos generales
const fotoPerfil = ref('@/assets/images/profile/user-1.jpg')



// Cambio de contraseña
const currenypwd = ref('')
const newpwd = ref('')
const confirmpwd = ref('')

// Datos personales

const storephone = ref('****** 220 1111')
const storeaddress = ref('11301 Ciudad Modelo, Av Jacovo Majluta, Santo Domingo, RD')

// Breadcrumb
const page = ref({ title: 'Ajustes de cuenta' })
const breadcrumbs = ref([
  { text: 'Dashboard', disabled: false, href: '#' },
  { text: 'Ajustes de cuenta', disabled: true, href: '#' }
])
</script>

<template>
  <BaseBreadcrumb :title="page.title" :breadcrumbs="breadcrumbs" />

  <v-row class="justify-center">
    <v-col cols="12">
      <v-card elevation="10" rounded="md">
        <v-tabs v-model="tab" bg-color="transparent" height="70" color="primary">
          <v-tab value="Account"><UserCircleIcon class="mr-2" size="20" />Cuenta</v-tab>
          <v-tab value="Security"><LockIcon class="mr-2" size="20" />Seguridad</v-tab>
        </v-tabs>

        <v-divider />

        <v-card-text class="pa-sm-6 pa-3 pb-sm-6 pb-6">
          <v-window v-model="tab">
            <!-- TAB ACCOUNT -->
            <v-window-item value="Account">
              <v-card elevation="10">
                <v-row class="ma-sm-n2 ma-n1">
                  <!-- CAMBIO DE PERFIL -->
                  <v-col cols="12" sm="6">
                    <v-card variant="outlined">
                      <v-card-item>
                        <h5 class="text-h5">Editar Perfil</h5>
                        <div class="text-subtitle-1 textSecondary mt-2">
                          Cambia tu foto de perfil aquí
                        </div>
                        <div class="text-center mt-6 mb-6">
                          <v-avatar size="120">
                            <img :src="user?.fotoPerfil || 'https://via.placeholder.com/120'" height="120" alt="Foto de perfil" />
                          </v-avatar>
                        </div>
                        <div class="d-flex justify-center">
                          <v-btn color="primary" class="mx-2" flat>Subir</v-btn>
                          <v-btn color="error" class="mx-2" variant="outlined" flat>Resetear</v-btn>
                        </div>
                        <div class="text-subtitle-1 textSecondary text-center my-sm-8 my-6">
                          Formato Permitido JPG or PNG. Max size de 800kb
                        </div>
                      </v-card-item>
                    </v-card>
                  </v-col>

                  <!-- CAMBIO DE CONTRASEÑA -->
                  <v-col cols="12" sm="6">
                    <v-card variant="outlined">
                      <v-card-item>
                        <h5 class="text-h5">Cambiar Clave</h5>
                        <div class="text-subtitle-1 textSecondary mt-2">
                          Confirmar Clave para cambiarla
                        </div>
                        <div class="mt-5">
                          <v-label class="mb-2 font-weight-medium">Clave Actual</v-label>
                          <v-text-field color="primary" variant="outlined" type="password" v-model="currenypwd" />
                          <v-label class="mb-2 font-weight-medium">Nueva Clave</v-label>
                          <v-text-field color="primary" variant="outlined" type="password" v-model="newpwd" />
                          <v-label class="mb-2 font-weight-medium">Confirmar Clave</v-label>
                          <v-text-field
                            color="primary"
                            variant="outlined"
                            type="password"
                            v-model="confirmpwd"
                            hide-details
                          />
                        </div>
                      </v-card-item>
                    </v-card>
                  </v-col>

                  <!-- DATOS PERSONALES -->
                  <v-col cols="12">
                    <v-card variant="outlined">
                      <v-card-item>
                        <h5 class="text-h5">Datos Personales</h5>
                        <div class="text-subtitle-1 textSecondary mt-2">
                          Para cambiar tus datos personales , edita y guarda los cambios.
                        </div>
                        <div class="mt-5">
                          <v-row>
                            <v-col cols="12" md="6">
                            <v-label class="mb-2 font-weight-medium">Usuario</v-label>
                            <v-text-field
                              color="primary"
                              variant="outlined"
                              type="text"
                              v-model="user.usuaNombre"
                              hide-details
                              readonly
                            />
                          </v-col>

                          <v-col cols="12" md="6">
                            <v-label class="mb-2 font-weight-medium">Rol</v-label>
                            <v-text-field
                              color="primary"
                              variant="outlined"
                              type="text"
                              v-model="user.rol"
                              hide-details
                              readonly
                            />
                          </v-col>
                            
                            <v-col cols="12" md="6">
                            <v-label class="mb-2 font-weight-medium">Correo</v-label>
                            <v-text-field
                              color="primary"
                              variant="outlined"
                              type="text"
                              v-model="user.email"
                              hide-details
                            />
                          </v-col>
                            <v-col cols="12" md="6">
                              <v-label class="mb-2 font-weight-medium">Telefono</v-label>
                              <v-text-field color="primary" variant="outlined" type="text" v-model="storephone" hide-details />
                            </v-col>
                            <v-col cols="12">
                              <v-label class="mb-2 font-weight-medium">Direcciòn</v-label>
                              <v-text-field color="primary" variant="outlined" type="text" v-model="storeaddress" hide-details />
                            </v-col>
                          </v-row>
                        </div>
                      </v-card-item>
                    </v-card>
                  </v-col>
                </v-row>

                <!-- BOTONES SIN FUNCIONALIDAD NO TIENEN LOGICA AUN -->
                <div class="d-flex justify-end mt-5">
                  <v-btn color="primary" class="mr-4" flat>Guardar</v-btn>
                  <v-btn class="bg-lighterror text-error" flat>Cancelar</v-btn>
                </div>
              </v-card>
            </v-window-item>

            <!-- TAB SEGURIDAD (puedes expandir aquí si lo deseas) -->
            <v-window-item value="Security">
              <v-card class="pa-6" elevation="2">
                <h5 class="text-h6 mb-4">Seguridad</h5>
                <p class="text-subtitle-2">Proximamente segurídad .</p>
              </v-card>
            </v-window-item>
          </v-window>
        </v-card-text>
      </v-card>
    </v-col>
  </v-row>
</template>
