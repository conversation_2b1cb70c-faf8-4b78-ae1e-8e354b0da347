<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { honorAcademico } from "@/utils/models/Academico/honorAcademico";
import HonorAcademicoService from "@/services/Academicos/HonorAcademicoService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Rango Inicial", key: "rangoInicial" },
  { title: "Rango Final", key: "rangoFinal" },
  { title: "Orden", key: "orden" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const honoresAcademicos = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<honorAcademico>({
  id: 0,
  descripcion: "",
  rangoInicial: 0,
  rangoFinal: 0,
  orden: 0,
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});

//Metodos
function editItem(honorAcademico: honorAcademico) {
  Object.assign(item.value, honorAcademico);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await HonorAcademicoService.getHonoresAcademicos(
    value,
    pageNumber,
    pageSize
  );
  honoresAcademicos.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateHonorAcademico() {
  loading.value = true;
  await apiQuery.put(
    `api/academicos/honores-academicos/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addHonorAcademico() {
  loading.value = true;
  await apiQuery.post(`api/academicos/honores-academicos`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    rangoInicial: 0,
    rangoFinal: 0,
    orden: 0,
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Honores Académicos'"
    :filters="null"
    :headers="headers"
    :items="honoresAcademicos.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="honoresAcademicos.pageNumber"
    :pageSize="honoresAcademicos.pageSize"
    :totalPages="honoresAcademicos.totalPages"
    :totalRecords="honoresAcademicos.totalRecords"
    @editItem="editItem"
    @update="updateHonorAcademico()"
    @save="addHonorAcademico()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Orden"
          v-model="item.orden"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Rango Inicial"
          v-model="item.rangoInicial"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Rango Final"
          v-model="item.rangoFinal"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estatus"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
