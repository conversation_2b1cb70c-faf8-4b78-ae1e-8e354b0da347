<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import type { documentoAcademico } from "@/utils/models/Academico/documentoacademico";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import { DocumentosService } from "@/services/Academicos/DocumentosService";
import { ProcesosService } from "@/services/Academicos/ProcesosService";
import lazyFetch from "@/utils/lazyFetch";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Detiene Proceso", key: "detieneProceso" },
  { title: "Proceso Académico", key: "procesoAcademicoDescripcion" },
  { title: "Estado", key: "estado" },
  { title: "Acciones", key: "acciones" },
];

const documentos = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const procesosacademicos = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<documentoAcademico>({
  id: 0,
  descripcion: "",
  nombreDigital: "",
  detieneProceso: false,
  procesoAcademico: null,
  procesoAcademicoDescripcion: null,
  diasProrroga: 0,
  estado: true,
  esTitulo: false,
});

let loading = ref(false);
let loadingProcesosAcademicos = ref(false);
const searchText = ref("");

// Montar
onMounted(async () => {
  await Promise.all([getProcesosAcademicos(), searchItem("")]);
});

watch(searchText, (newValue) => {
  getProcesosAcademicos(newValue);
});

//Metodos
function editItem(document: documentoAcademico) {
  Object.assign(item.value, document);

  searchText.value = document.procesoAcademicoDescripcion || "";
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await DocumentosService.getDocumentos(
    value,
    pageNumber,
    pageSize
  );

  documentos.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function getProcesosAcademicos(value: string = "") {
  loadingProcesosAcademicos.value = true;

  const response = await lazyFetch(() =>
    ProcesosService.getProcesos(value)
  );

  procesosacademicos.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loadingProcesosAcademicos.value = false;
}

async function updateDocumento() {
  loading.value = true;
  const response = await apiQuery.put(
    `api/academicos/documentosacademicos/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function saveDocumento() {
  loading.value = true;
  const response = await apiQuery.post(
    `api/academicos/documentosacademicos`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  searchText.value = "";

  item.value = {
    id: 0,
    descripcion: "",
    nombreDigital: "",
    detieneProceso: false,
    procesoAcademico: null,
    procesoAcademicoDescripcion: null,
    diasProrroga: 0,
    estado: true,
    esTitulo: false,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Documento Académico'"
    :filters="null"
    :headers="headers"
    :items="documentos.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="documentos.pageNumber"
    :pageSize="documentos.pageSize"
    :totalPages="documentos.totalPages"
    :totalRecords="documentos.totalRecords"
    :add="true"
    @editItem="editItem"
    @update="updateDocumento()"
    @save="saveDocumento()"
    @searchItem="searchItem"
    @newItem="newItem"
   
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Archivo Digital"
          v-model="item.nombreDigital"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-checkbox
          color="primary"
          label="Detiene Proceso"
          v-model="item.detieneProceso"
          outlined
          dense
        ></v-checkbox
      ></v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.procesoAcademico"
          v-model:search="searchText"
          :items="procesosacademicos.items"
          item-title="descripcion"
          item-value="id"
          label="Proceso Académico"
          density="compact"
          variant="outlined"
          :loading="loadingProcesosAcademicos"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.procesoAcademico = value)"
          @update:search="(value) => (searchText = value)"
        ></v-autocomplete>
      </v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          label="Días de Prórroga"
          v-model="item.diasProrroga"
          type="number"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-checkbox
          label="¿Es Título?"
          v-model="item.esTitulo"
          outlined
          dense
        ></v-checkbox
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
