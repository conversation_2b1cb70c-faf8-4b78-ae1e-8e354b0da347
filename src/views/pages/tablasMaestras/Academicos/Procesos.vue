<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import {  ProcesosService } from "@/services/Academicos/ProcesosService";
import TiposProcesosService from "@/services/Academicos/TipoProcesoService";
import type { proceso } from "@/utils/models/Academico/proceso";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Tipo Proceso", key: "tipoProcesoDescripcion" },
  { title: "Orden", key: "orden" },
  { title: "Estado", key: "estado" },
  { title: "Acciones", key: "acciones" },
];

const procesos = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const tiposProcesos = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<proceso>({
  id: 0,
  descripcion: "",
  estado: true,
  tipoProceso: 0,
  orden: 0,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([getProcesos(), searchItem("")]);
});
//Metodos
function editItem(proceso: proceso) {
  Object.assign(item.value, proceso);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await ProcesosService.getProcesos(
    value,
    pageNumber,
    pageSize
  );

  procesos.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function getProcesos() {
  loading.value = true;
  const response = await TiposProcesosService.getTiposProcesos("", 1, 100000);

  tiposProcesos.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}

async function updateProceso() {
  loading.value = true;
  const response = await apiQuery.put(
    `api/academicos/proceso/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addProceso() {
  loading.value = true;
  const response = await apiQuery.post(
    `api/academicos/proceso`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estado: true,
    tipoProceso: 0,
    orden: 0,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Procesos'"
    :filters="null"
    :headers="headers"
    :items="procesos.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="procesos.pageNumber"
    :pageSize="procesos.pageSize"
    :totalPages="procesos.totalPages"
    :totalRecords="procesos.totalRecords"
    @editItem="editItem"
    @update="updateProceso()"
    @save="addProceso()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-autocomplete
          :items="tiposProcesos.items"
          item-value="id"
          item-title="descripcion"
          color="primary"
          label="Tipo Proceso"
          v-model="item.tipoProceso"
          outlined
          dense
        ></v-autocomplete
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Orden"
          v-model="item.orden"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
