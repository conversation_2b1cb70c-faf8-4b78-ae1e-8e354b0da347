<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { tipoEstudianteMensaje } from "@/utils/models/Academico/tipoEstudianteMensaje";
import { TipoEstudianteMensajeService } from "@/services/Academicos/TipoEstudianteMensajeService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const tiposEstudiantesMensajes = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<tipoEstudianteMensaje>({
  id: 0,
  descripcion: "",
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(tiposEstudiantesMensajes: tipoEstudianteMensaje) {
  Object.assign(item.value, tiposEstudiantesMensajes);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response =
    await TipoEstudianteMensajeService.getTiposEstudiantesMensajes(
      value,
      pageNumber,
      pageSize
    );
  tiposEstudiantesMensajes.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateTipoEstudianteMensaje() {
  loading.value = true;
  await apiQuery.put(
    `api/academicos/tipo-estudiante-mensaje/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addTipoEstudianteMensaje() {
  loading.value = true;
  await apiQuery.post(`api/academicos/tipo-estudiante-mensaje`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estatus: false,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Tipos Estudiantes Mensajes'"
    :filters="null"
    :headers="headers"
    :items="tiposEstudiantesMensajes.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tiposEstudiantesMensajes.pageNumber"
    :pageSize="tiposEstudiantesMensajes.pageSize"
    :totalPages="tiposEstudiantesMensajes.totalPages"
    :totalRecords="tiposEstudiantesMensajes.totalRecords"
    @editItem="editItem"
    @update="updateTipoEstudianteMensaje()"
    @save="addTipoEstudianteMensaje()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estatus"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
