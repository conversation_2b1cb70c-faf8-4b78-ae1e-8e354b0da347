<script setup lang="ts">
import { computed, onMounted, ref, watch, type Ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import { CarreraService } from "@/services/Academicos/CarreraService";
import type { PlanEstudio } from "@/utils/models/Academico/planEstudio";
import PlanEstudioService from "@/services/Academicos/PlanesEstudioService";
import wDataTable from "../../../../components/apps/wDataTable.vue";
import type { planEstudioDetalle } from "@/utils/models/Academico/planEstudioDetalle";
import AsignaturasService from "@/services/Academicos/AsignaturasService";
import type { planEstudioCorrequisito } from "@/utils/models/Academico/planEstudioCorrequisito";
import type { planEstudioPrerrequisito } from "@/utils/models/Academico/planEstudioPrerrequisito";
import lazyFetch from "@/utils/lazyFetch";
import customFilter from "@/utils/helpers/customFilter";
import type { planEstudioReforzamiento } from "@/utils/models/Academico/planEstudioReforzamiento";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Carrera", key: "carreraDescripcion" },
  { title: "Versión", key: "version" },
  { title: "Vigente", key: "vigente" },
  { title: "Estado", key: "estatus" },
  { title: "Acciones", key: "acciones" },
];

const headersAsignaturas = [
  { title: "Código", key: "codigo" },
  { title: "Descripción", key: "asignaturaDescripcion" },
  { title: "Créditos", key: "creditos" },
  { title: "Cuatrimestre", key: "cuatrimestre" },
  { title: "HT", key: "ht" },
  { title: "HP", key: "hp" },
  { title: "HI", key: "hi" },
  { title: "HNP ", key: "hnp" },
  { title: "MESCYT", key: "mescyt" },
  { title: "Orden", key: "orden" },
  { title: "Acciones", key: "acciones" },
];

const headersCorrequisitos = [
  { title: "Correq.", key: "codigoCorrequisito" },
  { title: "Descripción", key: "descripcionCorrequisito" },
  { title: "Acciones", key: "acciones" },
];

const headersPrerrequisitos = [
  { title: "Prerreq.", key: "prerrequisitoCodigo" },
  { title: "Descripción", key: "prerrequisitoDescripcion" },
  { title: "Acciones", key: "acciones" },
];

const headersReforzamiento = [
  { title: "Código", key: "asignaturaCodigo" },
  { title: "Descripción", key: "asignaturaDescripcion" },
  { title: "Acciones", key: "acciones" },
];

const planesEstudio = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const planesEstudioComplementario = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const carreras = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const asignaturas = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<PlanEstudio>({
  id: 0,
  descripcion: "",
  version: 0,
  fechaInicio: new Date(),
  fechaFin: null,
  carrera: 0,
  carreraDescripcion: "",
  carreraAlias: "",
  planComplementario: 0,
  creditoMaximo: 0,
  resolucionConsejo: "",
  decretoNo: "",
  resolucionNo: "",
  tituloMasc: "",
  tituloFem: "",
  vigente: false,
  estatus: false,
  planEstudioDet: [],
  planEstudioCorrequisitos: [],
  planEstudioPrerrequisitos: [],
  planEstudioReforzamientos: [],
});

let itemDetalle = ref<planEstudioDetalle>({
  id: 0,
  planEstudio: 0,
  asignatura: 0,
  asignaturaDescripcion: "",
  asignaturaCodigoDescripcion: "",
  cuatrimestre: 0,
  creditos: 0,
  ht: 0,
  hp: 0,
  hi: 0,
  hnp: 0,
  esCicloBasico: false,
  creditosAprobados: null,
  cuatrimestresAprobados: null,
  orden: 0,
  codigo: "",
  mescyt: false,
});

let itemCorrequisito = ref<planEstudioCorrequisito>({
  id: 0,
  planEstudio: 0,
  planEstudioDescripcion: "",
  asignatura: 0,
  asignaturaDescripcion: "",
  asignaturaCodigoDescripcion: "",
  correquisito: 0,
  asignaturaCodigo: null,
  codigoCorrequisito: null,
  descripcionCorrequisito: null,
});

let itemPrerrequisito = ref<planEstudioPrerrequisito>({
  id: 0,
  planEstudio: 0,
  planEstudioDescripcion: "",
  asignatura: 0,
  asignaturaDescripcion: "",
  asignaturaCodigoDescripcion: "",
  prerrequisito: 0,
  asignaturaCodigo: null,
  prerrequisitoCodigo: null,
  prerrequisitoDescripcion: null,
});

let itemReforzamiento = ref<planEstudioReforzamiento>({
  id: 0,
  planEstudio: 0,
  planEstudioDescripcion: "",
  asignatura: 0,
  asignaturaDescripcion: "",
  asignaturaCodigo: null,
});

let loading = ref(false);
const showDatePicker = ref(false);
const showDatePickerFin = ref(false);
const nuevoItem = ref(false);
const nuevoItemReforzamiento = ref(false);
const existeVersion = ref(false);
const validateCreditoMaximo = ref(false);
const asignaturaAdded = ref(false);
const isPrerrequisito = ref(false);
const isCorrequisito = ref(false);
const itemsPerPage = ref(10);
const itemsPerPageCor = ref(10);
const itemsPerPagePre = ref(10);
const itemsPerPageRef = ref(10);
const asignaturaCorrequisito = ref(null);
const asignaturaPrerrequisito = ref(null);
const asignaturaOut = ref<any>(null);
const planEstudioDet = ref(item.value.planEstudioDet);
const planEstudioCor = ref(item.value.planEstudioCorrequisitos);
const planEstudioPre = ref(item.value.planEstudioPrerrequisitos);
const planEstudioRef = ref(item.value.planEstudioReforzamientos);

var mensajeAsignaturaAddedDialog = ref("");
var valueBusqueda = ref("");

var asignaturasCorrequisitos = ref<any[] | null>(null);
var asignaturasPrerrequisitos = ref<any[] | null>(null);

let loadingAsignaturas = ref(false);
const searchTextAsignaturas = ref("");

let loadingCarreras = ref(false);
const searchTextCarreras = ref("");

let loadingPlanComplementario = ref(false);
const searchTextPlanComplementario = ref("");

let loadingReforzamiento = ref(false);
const searchTextReforzamiento = ref("");

const lastValue = ref("");

// Montar
onMounted(async () => {
  await Promise.all([
    searchItem(""),
    searchCarreras(""),
    searchAsignaturas(""),
  ]);
});
const filteredAsignaturas = computed(() => {
  if (!valueBusqueda.value) return item.value.planEstudioDet;

  return item.value.planEstudioDet.filter((z) =>
    z.asignaturaCodigoDescripcion
      .toLowerCase()
      .includes(valueBusqueda.value.toLowerCase())
  );
});

function usePagination(list: any[], itemsPerPageDefault = 10) {
  const dataList = ref(list);

  const currentPage = ref(1);
  const itemsPerPage = ref(itemsPerPageDefault);

  const totalPages = computed(() => {
    return Math.ceil(dataList.value.length / itemsPerPage.value) || 1;
  });

  const paginatedData = computed(() => {
    const start = (currentPage.value - 1) * itemsPerPage.value;
    const end = start + itemsPerPage.value;
    return dataList.value.slice(start, end);
  });

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page;
    }
  };

  const nextPage = () => {
    if (currentPage.value < totalPages.value) {
      currentPage.value += 1;
    }
  };

  const prevPage = () => {
    if (currentPage.value > 1) {
      currentPage.value -= 1;
    }
  };

  const setItemsPerPage = (newItemsPerPage: number) => {
    itemsPerPage.value = newItemsPerPage;
    currentPage.value = 1; // Reset to first page
  };

  watch(
    () => dataList.value.length,
    (newLength) => {
      const newTotalPages = Math.ceil(newLength / itemsPerPage.value) || 1;
      if (currentPage.value > newTotalPages) {
        currentPage.value = newTotalPages;
      }
    }
  );

  const updateList = (newList: any[]) => {
    dataList.value = newList;
  };

  return {
    currentPage,
    itemsPerPage,
    totalPages,
    paginatedData,
    goToPage,
    nextPage,
    prevPage,
    setItemsPerPage,
    updateList,
  };
}

const paginationAsignaturasDet = usePagination(planEstudioDet.value);

const paginationDataPrerrequisitos = usePagination(
  planEstudioPre.value,
  itemsPerPagePre.value
);

const paginationDataCorrequisitos = usePagination(
  planEstudioCor.value,
  itemsPerPageCor.value
);

const paginationDataReforzamiento = usePagination(
  planEstudioRef.value,
  itemsPerPageRef.value
);
watch(filteredAsignaturas, (newList) => {
  paginationAsignaturasDet.updateList(newList);
});

watch(searchTextCarreras, (newValue) => {
  searchCarreras(newValue);
});

watch(searchTextAsignaturas, (newValue) => {
  searchAsignaturas(newValue);
});

watch(searchTextPlanComplementario, (newValue) => {
  searchPlanComplementario(newValue);
});

watch(searchTextReforzamiento, (newValue) => {
  searchAsignaturas(newValue);
});

//Metodos
function editItem(planEstudio: PlanEstudio) {
  Object.assign(item.value, {
    ...planEstudio,
    fechaFin: planEstudio.fechaFin ? new Date(planEstudio.fechaFin) : null,
    fechaInicio: planEstudio.fechaInicio
      ? new Date(planEstudio.fechaInicio)
      : null, // Convierte la fecha del backend en un objeto Date o null
  });

  searchTextCarreras.value =
    `${planEstudio.carreraAlias} - ${planEstudio.carreraDescripcion}` || "";
  item.value.planEstudioDet = planEstudio.planEstudioDet;
  planEstudioDet.value = planEstudio.planEstudioDet;
  paginationAsignaturasDet.updateList(planEstudioDet.value);
  paginationDataReforzamiento.updateList(item.value.planEstudioReforzamientos);
}

function editItemDetalle(planEstudioDetalle: planEstudioDetalle) {
  searchTextAsignaturas.value = planEstudioDetalle.asignaturaCodigoDescripcion;
  Object.assign(itemDetalle.value, planEstudioDetalle);
}

const formatearFechaInicio = computed({
  get() {
    if (!item.value.fechaInicio) return "";
    return item.value.fechaInicio.toISOString().split("T")[0]; // Formato YYYY-MM-DD
  },
  set(value: string) {
    item.value.fechaInicio = value ? new Date(value) : null;
  },
});

const formatearFechaFin = computed({
  get() {
    if (!item.value.fechaFin) return "";
    return item.value.fechaFin.toISOString().split("T")[0]; // Formato YYYY-MM-DD
  },
  set(value: string) {
    item.value.fechaFin = value ? new Date(value) : null;
  },
});

const isValid = computed(
  () =>
    item.value.descripcion != "" &&
    item.value.descripcion != null &&
    item.value.version != 0 &&
    item.value.carrera != null &&
    item.value.creditoMaximo != 0 &&
    item.value.tituloMasc != "" &&
    item.value.tituloMasc != null &&
    item.value.tituloFem != "" &&
    item.value.tituloFem != null
);

function onDateSelected(value: any) {
  item.value.fechaInicio = value;
  showDatePicker.value = false;
}

function selecionarFechaFin(value: any) {
  item.value.fechaFin = value;
  showDatePickerFin.value = false;
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await PlanEstudioService.getPlanesEstudio(
    value,
    pageNumber,
    pageSize
  );
  planesEstudio.value = response;
  loading.value = false;
}

async function searchPlanComplementario(value: string) {
  loading.value = true;
  const response = await PlanEstudioService.getPlanesEstudio(value);
  planesEstudioComplementario.value = response;
  loading.value = false;
}

async function searchCarreras(value: string) {
  loadingCarreras.value = true;
  const response = await lazyFetch(() => CarreraService.getCarreras(value));
  carreras.value = response;
  loadingCarreras.value = false;
}

async function searchAsignaturas(value: string) {
  loadingAsignaturas.value = true;
  const response = await lazyFetch(() =>
    AsignaturasService.getAsignaturas(value)
  );

  asignaturas.value = response;
  loadingAsignaturas.value = false;
}

async function updatePlanEstudio() {
  loading.value = true;
  if (!item.value.fechaFin) {
    item.value.fechaFin = null;
  }
  await apiQuery.put(
    `api/academicos/plan-estudio/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);
  loading.value = false;
}

async function savePlanEstudio() {
  loading.value = true;
  await apiQuery.post(`api/academicos/plan-estudio`, item.value);

  searchItem(item.value.descripcion);
  loading.value = false;
}

async function validateVersionPlanEstudio() {
  if (item.value.carrera != null && item.value.version != 0) {
    loading.value = true;
    const response = await apiQuery.get(
      `api/academicos/plan-estudio/${item.value.id}/${item.value.carrera}/${item.value.version}`
    );
    existeVersion.value = response;
    if (existeVersion.value) {
      item.value.version = Number(item.value.version) + 1;
    }
    loading.value = false;
  }
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    version: null,
    fechaInicio: new Date(),
    fechaFin: null,
    carrera: null,
    carreraDescripcion: "",
    carreraAlias: "",
    planComplementario: null,
    creditoMaximo: 0,
    resolucionConsejo: "",
    decretoNo: "",
    resolucionNo: "",
    tituloMasc: "",
    tituloFem: "",
    vigente: false,
    estatus: true,
    planEstudioDet: [],
    planEstudioCorrequisitos: [],
    planEstudioPrerrequisitos: [],
    planEstudioReforzamientos: [],
  };

  planEstudioCor.value = [];
  planEstudioPre.value = [];
  planEstudioRef.value = [];
  planEstudioDet.value = [];

  paginationAsignaturasDet.updateList(planEstudioDet.value);
  paginationDataReforzamiento.updateList(item.value.planEstudioReforzamientos);
  paginationDataCorrequisitos.updateList(planEstudioCor.value);
  paginationDataPrerrequisitos.updateList(planEstudioPre.value);

  searchCarreras("");
  searchPlanComplementario("");
}
function onAdd() {
  nuevoItem.value = true;
  onAddAsignatura();
}

function addReforzamiento() {
  nuevoItemReforzamiento.value = true;
  searchAsignaturas("");
  onAddReforzamiento();
}

function onAddReforzamiento() {
  itemReforzamiento.value = {
    id: 0,
    planEstudio: item.value.id,
    planEstudioDescripcion: item.value.descripcion,
    asignatura: null,
    asignaturaDescripcion: "",
    asignaturaCodigo: null,
  };
}

function onAddAsignatura() {
  itemDetalle.value = {
    id: 0,
    planEstudio: item.value.id,
    asignatura: null,
    asignaturaDescripcion: "",
    asignaturaCodigoDescripcion: "",
    codigo: "",
    cuatrimestre: 1,
    creditos: 0,
    ht: 0,
    hp: 0,
    hi: 0,
    hnp: 0,
    esCicloBasico: false,
    creditosAprobados: null,
    cuatrimestresAprobados: null,
    orden: 1,
    mescyt: true,
  };

  lastValue.value = "";
  searchAsignaturas("");
}

function onAddCorrequisito() {
  itemCorrequisito.value = {
    id: 0,
    asignaturaCodigoDescripcion: "",
    planEstudio: item.value.id,
    planEstudioDescripcion: item.value.descripcion,
    asignatura: 0,
    asignaturaDescripcion: "",
    correquisito: null,
    asignaturaCodigo: null,
    codigoCorrequisito: null,
    descripcionCorrequisito: null,
  };
}

function onAddPrerrequisito() {
  itemPrerrequisito.value = {
    id: 0,
    planEstudio: 0,
    planEstudioDescripcion: "",
    asignatura: 0,
    asignaturaDescripcion: "",
    asignaturaCodigoDescripcion: "",
    prerrequisito: null,
    asignaturaCodigo: null,
    prerrequisitoCodigo: null,
    prerrequisitoDescripcion: null,
  };
}

function assignOrder(cuatrimestre: number, newOrden: number) {
  // Get the list excluding the current item being reassigned
  const asignaturasOrdenar = item.value.planEstudioDet.filter(
    (x) =>
      x.cuatrimestre === cuatrimestre &&
      x.asignatura !== itemDetalle.value.asignatura
  );
  const maxOrden = asignaturasOrdenar.length;

  // Clamp newOrden to the valid range [1, maxOrden]
  newOrden = Math.max(1, Math.min(newOrden, maxOrden));

  // Shift affected items forward
  asignaturasOrdenar.forEach((item) => {
    if (item.orden >= newOrden) {
      item.orden++;
    }
  });

  // Assign the new order to the item being modified
  itemDetalle.value.orden = newOrden;

  // Reorder the list to maintain consistency and avoid duplicate `orden`
  const allInCuatrimestre = planEstudioDet.value
    .filter((x) => x.cuatrimestre === cuatrimestre)
    .sort((a, b) => {
      if (a.cuatrimestre === b.cuatrimestre) {
        return a.orden - b.orden;
      }
      return a.cuatrimestre - b.cuatrimestre;
    });
  // Reassign sequential orden to ensure consistency (1-based)
  allInCuatrimestre.forEach((item, index) => {
    item.orden = index + 1;
  });
}

function reassignOrderAfterRemoval(cuatrimestre: number, removedOrden: number) {
  const asignaturasOrdenar = planEstudioDet.value.filter(
    (x) => x.cuatrimestre === cuatrimestre && x.orden > removedOrden
  );

  asignaturasOrdenar.forEach((item) => {
    item.orden--;
  });
}

function onSelectAsignatura() {
  itemDetalle.value.asignaturaDescripcion = asignaturas.value.items.find(
    (x) => x.id === itemDetalle.value.asignatura
  )?.descripcion;

  itemDetalle.value.codigo = asignaturas.value.items.find(
    (x) => x.id === itemDetalle.value.asignatura
  )?.codigo;

  itemDetalle.value.asignaturaCodigoDescripcion = asignaturas.value.items.find(
    (x) => x.id === itemDetalle.value.asignatura
  )?.asignaturaCodigoDescripcion;

  itemDetalle.value.creditos = asignaturas.value.items.find(
    (x) => x.id === itemDetalle.value.asignatura
  )?.creditos;

  itemDetalle.value.hp = asignaturas.value.items.find(
    (x) => x.id === itemDetalle.value.asignatura
  )?.horasPracticas;

  itemDetalle.value.hi = asignaturas.value.items.find(
    (x) => x.id === itemDetalle.value.asignatura
  )?.horasInvestigacion;

  itemDetalle.value.hnp = asignaturas.value.items.find(
    (x) => x.id === itemDetalle.value.asignatura
  )?.horasNoPresenciales;

  itemDetalle.value.orden = planEstudioPre.value.length + 1;
}

function onSelectAsignaturaReforzamiento() {
  itemReforzamiento.value.asignaturaDescripcion = asignaturas.value.items.find(
    (x) => x.id === itemReforzamiento.value.asignatura
  )?.descripcion;

  itemReforzamiento.value.asignaturaCodigo = asignaturas.value.items.find(
    (x) => x.id === itemReforzamiento.value.asignatura
  )?.codigo;
}

function onSelectAsignaturaCorrequisito() {
  itemCorrequisito.value.asignatura = asignaturaOut.value.item.asignatura;
  itemCorrequisito.value.asignaturaDescripcion = item.value.planEstudioDet.find(
    (x) => x.asignatura === asignaturaOut.value.item.asignatura
  )?.asignaturaDescripcion;

  itemCorrequisito.value.asignaturaCodigo =
    item.value.planEstudioDet.find(
      (x) => x.asignatura === asignaturaOut.value.item.asignatura
    )?.codigo ?? null;
  itemCorrequisito.value.codigoCorrequisito =
    item.value.planEstudioDet.find(
      (x) => x.asignatura === itemCorrequisito.value.correquisito
    )?.codigo ?? null;

  itemCorrequisito.value.correquisito =
    item.value.planEstudioDet.find(
      (x) => x.asignatura === itemCorrequisito.value.correquisito
    )?.asignatura ?? 0;

  itemCorrequisito.value.descripcionCorrequisito =
    item.value.planEstudioDet.find(
      (x) => x.asignatura === itemCorrequisito.value.correquisito
    )?.asignaturaDescripcion ?? null;
}

function onSelectAsignaturaPrerrequisito() {
  itemPrerrequisito.value.asignatura = asignaturaOut.value.item.asignatura;
  itemPrerrequisito.value.asignaturaDescripcion =
    item.value.planEstudioDet.find(
      (x) => x.asignatura === asignaturaOut.value.item.asignatura
    )?.asignaturaDescripcion;

  itemPrerrequisito.value.asignaturaCodigo =
    item.value.planEstudioDet.find(
      (x) => x.asignatura === asignaturaOut.value.item.asignatura
    )?.codigo ?? null;

  itemPrerrequisito.value.prerrequisito =
    item.value.planEstudioDet.find(
      (x) => x.asignatura === itemPrerrequisito.value.prerrequisito
    )?.asignatura ?? 0;

  itemPrerrequisito.value.prerrequisitoCodigo =
    item.value.planEstudioDet.find(
      (x) => x.asignatura === itemPrerrequisito.value.prerrequisito
    )?.codigo ?? null;

  itemPrerrequisito.value.prerrequisitoDescripcion =
    item.value.planEstudioDet.find(
      (x) => x.asignatura === itemPrerrequisito.value.prerrequisito
    )?.asignaturaDescripcion ?? null;
}

function onSaveAsignaturaCorrequisito() {
  if (itemCorrequisito.value.id === 0) {
    const index = item.value.planEstudioCorrequisitos.findIndex(
      (x) => x.correquisito === itemCorrequisito.value.asignatura
    );
    if (index !== -1) {
      mensajeAsignaturaAdded(2);
    } else {
      item.value.planEstudioCorrequisitos.push(itemCorrequisito.value);
    }
  } else {
    const index = item.value.planEstudioCorrequisitos.findIndex(
      (x) => x.id === itemCorrequisito.value.id
    );
    if (index !== -1) {
      mensajeAsignaturaAdded(2);
    }
  }

  planEstudioCor.value = item.value.planEstudioCorrequisitos.filter(
    (x) =>
      x.asignatura === asignaturaOut.value.item.asignatura ||
      x.correquisito === asignaturaOut.value.item.asignatura
  );
  paginationDataCorrequisitos.updateList(planEstudioCor.value);
  onAddCorrequisito();
}

function onSaveAsignaturaPrerrequisito() {
  if (itemPrerrequisito.value.id === 0) {
    const index = item.value.planEstudioPrerrequisitos.findIndex(
      (x) => x.prerrequisito === itemPrerrequisito.value.asignatura
    );
    if (index !== -1) {
      mensajeAsignaturaAdded(1);
    } else {
      item.value.planEstudioPrerrequisitos.push(itemPrerrequisito.value);
    }
  } else {
    const index = item.value.planEstudioPrerrequisitos.findIndex(
      (x) => x.id === itemPrerrequisito.value.id
    );
    if (index !== -1) {
      mensajeAsignaturaAdded(1);
    }
  }
  planEstudioPre.value = item.value.planEstudioPrerrequisitos.filter(
    (x) => x.asignatura === asignaturaOut.value.item.asignatura
  );
  paginationDataPrerrequisitos.updateList(planEstudioPre.value);
  onAddPrerrequisito();
}

function onSaveAsignaturaDet() {
  if (itemDetalle.value.id === 0) {
    const index = item.value.planEstudioDet.findIndex(
      (x) => x.asignatura === itemDetalle.value.asignatura
    );
    if (index !== -1) {
      mensajeAsignaturaAdded(4);
    } else {
      item.value.planEstudioDet.push(itemDetalle.value);
    }
  } else {
    const index = item.value.planEstudioDet.findIndex(
      (x) =>
        x.id === itemDetalle.value.id &&
        x.asignatura !== itemDetalle.value.asignatura
    );
    if (index !== -1) {
      mensajeAsignaturaAdded(4);
    } else {
      const index = item.value.planEstudioDet.findIndex(
        (x) => x.id === itemDetalle.value.id
      );
      if (index !== -1) {
        item.value.planEstudioDet[index] = itemDetalle.value;
      }
    }
  }
  paginationAsignaturasDet.updateList(item.value.planEstudioDet);
  assignOrder(itemDetalle.value.cuatrimestre, Number(itemDetalle.value.orden));
  planEstudioDet.value.sort((a, b) => {
    if (a.cuatrimestre === b.cuatrimestre) {
      return a.orden - b.orden;
    }
    return a.cuatrimestre - b.cuatrimestre;
  });

  onAddAsignatura();
  validarCreditoMaximo();
}

function onSaveAsignaturaReforzamiento() {
  if (itemReforzamiento.value.id === 0) {
    const index = item.value.planEstudioReforzamientos.findIndex(
      (x) => x.asignatura === itemReforzamiento.value.asignatura
    );
    if (index !== -1) {
      mensajeAsignaturaAdded(3);
    } else {
      item.value.planEstudioReforzamientos.push(itemReforzamiento.value);
    }
  } else {
    const index = item.value.planEstudioReforzamientos.findIndex(
      (x) => x.id === itemReforzamiento.value.id
    );
    if (index !== -1) {
      mensajeAsignaturaAdded(3);
    }
  }

  paginationDataReforzamiento.updateList(item.value.planEstudioReforzamientos);

  onAddReforzamiento();
}

function deleteAsignaturaDet(planEstudioDet: planEstudioDetalle) {
  const index = item.value.planEstudioDet.findIndex(
    (x) => x.asignatura === planEstudioDet.asignatura
  );
  if (index !== -1) {
    item.value.planEstudioDet.splice(index, 1);
  }

  reassignOrderAfterRemoval(
    planEstudioDet.cuatrimestre,
    Number(planEstudioDet.orden)
  );
}

function validarCreditoMaximo() {
  const allCreditos = item.value.planEstudioDet.reduce((acc, curr) => {
    return acc + Number(curr.creditos || 0); // handles null or undefined
  }, 0);

  if (Number(item.value.creditoMaximo) < allCreditos) {
    validateCreditoMaximo.value = true;
    item.value.creditoMaximo = allCreditos; // optionally update to match
  } else {
    validateCreditoMaximo.value = false;
  }
}

function doAction(action: number) {
  switch (action) {
    case 1:
      isPrerrequisito.value = true;
      isCorrequisito.value = false;
      if (item.value.planEstudioPrerrequisitos.length > 0) {
        planEstudioPre.value = item.value.planEstudioPrerrequisitos.filter(
          (x) => x.asignatura === asignaturaOut.value.item.asignatura
        );
      }
      filterPrerrequisitos();
      break;
    case 2:
      isPrerrequisito.value = false;
      isCorrequisito.value = true;
      if (item.value.planEstudioCorrequisitos.length > 0) {
        planEstudioCor.value = item.value.planEstudioCorrequisitos.filter(
          (x) =>
            x.asignatura === asignaturaOut.value.item.asignatura ||
            x.correquisito === asignaturaOut.value.item.asignatura
        );
      }
      filterCorrequisitos();
      break;
  }

  onAddCorrequisito();
  onAddPrerrequisito();
}

function esVigenteActivo() {
  if (item.value.estatus) {
    item.value.vigente = true;
  }
}

function mensajeAsignaturaAdded(action: number) {
  switch (action) {
    case 1:
      mensajeAsignaturaAddedDialog.value =
        "La asignatura ya fue agregada en los prerrequisitos";
      break;
    case 2:
      mensajeAsignaturaAddedDialog.value =
        "La asignatura ya fue agregada en los correquisitos";
      break;
    case 3:
      mensajeAsignaturaAddedDialog.value =
        "La asignatura ya fue agregada en los reforzamientos";
      break;
    case 4:
      mensajeAsignaturaAddedDialog.value =
        "La asignatura ya fue agregada en el plan de estudio";
      break;
  }
  asignaturaAdded.value = true;
}

function onCloseDialog() {
  asignaturaCorrequisito.value = null;
  asignaturasCorrequisitos.value = [];
  asignaturaPrerrequisito.value = null;
  asignaturasPrerrequisitos.value = [];
  isPrerrequisito.value = false;
  isCorrequisito.value = false;
}

function filterCorrequisitos() {
  const planDetalle = item.value.planEstudioDet;
  const planCorrequisito = item.value.planEstudioCorrequisitos;
  const asignatura = planDetalle.find(
    (x) => x.asignatura == asignaturaOut.value.item.asignatura
  );
  const correquisitosDeAsignatura = planCorrequisito.filter(
    (x) => x.asignatura === asignatura?.asignatura
  );

  asignaturasCorrequisitos.value = planDetalle.filter(
    (x) =>
      x.asignatura !== asignatura?.asignatura &&
      x.cuatrimestre == asignatura?.cuatrimestre &&
      !correquisitosDeAsignatura.some((y) => y.correquisito === x.asignatura)
  );

  paginationDataCorrequisitos.updateList(planEstudioCor.value);
}

function filterPrerrequisitos() {
  const planDetalle = item.value.planEstudioDet;
  const planPrerrequisitos = item.value.planEstudioPrerrequisitos;
  const asignatura = planDetalle.find(
    (x) => x.asignatura == asignaturaOut.value.item.asignatura
  );
  const prerrequisitosDeAsignatura = planPrerrequisitos.filter(
    (x) => x.asignatura === asignatura?.asignatura
  );

  asignaturasPrerrequisitos.value = planDetalle.filter(
    (x) =>
      x.asignatura !== asignatura?.asignatura &&
      x.cuatrimestre < (asignatura?.cuatrimestre ?? 1) &&
      !prerrequisitosDeAsignatura.some((y) => y.prerrequisito === x.asignatura)
  );
  paginationDataPrerrequisitos.updateList(planEstudioPre.value);
}
</script>

<template>
  <wFormData
    :panel="true"
    :loading="loading"
    :title="'Plan Estudio'"
    :filters="null"
    :headers="headers"
    :items="planesEstudio.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'1600px'"
    :filtervalue="null"
    :pageNumber="planesEstudio.pageNumber"
    :pageSize="planesEstudio.pageSize"
    :totalPages="planesEstudio.totalPages"
    :totalRecords="planesEstudio.totalRecords"
    @editItem="editItem"
    @update="updatePlanEstudio()"
    @searchItem="searchItem"
    @newItem="newItem"
    @save="savePlanEstudio()"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          v-model="item.descripcion"
          outlined
          dense
          prepend-inner-icon="mdi-file-document"
          counter="80"
        >
          <template #label>
            Descripción <span style="color: red">*</span>
          </template>
        </v-text-field></v-col
      >
      <v-col cols="12" sm="6">
        <v-text-field
          type="number"
          v-model="item.version"
          outlined
          dense
          prepend-inner-icon="mdi-format-list-numbered"
          @blur="validateVersionPlanEstudio()"
        >
          <template #label>
            Versión <span style="color: red">*</span>
          </template></v-text-field
        ></v-col
      >
      <v-col cols="12" sm="4">
        <v-autocomplete
          v-model="item.carrera"
          :items="carreras.items"
          v-model:search="searchTextCarreras"
          :loading="loadingCarreras"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          item-value="id"
          item-title="aliasDescripcionCarrera"
          color="primary"
          outlined
          dense
          :custom-filter="customFilter"
          @update:modelValue="(value) => (item.carrera = value)"
          @update:search="(value) => (searchTextCarreras = value)"
          @update:model-value="validateVersionPlanEstudio()"
        >
          <template #label>
            Carrera <span style="color: red">*</span>
          </template>
        </v-autocomplete>
      </v-col>
      <v-col cols="12" sm="4">
        <v-autocomplete
          :items="planesEstudioComplementario.items"
          v-model:search="searchTextPlanComplementario"
          :loading="loadingPlanComplementario"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          item-value="id"
          item-title="descripcion"
          color="primary"
          label="Plan Complementario"
          v-model="item.planComplementario"
          outlined
          dense
          clearable
          :custom-filter="customFilter"
          @update:modelValue="(value) => (item.planComplementario = value)"
          @update:search="(value) => (searchTextPlanComplementario = value)"
        ></v-autocomplete
      ></v-col>
      <v-col cols="12" sm="4">
        <v-dialog v-model="showDatePicker" width="300px">
          <template #activator="{ props }">
            <v-text-field
              v-model="formatearFechaInicio"
              hint="YYYY-MM-DD"
              persistent-hint
              prepend-inner-icon="mdi-calendar"
              v-bind="props"
            >
              <template #label>
                Fecha Inicio <span style="color: red">*</span>
              </template>
            </v-text-field>
          </template>

          <v-date-picker
            show-adjacent-months
            v-model="item.fechaInicio"
            @update:model-value="onDateSelected"
            color="primary"
          />
        </v-dialog>
      </v-col>
      <v-col cols="12" sm="4">
        <v-dialog v-model="showDatePickerFin" width="300px">
          <template #activator="{ props }">
            <v-text-field
              v-model="formatearFechaFin"
              label="Fecha Final"
              hint="YYYY-MM-DD"
              prepend-inner-icon="mdi-calendar"
              persistent-hint
              v-bind="props"
              clearable
            />
          </template>
          <v-date-picker
            show-adjacent-months
            v-model="item.fechaFin"
            @update:model-value="selecionarFechaFin"
            color="primary"
          />
        </v-dialog>
      </v-col>
      <v-col cols="12" sm="4">
        <v-text-field
          type="number"
          v-model="item.creditoMaximo"
          outlined
          dense
          @blur="validarCreditoMaximo()"
        >
          <template #label>
            Crédito Máximo <span style="color: red">*</span>
          </template></v-text-field
        ></v-col
      >
      <v-col cols="12" sm="4">
        <v-text-field
          label="Decreto No."
          v-model="item.decretoNo"
          prepend-inner-icon="mdi-gavel"
          outlined
          dense
          counter="50"
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Resolución Consejo"
          v-model="item.resolucionConsejo"
          outlined
          dense
          counter="50"
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Resolución Consejo No."
          v-model="item.resolucionNo"
          outlined
          dense
          counter="50"
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          v-model="item.tituloMasc"
          outlined
          dense
          prepend-inner-icon="mdi-school"
          counter="200"
        >
          <template #label>
            Título Masculino <span style="color: red">*</span>
          </template></v-text-field
        ></v-col
      >
      <v-col cols="12" sm="6">
        <v-text-field
          v-model="item.tituloFem"
          outlined
          dense
          prepend-inner-icon="mdi-school"
          counter="200"
        >
          <template #label>
            Título Femenino <span style="color: red">*</span>
          </template></v-text-field
        ></v-col
      >
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Está Vigente"
          v-model="item.vigente"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estatus"
          outlined
          dense
          @change="esVigenteActivo()"
        ></v-switch>
      </v-col>
      <v-col cols="12">
        <v-alert
          color="primary"
          v-if="!isValid"
          icon="mdi-information"
          text="No puede agregar asignaturas si no ha llenado los campos obligatorios del plan de estudio"
          variant="tonal"
        ></v-alert>
      </v-col>
      <v-card>
        <v-card-title>
          <v-toolbar
            class="text-white rounded"
            color="primary"
            density="comfortable"
          >
            <v-toolbar-title>Asignaturas del Plan</v-toolbar-title>
            <v-btn icon @click="onAdd">
              <v-icon>mdi-plus</v-icon>
            </v-btn>
          </v-toolbar>
        </v-card-title>
        <v-card-text>
          <v-col cols="12">
            <v-text-field
              v-model="valueBusqueda"
              label="Buscar Asignaturas"
              variant="outlined"
              density="compact"
              clearable
              prepend-inner-icon="mdi-magnify"
            ></v-text-field>
            <wDataTable
              :dialogWidth="'1000px'"
              :loading="loading"
              :title="'Asignaturas del Plan'"
              :headers="headersAsignaturas"
              :items="paginationAsignaturasDet.paginatedData.value"
              :pageNumber="paginationAsignaturasDet.currentPage.value"
              :pageSize="paginationAsignaturasDet.itemsPerPage.value"
              :totalPages="paginationAsignaturasDet.totalPages.value"
              :totalRecords="item.planEstudioDet.length"
              v-model:add="nuevoItem"
              :edit="true"
              :eliminar="true"
              :imprimir="false"
              @editItem="editItemDetalle"
              @save="onSaveAsignaturaDet()"
              @update="onSaveAsignaturaDet()"
              @delete="deleteAsignaturaDet"
              @goTo="paginationAsignaturasDet.goToPage"
            >
              <template #editItem>
                <v-col cols="12" sm="12">
                  <v-autocomplete
                    v-model="itemDetalle.asignatura"
                    :loading="loadingAsignaturas"
                    :items="asignaturas.items"
                    item-title="asignaturaCodigoDescripcion"
                    item-value="id"
                    :loading-text="'Cargando...'"
                    :no-data-text="'No hay datos'"
                    :no-results-text="'No se encontraron resultados'"
                    label="Asignaturas"
                    density="compact"
                    variant="outlined"
                    :custom-filter="customFilter"
                    @update:modelValue="
                      (value) => (itemDetalle.asignatura = value)
                    "
                    @update:model-value="onSelectAsignatura()"
                    @update:search="(value) => (searchTextAsignaturas = value)"
                  >
                  </v-autocomplete>
                </v-col>
                <v-col cols="12" sm="3">
                  <v-text-field
                    type="number"
                    label="Cuatrimestre"
                    v-model="itemDetalle.cuatrimestre"
                    outlined
                    dense
                    min="1"
                  ></v-text-field
                ></v-col>
                <v-col cols="12" sm="3">
                  <v-text-field
                    type="number"
                    label="Créditos"
                    v-model="itemDetalle.creditos"
                    outlined
                    dense
                  ></v-text-field
                ></v-col>
                <v-col cols="12" sm="3">
                  <v-text-field
                    type="number"
                    label="Horas Teóricas"
                    v-model="itemDetalle.ht"
                    outlined
                    dense
                    prepend-inner-icon="mdi-clock"
                  ></v-text-field
                ></v-col>
                <v-col cols="12" sm="3">
                  <v-text-field
                    type="number"
                    label="Horas Prácticas"
                    v-model="itemDetalle.hp"
                    outlined
                    dense
                    prepend-inner-icon="mdi-clock"
                  ></v-text-field
                ></v-col>
                <v-col cols="12" sm="3">
                  <v-text-field
                    type="number"
                    label="Horas Investigación"
                    v-model="itemDetalle.hi"
                    prepend-inner-icon="mdi-clock"
                    outlined
                    dense
                  ></v-text-field
                ></v-col>
                <v-col cols="12" sm="3">
                  <v-text-field
                    type="number"
                    label="Horas No Presenciales"
                    v-model="itemDetalle.hnp"
                    prepend-inner-icon="mdi-clock"
                    outlined
                    dense
                  ></v-text-field
                ></v-col>
                <v-col cols="12" sm="3">
                  <v-text-field
                    type="number"
                    label="Cant. Créditos Aprobados"
                    v-model="itemDetalle.creditosAprobados"
                    outlined
                    dense
                  ></v-text-field
                ></v-col>
                <v-col cols="12" sm="3">
                  <v-text-field
                    type="number"
                    label="Cant. Cuatr. Aprobados"
                    v-model="itemDetalle.cuatrimestresAprobados"
                    outlined
                    dense
                  ></v-text-field
                ></v-col>
                <v-col cols="12" sm="3">
                  <v-text-field
                    type="number"
                    label="Orden"
                    v-model="itemDetalle.orden"
                    outlined
                    dense
                    prepend-inner-icon="mdi-format-list-numbered"
                  ></v-text-field
                ></v-col>
                <v-col cols="12" sm="3">
                  <v-switch
                    color="primary"
                    label="Asignatura Ciclo Básico"
                    v-model="itemDetalle.esCicloBasico"
                    outlined
                    dense
                  ></v-switch>
                </v-col>
                <v-col cols="12" sm="3">
                  <v-switch
                    color="primary"
                    label="Mescyt"
                    v-model="itemDetalle.mescyt"
                    outlined
                    dense
                  ></v-switch>
                </v-col>
              </template>
              <template #moreActions="item">
                <v-menu>
                  <template #activator="{ props }">
                    <v-btn
                      v-bind="props"
                      icon
                      @click.stop="asignaturaOut = item"
                    >
                      <v-icon>mdi-dots-vertical</v-icon>
                    </v-btn>
                  </template>

                  <v-list>
                    <v-list-item
                      @click="doAction(1)"
                      density="compact"
                      v-if="asignaturaOut.item.cuatrimestre > 1"
                    >
                      <template v-slot:prepend>
                        <v-icon icon="mdi-ab-testing"></v-icon>
                      </template>

                      <v-list-item-title>Prerrequisitos </v-list-item-title>
                    </v-list-item>

                    <v-list-item @click="doAction(2)">
                      <template v-slot:prepend>
                        <v-icon icon="mdi-diameter"></v-icon>
                      </template>
                      <v-list-item-title>Correquisitos</v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </template>
            </wDataTable>
          </v-col>
        </v-card-text>
      </v-card>
      <v-card class="mt-2">
        <v-card-title>
          <v-toolbar
            class="text-white rounded"
            color="primary"
            density="comfortable"
          >
            <v-toolbar-title>Asignauras Reforzamiento</v-toolbar-title>
            <v-btn v-if="isValid" icon @click="addReforzamiento()">
              <v-icon>mdi-plus</v-icon>
            </v-btn>
          </v-toolbar>
        </v-card-title>
        <v-card-text>
          <wDataTable
            :dialogWidth="'800px'"
            :loading="loading"
            :title="'Asignaturas Reforzamiento'"
            :headers="headersReforzamiento"
            :items="paginationDataReforzamiento.paginatedData.value"
            :pageNumber="paginationDataReforzamiento.currentPage.value"
            :pageSize="paginationDataReforzamiento.itemsPerPage.value"
            :totalPages="paginationDataReforzamiento.totalPages.value"
            :totalRecords="item.planEstudioReforzamientos.length"
            v-model:add="nuevoItemReforzamiento"
            :edit="true"
            :eliminar="true"
            :imprimir="false"
            @goTo="paginationDataReforzamiento.goToPage"
          >
            <template #editItem>
              <v-row>
                <!-- Autocomplete -->
                <v-col cols="12" sm="8">
                  <v-autocomplete
                    :loading="loadingReforzamiento"
                    v-model="itemReforzamiento.asignatura"
                    :items="asignaturas.items"
                    item-title="asignaturaCodigoDescripcion"
                    item-value="id"
                    :loading-text="'Cargando...'"
                    :no-data-text="'No hay datos'"
                    :no-results-text="'No se encontraron resultados'"
                    label="Asignaturas"
                    density="compact"
                    variant="outlined"
                    :custom-filter="customFilter"
                    @update:model-value="onSelectAsignaturaReforzamiento()"
                    @update:modelValue="
                      (value) => (itemReforzamiento.asignatura = value)
                    "
                    @update:search="
                      (value) => (searchTextReforzamiento = value)
                    "
                  />
                </v-col>

                <!-- Button -->
                <v-col cols="12" sm="4">
                  <v-btn
                    color="primary"
                    @click="onSaveAsignaturaReforzamiento()"
                  >
                    Agregar Reforzamiento
                  </v-btn>
                </v-col>
              </v-row>
            </template>
          </wDataTable>
        </v-card-text>
      </v-card>
      <v-dialog v-model="existeVersion" max-width="500px">
        <v-card>
          <v-card-title class="pa-4 bg-primary">
            <span class="text-h5">{{ "Advertencia" }}</span>
          </v-card-title>
          <v-card-text>
            <span class="text-h5">{{
              "Ya existe un plan estudio con esta versión"
            }}</span>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="error"
              variant="flat"
              dark
              @click="existeVersion = false"
              >Cancelar</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-dialog>
      <v-dialog v-model="validateCreditoMaximo" max-width="500px">
        <v-card>
          <v-card-title class="pa-4 bg-primary">
            <span class="text-h5">{{ "Advertencia" }}</span>
          </v-card-title>
          <v-card-text>
            <span class="text-h5">{{
              "La suma de los créditos de las asignaturas no puede ser mayor al crédito máximo"
            }}</span>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="error"
              variant="flat"
              dark
              @click="validateCreditoMaximo = false"
              >Cancelar</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-dialog>
      <v-dialog v-model="asignaturaAdded" max-width="500px">
        <v-card>
          <v-card-title class="pa-4 bg-primary">
            <span class="text-h5">{{ "Aviso" }}</span>
          </v-card-title>
          <v-card-text>
            <span class="text-h5">{{ mensajeAsignaturaAddedDialog }}</span>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="error"
              variant="flat"
              dark
              @click="asignaturaAdded = false"
              >Cancelar</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-dialog>
      <v-dialog v-model="isPrerrequisito" max-width="900px">
        <v-card>
          <v-card-title class="pa-4 bg-primary">
            <span class="text-h5">{{
              `Prerrequisitos: ${asignaturaOut.item.codigo} - ${asignaturaOut.item.asignaturaDescripcion}`
            }}</span>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" sm="5">
                <v-autocomplete
                  v-model="itemPrerrequisito.prerrequisito"
                  :items="asignaturasPrerrequisitos || undefined"
                  item-title="asignaturaCodigoDescripcion"
                  item-value="asignatura"
                  label="Asignaturas Prerrequisitos"
                  density="compact"
                  variant="outlined"
                  :hint="`Las asignaturas que puede eleigr como prerrequisito, son aquellas anterior al cuatrimestre ${asignaturaOut.item.cuatrimestre}.`"
                  persistent-hint
                  :custom-filter="customFilter"
                  @update:model-value="onSelectAsignaturaPrerrequisito()"
                ></v-autocomplete>
              </v-col>
              <v-col cols="12" sm="4">
                <v-btn color="primary" @click="onSaveAsignaturaPrerrequisito()">
                  Agregar Prerrequisito
                </v-btn>
              </v-col>
            </v-row>
            <v-card>
              <v-col cols="12">
                <wDataTable
                  :dialogWidth="'800px'"
                  :loading="loading"
                  :title="'Prerrequisitos del Plan'"
                  :headers="headersPrerrequisitos"
                  :items="paginationDataPrerrequisitos.paginatedData.value"
                  :pageNumber="paginationDataPrerrequisitos.currentPage.value"
                  :pageSize="paginationDataPrerrequisitos.itemsPerPage.value"
                  :totalPages="paginationDataPrerrequisitos.totalPages.value"
                  :totalRecords="item.planEstudioPrerrequisitos.length"
                  v-model:add="nuevoItem"
                  :edit="false"
                  :eliminar="true"
                  :imprimir="false"
                  @goTo="paginationDataPrerrequisitos.goToPage"
                ></wDataTable>
              </v-col>
            </v-card>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                color="primary"
                variant="flat"
                dark
                @click="onCloseDialog()"
                >Aceptar</v-btn
              >
            </v-card-actions>
          </v-card-text>
        </v-card>
      </v-dialog>
      <v-dialog v-model="isCorrequisito" max-width="900px">
        <v-card>
          <v-card-title class="pa-4 bg-primary">
            <span class="text-h5">{{
              `Correquisitos: ${asignaturaOut.item.codigo} - ${asignaturaOut.item.asignaturaDescripcion}`
            }}</span>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" sm="5">
                <v-autocomplete
                  v-model="itemCorrequisito.correquisito"
                  :items="asignaturasCorrequisitos || undefined"
                  item-title="asignaturaCodigoDescripcion"
                  item-value="asignatura"
                  label="Asignaturas Correquisitos"
                  density="compact"
                  variant="outlined"
                  :custom-filter="customFilter"
                  @update:model-value="onSelectAsignaturaCorrequisito()"
                ></v-autocomplete>
              </v-col>
              <v-col cols="12" sm="4">
                <v-btn color="primary" @click="onSaveAsignaturaCorrequisito()">
                  Agregar Correqusito
                </v-btn>
              </v-col>
            </v-row>
            <v-card>
              <v-col cols="12">
                <wDataTable
                  :dialogWidth="'800px'"
                  :loading="loading"
                  :title="'Correquisitos del Plan'"
                  :headers="headersCorrequisitos"
                  :items="paginationDataCorrequisitos.paginatedData.value"
                  :pageNumber="paginationDataCorrequisitos.currentPage.value"
                  :pageSize="paginationDataCorrequisitos.itemsPerPage.value"
                  :totalPages="paginationDataCorrequisitos.totalPages.value"
                  :totalRecords="item.planEstudioCorrequisitos.length"
                  v-model:add="nuevoItem"
                  :edit="false"
                  :eliminar="true"
                  :imprimir="false"
                  @goTo="paginationDataCorrequisitos.goToPage"
                ></wDataTable>
              </v-col>
            </v-card>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                color="primary"
                variant="flat"
                dark
                @click="onCloseDialog()"
                >Aceptar</v-btn
              >
            </v-card-actions>
          </v-card-text>
        </v-card>
      </v-dialog>
    </template>
  </wFormData>
</template>
