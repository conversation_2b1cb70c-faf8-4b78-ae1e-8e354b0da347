<script setup lang="ts">
import wFormData from "../../../../components/apps/wFormData.vue";
import { onMounted, ref, watch } from "vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { escuelas } from "@/utils/models/Academico/escuelas";
import escuelasServices, {
  EscuelasSevices,
} from "@/services/Academicos/EscuelasServices";
import { apiQuery } from "@/utils/helpers/apiQuery";
import lazyFetch from "@/utils/lazyFetch";
import customFilter from "@/utils/helpers/customFilter";

let loading = ref(false);
let loadingProcesosAcademicos = ref(false);
let searchText = ref("");

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Facultad", key: "facultadDescripcion" },
  { title: "Activo", key: "estado" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const escuela = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const procesosacademicos = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<escuelas>({
  id: 0,
  company: 0,
  descripcion: "",
  facultad: 0,
  facultadDescripcion: "",
  fechaInicio: new Date(),
  fechaFin: new Date(),
  telefono: "",
  email: "",
  director: "",
  resolucionNumero: "",
  resolucionFecha: new Date(),
  estado: "",
});

onMounted(async () => {
  await Promise.all([searchItem(""), getFacultades()]);
});

watch(searchText, (newValue) => {
  getProcesosAcademicos(newValue);
});

async function getProcesosAcademicos(value: string = "") {
  loadingProcesosAcademicos.value = true;

  const response = await lazyFetch(() => EscuelasSevices.getFacultad(value));

  procesosacademicos.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loadingProcesosAcademicos.value = false;
}
async function searchItem(value: string) {
  loading.value = true;
  const response = await escuelasServices.searchItem(value, 1, 10);
  escuela.value = response;
  loading.value = false;
}

async function getFacultades() {
  loading.value = true;
  const response = await escuelasServices.getFacultad("");
  procesosacademicos.value = response;
  loading.value = false;
}

function formatearFecha(fecha: Date): string {
  const d = new Date(fecha);
  const yyyy = d.getFullYear();
  const mm = ("0" + (d.getMonth() + 1)).slice(-2); // Alternativa a padStart
  const dd = ("0" + d.getDate()).slice(-2); // Alternativa a padStart
  return `${yyyy}-${mm}-${dd}`;
}

function editItem(escuelaEdit: escuelas) {
  item.value = {
    ...escuelaEdit,
    fechaInicio: new Date(escuelaEdit.fechaInicio),
    fechaFin: new Date(escuelaEdit.fechaFin),
  };
}

async function updateEscuela() {
  try {
    const response = await apiQuery.put(
      `api/academicos/escuela/${item.value.id}`,
      item.value
    );

    if (!response) {
      throw new Error("Network response was not ok");
    }

    searchItem(item.value.descripcion);
    loading.value = false;
    //return response;
  } catch (error) {
    console.error("Error fetching documentos:", error);
    throw error;
  }
}

function newItem() {
  item.value = {
    id: 0,
    company: 0,
    descripcion: "",
    facultad: 0,
    facultadDescripcion: "",
    fechaInicio: new Date(),
    fechaFin: new Date(),
    telefono: "",
    email: "",
    director: "",
    resolucionNumero: "",
    resolucionFecha: new Date(),
    estado: "",
  };
}

async function saveEscuela() {
  loading.value = true;
  const response = await apiQuery.post(`api/academicos/escuela`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Escuelas'"
    :filters="null"
    :headers="headers"
    :items="escuela.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="escuela.pageNumber"
    :pageSize="escuela.pageSize"
    :totalPages="escuela.totalPages"
    :totalRecords="escuela.totalRecords"
    @editItem="editItem"
    @searchItem="searchItem"
    @update="updateEscuela"
    @newItem="newItem()"
    @save="saveEscuela"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="12">
        <v-autocomplete
          label="Facultad"
          :items="procesosacademicos.items"
          v-model="item.facultadDescripcion"
          v-model:search="searchText"
          item-title="descripcion"
          item-value="id"
          density="compact"
          variant="outlined"
          :loading="loadingProcesosAcademicos"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.descripcion = value)"
          @update:search="(value) => (searchText = value)"
          :custom-filter="customFilter"
        >
        </v-autocomplete>
      </v-col>
      <v-col cols="12" sm="12">
        <v-text-field label="Descripción" v-model="item.descripcion">
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Fecha inicio"
          type="date"
          v-model="item.fechaInicio"
          :value="formatearFecha(item.fechaInicio)"
        >
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Fecha fin"
          type="date"
          v-model="item.fechaFin"
          :value="formatearFecha(item.fechaFin)"
        >
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field label="Telefono" type="phone" v-model="item.telefono">
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field label="Email" type="email" v-model="item.email">
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field label="Director" type="text" v-model="item.director">
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Amparada Resolucion No."
          type="text"
          v-model="item.resolucionNumero"
        >
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Fecha Resolucion"
          type="date"
          v-model="item.resolucionFecha"
          :value="formatearFecha(item.resolucionFecha)"
        >
        </v-text-field>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
