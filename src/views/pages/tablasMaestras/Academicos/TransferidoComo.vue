<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { transferidoComo } from "@/utils/models/Academico/transferidoComo";
import TransferidoComoService from "@/services/Academicos/TransferidoComoService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const transferidosComo = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<transferidoComo>({
  id: 0,
  descripcion: "",
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(transferidoComo: transferidoComo) {
  Object.assign(item.value, transferidoComo);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await TransferidoComoService.getTransferidosComo(
    value,
    pageNumber,
    pageSize
  );
  transferidosComo.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateTransferidoComo() {
  loading.value = true;
  await apiQuery.put(
    `api/academicos/transferido-como/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addTransferidoComo() {
  loading.value = true;
  await apiQuery.post(`api/academicos/transferido-como`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Transferido Como'"
    :filters="null"
    :headers="headers"
    :items="transferidosComo.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="transferidosComo.pageNumber"
    :pageSize="transferidosComo.pageSize"
    :totalPages="transferidosComo.totalPages"
    :totalRecords="transferidosComo.totalRecords"
    @editItem="editItem"
    @update="updateTransferidoComo()"
    @save="addTransferidoComo()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estatus"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
