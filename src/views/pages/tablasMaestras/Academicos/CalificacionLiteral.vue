<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { carreraTipoIntegrante } from "@/utils/models/Academico/carreraTipoIntegrante";
import type { calificacionLiteral } from "@/utils/models/Academico/calificacionLiteral";
import CarreraTipoIntegranteService from "@/services/Academicos/CarreraTipoIntegranteService";
import CalificacionLiteralService from "@/services/Academicos/CalificacionLiteralService";
import TipoGradoTituloService from "@/services/Academicos/TipoGradoTituloService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Literal", key: "literal" },
  { title: "Tipo Grado", key: "tipoGradoTituloDescripcion" },
  { title: "Punt<PERSON>", key: "puntos" },
  { title: "Rango Inicio", key: "rangoInicio" },
  { title: "Rango Fin", key: "rangoFin" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const calificacionesLiterales = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const tiposGradoTitulos = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<calificacionLiteral>({
  id: 0,
  descripcion: "",
  literal: "",
  tipoGradoTitulo: null,
  tipoGradoTituloDescripcion: "",
  supera: false,
  califica: false,
  retirada: false,
  seleccion: false,
  convalidada: false,
  colacionada: false,
  exonerada: false,
  puntos: 0,
  faltaNota: false,
  rangoInicio: 0,
  rangoFin: 0,
  digita: false,
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem(""), getTiposGradoTitulo()]);
});
//Metodos
function editItem(calificacionLiteral: calificacionLiteral) {
  Object.assign(item.value, calificacionLiteral);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await CalificacionLiteralService.getCalificacionesLiterales(
    value,
    pageNumber,
    pageSize
  );
  calificacionesLiterales.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function getTiposGradoTitulo() {
  loading.value = true;
  const response = await TipoGradoTituloService.getTiposGradoTitulo(
    "",
    1,
    100000
  );

  tiposGradoTitulos.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}

async function updateCalificacionLiteral() {
  loading.value = true;
  await apiQuery.put(
    `api/academicos/calificaciones-literales/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addCalificacionLiteral() {
  loading.value = true;
  await apiQuery.post(`api/academicos/calificaciones-literales`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    literal: "",
    tipoGradoTitulo: null,
    tipoGradoTituloDescripcion: "",
    supera: false,
    califica: false,
    retirada: false,
    seleccion: false,
    convalidada: false,
    colacionada: false,
    exonerada: false,
    puntos: 0,
    faltaNota: false,
    rangoInicio: 0,
    rangoFin: 0,
    digita: false,
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :panel="true"
    :loading="loading"
    :title="'Calificación Literal'"
    :filters="null"
    :headers="headers"
    :items="calificacionesLiterales.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="calificacionesLiterales.pageNumber"
    :pageSize="calificacionesLiterales.pageSize"
    :totalPages="calificacionesLiterales.totalPages"
    :totalRecords="calificacionesLiterales.totalRecords"
    @editItem="editItem"
    @update="updateCalificacionLiteral()"
    @save="addCalificacionLiteral()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="4">
        <v-autocomplete
          :items="tiposGradoTitulos.items"
          item-value="id"
          item-title="descripcion"
          color="primary"
          label="Tipo Grado Título"
          v-model="item.tipoGradoTitulo"
          outlined
          dense
        ></v-autocomplete
      ></v-col>
      <v-col cols="12" sm="4">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="4">
        <v-text-field
          label="Literal"
          v-model="item.literal"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Califica"
          v-model="item.califica"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Colacionada"
          v-model="item.colacionada"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Convalidada"
          v-model="item.convalidada"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Digita"
          v-model="item.digita"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Exonerada"
          v-model="item.exonerada"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Falta Nota"
          v-model="item.faltaNota"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Selección"
          v-model="item.seleccion"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estatus"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
