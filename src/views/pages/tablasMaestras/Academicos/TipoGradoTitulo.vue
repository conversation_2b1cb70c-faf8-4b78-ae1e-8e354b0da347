<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { tipoGradoTitulo } from "@/utils/models/Academico/tipoGradoTitulo";
import TipoGradoTituloService from "@/services/Academicos/TipoGradoTituloService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Puntuación", key: "puntuacion" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones" },
];

const tiposGradoTitulo = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<tipoGradoTitulo>({
  id: 0,
  descripcion: "",
  puntuacion: 0,
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(tipoGradoTitulo: tipoGradoTitulo) {
  Object.assign(item.value, tipoGradoTitulo);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await TipoGradoTituloService.getTiposGradoTitulo(
    value,
    pageNumber,
    pageSize
  );

  tiposGradoTitulo.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateTipoGradoTitulo() {
  loading.value = true;
  const response = await apiQuery.put(
    `api/academicos/tipos-grado-titulo/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addTipoGradoTitulo() {
  loading.value = true;
  const response = await apiQuery.post(
    `api/academicos/tipos-grado-titulo`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    puntuacion: 0,
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Tipo Grado Título'"
    :filters="null"
    :headers="headers"
    :items="tiposGradoTitulo.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tiposGradoTitulo.pageNumber"
    :pageSize="tiposGradoTitulo.pageSize"
    :totalPages="tiposGradoTitulo.totalPages"
    :totalRecords="tiposGradoTitulo.totalRecords"
    @editItem="editItem"
    @update="updateTipoGradoTitulo()"
    @save="addTipoGradoTitulo()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Puntuación"
          v-model="item.puntuacion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estatus"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
