<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { tipoAsignatura } from "@/utils/models/Academico/tipoAsignatura";
import type { tipoAsignaturaElectiva } from "@/utils/models/Academico/tiposAsignaturasElectivas";
import TiposAsignaturasElectivasService from "@/services/Academicos/TiposAsignaturasElectivasService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const tiposAsignaturas = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<tipoAsignaturaElectiva>({
  id: 0,
  descripcion: "",
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(tipoAsignatura: tipoAsignatura) {
  Object.assign(item.value, tipoAsignatura);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response =
    await TiposAsignaturasElectivasService.getTiposAsignaturasElectivas(
      value,
      pageNumber,
      pageSize
    );
  tiposAsignaturas.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateTipoAsignaturaElectiva() {
  loading.value = true;
  await apiQuery.put(
    `api/academicos/tipos-asignaturas-electivas/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}
async function addTipoAsignaturaElectiva() {
  loading.value = true;
  await apiQuery.post(`api/academicos/tipos-asignaturas-electivas`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Tipos Asignaturas Electivas'"
    :filters="null"
    :headers="headers"
    :items="tiposAsignaturas.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tiposAsignaturas.pageNumber"
    :pageSize="tiposAsignaturas.pageSize"
    :totalPages="tiposAsignaturas.totalPages"
    :totalRecords="tiposAsignaturas.totalRecords"
    @editItem="editItem"
    @update="updateTipoAsignaturaElectiva()"
    @save="addTipoAsignaturaElectiva()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estatus"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
