<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { carreras } from "@/utils/models/Academico/carreraAcademico";
import carrreraService from "@/services/Academicos/carreraAcademicoService";
import EscuelasSevices from "@/services/Academicos/EscuelasServices";
import lazyFetch from "@/utils/lazyFetch";
import TiposCarrerasService from "@/services/Academicos/TipoCarreraService";
import customFilter from "@/utils/helpers/customFilter";
import cuentaContableCatologoService from "@/services/Financieros/cuentaContableCatalogoFinancierosService";
import WDataTable from "@/components/apps/wDataTable.vue";
import type { carreraAnalista } from "@/utils/models/Academico/carreraAnalista";
import UsuariosService from "@/services/Seguridad/UsuariosService";
import SedesServices from "@/services/Planta_Fisica/SedesServices";
import CarreraTipoIntegranteService from "@/services/Academicos/CarreraTipoIntegranteService";
import type { ISedes } from "@/utils/models/Planta_Fisica/ISedes";

let loading = ref(false);
let searchTextEscuelas = ref("");
let searchTextTiposCarreras = ref("");
let searchTextCatalogoCuentas = ref("");
let searchTextUsuarios = ref("");
let searchTextSedes = ref("");
let searchTextCarreraAnalista = ref("");

const nuevoItem = ref(false);
const analistaAdded = ref(false);
const showDatePicker = ref(false);
const showDatePickerFin = ref(false);
const isValidComplete = ref(false);
const carreraAnalistasForm = ref();

const selectedSedes = ref<ISedes[]>([]);
const rules = {
  required: (value: any) => !!value || "Requerido.",
  email: (value: any) => /.+@.+\..+/.test(value) || "E-mail must be valid.",
};

const headers = [
  { title: "Alias", key: "alias" },
  { title: "Descripción", key: "descripcion" },
  { title: "Tipo Carrera", key: "tipoCarreraDes" },
  { title: "Cuenta", key: "cuCodigo" },
  { title: "Descripción Cuenta", key: "cuDescripcion" },
  { title: "Activo", key: "estado" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const headersCarrerasAnalistas = [
  { title: "Recinto", key: "sedeDescripcion" },
  { title: "Tipo", key: "carreraTipoIntegranteDescripcion" },
  { title: "Nombre", key: "nombreCompletoUsuario" },
  { title: "Teléfono", key: "telefonoUsuario" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const headersCarrerasSedes = [{ title: "Recinto", key: "descripcion" }];

const carrera = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const usuarios = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const sedes = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const sedesCarrera = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const carreraTiposIntegrante = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const tiposCarrera = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const cuentasContableCatalogo = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const escuelas = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const item = ref<carreras>({
  id: 0,
  alias: "",
  descripcion: "",
  nombreCorto: "",
  escuelaId: null,
  escuelaDescripcion: "",
  fechaInicio: null,
  fechaFin: null,
  telefono: "",
  email: "",
  director: "",
  tipoCarreraId: null,
  tipoCarreraDes: "",
  emailAnalista: "",
  telefonoAnalista: "",
  cuentaContableCatalogoId: null,
  cuCodigo: "",
  cuDescripcion: "",
  cuentaContableGP: "",
  cuentaContableGPDimension: "",
  carreraAnalistas: [],
  carreraSedes: [],
  estado: false,
});

let itemAnalista = ref<carreraAnalista>({
  id: 0,
  carrera: 0,
  carreraDescripcion: null,
  sede: 0,
  sedeDescripcion: null,
  carreraTipoIntegrante: 0,
  carreraTipoIntegranteDescripcion: null,
  usuario: 0,
  nombreCompletoUsuario: null,
  correoUsuario: null,
  telefonoUsuario: null,
});

onMounted(async () => {
  await Promise.all([
    searchItem(""),
    searchEscuelas(""),
    searchTiposCarreras(""),
    searchCatalogoCuentas(""),
    searchUsuarios(""),
    searchCarreraTipoIntegrante(""),
    searchSedes(""),
  ]);
});

watch(searchTextTiposCarreras, (newValue) => {
  searchTiposCarreras(newValue);
});

watch(searchTextEscuelas, (newValue) => {
  searchEscuelas(newValue);
});

watch(searchTextCatalogoCuentas, (newValue) => {
  searchCatalogoCuentas(newValue);
});

watch(searchTextUsuarios, (newValue) => {
  searchUsuarios(newValue);
});

watch(searchTextSedes, (newValue) => {
  searchSedes(newValue);
});

watch(searchTextCarreraAnalista, (newValue) => {
  searchCarreraTipoIntegrante(newValue);
});

const formatearFechaInicio = computed({
  get() {
    if (!item.value.fechaInicio) return "";
    return item.value.fechaInicio.toISOString().split("T")[0]; // Formato YYYY-MM-DD
  },
  set(value: string) {
    item.value.fechaInicio = value ? new Date(value) : null;
  },
});

const formatearFechaFin = computed({
  get() {
    if (!item.value.fechaFin) return "";
    return item.value.fechaFin.toISOString().split("T")[0]; // Formato YYYY-MM-DD
  },
  set(value: string) {
    item.value.fechaFin = value ? new Date(value) : null;
  },
});

function usePagination(list: any[], itemsPerPageDefault = 10) {
  const dataList = ref(list);

  const currentPage = ref(1);
  const itemsPerPage = ref(itemsPerPageDefault);

  const totalPages = computed(() => {
    return Math.ceil(dataList.value.length / itemsPerPage.value) || 1;
  });

  const paginatedData = computed(() => {
    const start = (currentPage.value - 1) * itemsPerPage.value;
    const end = start + itemsPerPage.value;
    return dataList.value.slice(start, end);
  });

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page;
    }
  };

  const nextPage = () => {
    if (currentPage.value < totalPages.value) {
      currentPage.value += 1;
    }
  };

  const prevPage = () => {
    if (currentPage.value > 1) {
      currentPage.value -= 1;
    }
  };

  const setItemsPerPage = (newItemsPerPage: number) => {
    itemsPerPage.value = newItemsPerPage;
    currentPage.value = 1; // Reset to first page
  };

  watch(
    () => dataList.value.length,
    (newLength) => {
      const newTotalPages = Math.ceil(newLength / itemsPerPage.value) || 1;
      if (currentPage.value > newTotalPages) {
        currentPage.value = newTotalPages;
      }
    }
  );

  const updateList = (newList: any[]) => {
    dataList.value = newList;
  };

  return {
    currentPage,
    itemsPerPage,
    totalPages,
    paginatedData,
    goToPage,
    nextPage,
    prevPage,
    setItemsPerPage,
    updateList,
  };
}

const paginationCarrerasAnalistas = usePagination(item.value.carreraAnalistas);

async function onAdd() {
  nuevoItem.value = true;
  itemAnalista.value = {
    id: 0,
    carrera: item.value.id,
    carreraDescripcion: item.value.descripcion,
    sede: null,
    sedeDescripcion: null,
    carreraTipoIntegrante: null,
    carreraTipoIntegranteDescripcion: null,
    usuario: null,
    nombreCompletoUsuario: null,
    correoUsuario: null,
    telefonoUsuario: null,
  };
  await searchUsuarios("");
  await searchCarreraTipoIntegrante("");
  await searchSedes("");

  carreraAnalistasForm.value?.validate();
}

function onSelectUsuario() {
  var usuario = usuarios.value.items.find(
    (x) => x.id === itemAnalista.value.usuario
  );

  if (itemAnalista.value.carreraTipoIntegrante != null) {
    var carreraTipo = carreraTiposIntegrante.value.items.find(
      (x) => x.id === itemAnalista.value.carreraTipoIntegrante
    );

    itemAnalista.value.carreraTipoIntegranteDescripcion =
      carreraTipo.descripcion;
  }

  if (itemAnalista.value.sede != null) {
    var sede = sedes.value.items.find((x) => x.id === itemAnalista.value.sede);

    itemAnalista.value.sedeDescripcion = sede.descripcion;
  }

  itemAnalista.value.nombreCompletoUsuario = `${usuario?.nombre} ${usuario?.apellido}`;
  itemAnalista.value.telefonoUsuario = usuario.telefono;
  itemAnalista.value.correoUsuario = usuario.correo;
}

function deleteAnalistaDet(analista: carreraAnalista) {
  const index = item.value.carreraAnalistas.findIndex(
    (x) => x.usuario === analista.usuario
  );
  if (index !== -1) {
    item.value.carreraAnalistas.splice(index, 1);
  }
}

async function editItemAnalistas(carreraAnalista: carreraAnalista) {
  Object.assign(itemAnalista.value, carreraAnalista);
  await searchCarreraTipoIntegrante(
    carreraAnalista.carreraTipoIntegranteDescripcion ?? ""
  );
  await searchSedes(carreraAnalista.sedeDescripcion ?? "");
  await searchUsuarios(carreraAnalista.nombreCompletoUsuario ?? "");
}

function onAddCarreraAnalista() {
  itemAnalista.value = {
    id: 0,
    carrera: 0,
    carreraDescripcion: null,
    sede: 0,
    sedeDescripcion: null,
    carreraTipoIntegrante: 0,
    carreraTipoIntegranteDescripcion: null,
    usuario: 0,
    nombreCompletoUsuario: null,
    correoUsuario: null,
    telefonoUsuario: null,
  };
}

function mensajeAnalistaAdded() {
  analistaAdded.value = true;
}

function onSaveAnalistaDet() {
  if (itemAnalista.value.id === 0) {
    const index = item.value.carreraAnalistas.findIndex(
      (x) => x.usuario === itemAnalista.value.usuario
    );
    if (index !== -1) {
      mensajeAnalistaAdded();
    } else {
      item.value.carreraAnalistas.push(itemAnalista.value);
    }
  } else {
    const index = item.value.carreraAnalistas.findIndex(
      (x) =>
        x.id === itemAnalista.value.id &&
        x.usuario !== itemAnalista.value.usuario
    );
    if (index !== -1) {
      mensajeAnalistaAdded();
    } else {
      const index = item.value.carreraAnalistas.findIndex(
        (x) => x.id === itemAnalista.value.id
      );
      if (index !== -1) {
        item.value.carreraAnalistas[index] = itemAnalista.value;
      }
    }
  }
  paginationCarrerasAnalistas.updateList(item.value.carreraAnalistas);

  onAddCarreraAnalista();
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await carrreraService.searchItem(
    value,
    pageNumber,
    pageSize
  );
  carrera.value = response;
  loading.value = false;
}

async function searchEscuelas(value: string) {
  loading.value = true;
  const response = await lazyFetch(() => EscuelasSevices.searchItem(value));
  escuelas.value = response;
  loading.value = false;
}

async function searchSedes(value: string) {
  loading.value = true;
  const response = await lazyFetch(() => SedesServices.getSedes(value));
  sedes.value = response;
  loading.value = false;
}

async function searchSedesCarrera() {
  loading.value = true;
  const response = await SedesServices.getSedes("");
  sedesCarrera.value = response;
  loading.value = false;
}

async function searchUsuarios(value: string) {
  loading.value = true;
  const response = await lazyFetch(() => UsuariosService.getUsuarios(value));
  usuarios.value = response;
  loading.value = false;
}

async function searchTiposCarreras(value: string) {
  loading.value = true;
  const response = await lazyFetch(() =>
    TiposCarrerasService.getTiposCarreras(value)
  );
  tiposCarrera.value = response;
  loading.value = false;
}

async function searchCarreraTipoIntegrante(value: string) {
  loading.value = true;
  const response = await lazyFetch(() =>
    CarreraTipoIntegranteService.getCarrerasTipoIntegrante(value)
  );
  carreraTiposIntegrante.value = response;
  loading.value = false;
}

async function searchCatalogoCuentas(value: string) {
  loading.value = true;
  const response = await lazyFetch(() =>
    cuentaContableCatologoService.searchItem(value)
  );
  cuentasContableCatalogo.value = response;
  loading.value = false;
}

async function editItem(carreraEdit: carreras) {
  Object.assign(item.value, {
    ...carreraEdit,
    fechaFin: carreraEdit.fechaFin ? new Date(carreraEdit.fechaFin) : null,
    fechaInicio: carreraEdit.fechaInicio
      ? new Date(carreraEdit.fechaInicio)
      : null,
  });
  item.value.carreraAnalistas = carreraEdit.carreraAnalistas;
  item.value.carreraSedes = carreraEdit.carreraSedes;
  paginationCarrerasAnalistas.updateList(item.value.carreraAnalistas);

  await searchEscuelas(carreraEdit.escuelaDescripcion || "");
  await searchTiposCarreras(carreraEdit.tipoCarreraDes || "");
  await searchCatalogoCuentas(carreraEdit.cuDescripcion || "");
  await searchSedesCarrera();
  selectedSedes.value = sedesCarrera.value.items.filter((sede: ISedes) =>
    item.value.carreraSedes.some((cs: any) => cs.sede === sede.id)
  );
}

async function editItemAnalista(carreraAnalista: carreraAnalista) {
  Object.assign(itemAnalista.value, carreraAnalista);
  await searchSedesCarrera();
}

async function updateCarreras() {
  try {
    handleSelected();
    const response = await apiQuery.put(
      `api/academicos/carreras/${item.value.id}`,
      item.value
    );

    if (!response) {
      throw new Error("Network response was not ok");
    }

    searchItem(item.value.descripcion);
    loading.value = false;
    //return response;
  } catch (error) {
    console.error("Error actualizando carrera:", error);
    throw error;
  }
}

function onDateSelected(value: any) {
  item.value.fechaInicio = value;
  showDatePicker.value = false;
}

function seleccionarFechaFin(value: any) {
  item.value.fechaFin = value;
  showDatePickerFin.value = false;
}

async function newItem() {
  await searchEscuelas("");
  await searchTiposCarreras("");
  await searchCatalogoCuentas("");
  await searchSedesCarrera();
  item.value = {
    id: 0,
    alias: "",
    descripcion: "",
    nombreCorto: "",
    escuelaId: null,
    escuelaDescripcion: "",
    fechaInicio: new Date(),
    fechaFin: null,
    telefono: "",
    email: "",
    director: "",
    tipoCarreraId: null,
    tipoCarreraDes: "",
    emailAnalista: "",
    telefonoAnalista: "",
    cuentaContableCatalogoId: null,
    cuCodigo: "",
    cuDescripcion: "",
    cuentaContableGP: "",
    cuentaContableGPDimension: "",
    carreraAnalistas: [],
    carreraSedes: [],
    estado: true,
  };

  paginationCarrerasAnalistas.updateList(item.value.carreraAnalistas);
  selectedSedes.value = [];
}

async function saveCarreras() {
  loading.value = true;
  if (selectedSedes.value.length > 0) {
    handleSelected();
  }
  const response = await apiQuery.post(`api/academicos/carreras`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function handleSelected() {
  item.value.carreraSedes = selectedSedes.value.map((x) => {
    return {
      id: 0,
      carrera: item.value.id,
      carreraDescripcion: item.value.descripcion || "",
      sede: x.id,
      sedeDescripcion: x.descripcion || "",
    };
  });
}
</script>

<template>
  <WFormData
    :panel="true"
    :loading="loading"
    :title="'Carreras'"
    :filters="null"
    :headers="headers"
    :items="carrera.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'1400px'"
    :filtervalue="null"
    :pageNumber="carrera.pageNumber"
    :pageSize="carrera.pageSize"
    :totalPages="carrera.totalPages"
    :totalRecords="carrera.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateCarreras()"
    @newItem="newItem()"
    @save="saveCarreras()"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="8">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="4">
        <v-text-field
          color="primary"
          label="Alias"
          v-model="item.alias"
          outlined
          dense
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="12">
        <v-autocomplete
          v-model="item.escuelaId"
          :items="escuelas.items"
          item-value="id"
          item-title="descripcion"
          label="Escuela"
          outlined
          dense
          color="primary"
          :loading="loading"
          @update:search="(value) => (searchTextEscuelas = value)"
          @update:modelValue="(value) => (item.escuelaId = value)"
          :custom-filter="customFilter"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="4">
        <v-dialog v-model="showDatePicker" width="300px">
          <template #activator="{ props }">
            <v-text-field
              v-model="formatearFechaInicio"
              hint="YYYY-MM-DD"
              persistent-hint
              prepend-inner-icon="mdi-calendar"
              v-bind="props"
            >
              <template #label>
                Fecha Inicio <span style="color: red">*</span>
              </template>
            </v-text-field>
          </template>

          <v-date-picker
            show-adjacent-months
            v-model="item.fechaInicio"
            @update:model-value="onDateSelected"
            color="primary"
          />
        </v-dialog>
      </v-col>

      <v-col cols="12" sm="4">
        <v-dialog v-model="showDatePickerFin" width="300px">
          <template #activator="{ props }">
            <v-text-field
              v-model="formatearFechaFin"
              label="Fecha Final"
              hint="YYYY-MM-DD"
              prepend-inner-icon="mdi-calendar"
              persistent-hint
              v-bind="props"
              clearable
            />
          </template>
          <v-date-picker
            show-adjacent-months
            v-model="item.fechaFin"
            @update:model-value="seleccionarFechaFin"
            color="primary"
          />
        </v-dialog>
      </v-col>

      <v-col cols="12" sm="4">
        <v-autocomplete
          v-model="item.tipoCarreraId"
          :items="tiposCarrera.items"
          item-value="id"
          item-title="descripcion"
          label="Tipo Carrera"
          outlined
          dense
          color="primary"
          :loading="loading"
          @update:search="(value) => (searchTextTiposCarreras = value)"
          @update:modelValue="(value) => (item.tipoCarreraId = value)"
          :custom-filter="customFilter"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="8">
        <v-text-field
          color="primary"
          label="Nombre Corto"
          v-model="item.nombreCorto"
          outlined
          dense
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="4">
        <v-text-field
          color="primary"
          label="Teléfono"
          v-model="item.telefono"
          outlined
          dense
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="4">
        <v-text-field
          label="Correo"
          v-model="item.email"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="4">
        <v-text-field
          color="primary"
          label="Director"
          v-model="item.director"
          outlined
          dense
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="4">
        <v-text-field
          color="primary"
          label="Teléfono Analista"
          v-model="item.telefonoAnalista"
          outlined
          dense
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="5">
        <v-autocomplete
          v-model="item.cuentaContableCatalogoId"
          :items="cuentasContableCatalogo.items"
          item-value="id"
          item-title="descripcion"
          label="Cuenta Contable"
          outlined
          dense
          color="primary"
          :loading="loading"
          @update:search="(value) => (searchTextCatalogoCuentas = value)"
          @update:modelValue="
            (value) => (item.cuentaContableCatalogoId = value)
          "
          :custom-filter="customFilter"
        ></v-autocomplete>
      </v-col>

      <v-col cols="12" sm="2">
        <v-text-field
          color="primary"
          label="Cuenta Contable GP"
          v-model="item.cuentaContableGP"
          outlined
          dense
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="5">
        <v-text-field
          color="primary"
          label="Dimensión Cuenta Contable GP"
          v-model="item.cuentaContableGPDimension"
          outlined
          dense
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>

      <v-card>
        <v-card-title>
          <v-toolbar
            class="text-white rounded"
            color="primary"
            density="comfortable"
          >
            <v-toolbar-title
              >Coordinador | Analistas | Asistente</v-toolbar-title
            >
            <v-btn icon @click="onAdd()">
              <v-icon>mdi-plus</v-icon>
            </v-btn>
          </v-toolbar>
        </v-card-title>
        <v-card-text>
          <wDataTable
            :dialogWidth="'1000px'"
            :loading="loading"
            :title="'Coordinador | Analistas | Asistente'"
            :headers="headersCarrerasAnalistas"
            :items="paginationCarrerasAnalistas.paginatedData.value"
            :pageNumber="paginationCarrerasAnalistas.currentPage.value"
            :pageSize="paginationCarrerasAnalistas.itemsPerPage.value"
            :totalPages="paginationCarrerasAnalistas.totalPages.value"
            :totalRecords="item.carreraAnalistas.length"
            v-model:add="nuevoItem"
            :edit="true"
            :eliminar="true"
            :imprimir="false"
            :valid="isValidComplete"
            @editItem="editItemAnalistas"
            @save="onSaveAnalistaDet()"
            @update="onSaveAnalistaDet()"
            @delete="deleteAnalistaDet"
            @goTo="paginationCarrerasAnalistas.goToPage"
          >
            <template #editItem>
              <v-col cols="12">
                <v-form
                  ref="carreraAnalistasForm"
                  v-model="isValidComplete"
                  @submit.prevent="onSaveAnalistaDet()"
                >
                  <v-col cols="12" sm="12">
                    <v-autocomplete
                      v-model="itemAnalista.usuario"
                      :loading="loading"
                      :items="usuarios.items"
                      item-title="usuario"
                      item-value="id"
                      :loading-text="'Cargando...'"
                      :no-data-text="'No hay datos'"
                      :no-results-text="'No se encontraron resultados'"
                      label="Usuario"
                      density="compact"
                      variant="outlined"
                      :rules="[rules.required]"
                      :custom-filter="customFilter"
                      @editItem="editItemAnalista"
                      @update:modelValue="
                        (value) => (itemAnalista.usuario = value)
                      "
                      @update:model-value="onSelectUsuario()"
                      @update:search="(value) => (searchTextUsuarios = value)"
                    ></v-autocomplete>
                  </v-col>
                  <v-col cols="12" sm="12">
                    <v-autocomplete
                      v-model="itemAnalista.carreraTipoIntegrante"
                      :loading="loading"
                      :items="carreraTiposIntegrante.items"
                      item-title="descripcion"
                      item-value="id"
                      :loading-text="'Cargando...'"
                      :no-data-text="'No hay datos'"
                      :no-results-text="'No se encontraron resultados'"
                      label="Tipo Integrante"
                      density="compact"
                      variant="outlined"
                      :custom-filter="customFilter"
                      :rules="[rules.required]"
                      @update:model-value="onSelectUsuario()"
                      @update:modelValue="
                        (value) => (itemAnalista.carreraTipoIntegrante = value)
                      "
                      @update:search="
                        (value) => (searchTextCarreraAnalista = value)
                      "
                    ></v-autocomplete>
                  </v-col>
                  <v-col cols="12" sm="12">
                    <v-autocomplete
                      v-model="itemAnalista.sede"
                      :loading="loading"
                      :items="sedes.items"
                      item-title="descripcion"
                      item-value="id"
                      :loading-text="'Cargando...'"
                      :no-data-text="'No hay datos'"
                      :no-results-text="'No se encontraron resultados'"
                      label="Sede"
                      density="compact"
                      variant="outlined"
                      :rules="[rules.required]"
                      :custom-filter="customFilter"
                      @update:modelValue="
                        (value) => (itemAnalista.sede = value)
                      "
                      @update:model-value="onSelectUsuario()"
                      @update:search="(value) => (searchTextSedes = value)"
                    ></v-autocomplete>
                  </v-col>
                  <v-row>
                    <v-col cols="12" sm="4">
                      <v-text-field
                        readonly
                        color="primary"
                        label="Nombre"
                        v-model="itemAnalista.nombreCompletoUsuario"
                        outlined
                        dense
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" sm="4">
                      <v-text-field
                        readonly
                        color="primary"
                        label="Teléfono"
                        v-model="itemAnalista.telefonoUsuario"
                        outlined
                        dense
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" sm="4">
                      <v-text-field
                        readonly
                        color="primary"
                        label="Correo"
                        v-model="itemAnalista.correoUsuario"
                        outlined
                        dense
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-form>
              </v-col>
            </template>
          </wDataTable>
        </v-card-text>
      </v-card>

      <v-card class="mt-2">
        <v-card-title>
          <v-toolbar
            class="text-white rounded"
            color="primary"
            density="comfortable"
          >
            <v-toolbar-title>Recintos</v-toolbar-title>
          </v-toolbar>
        </v-card-title>
        <v-card-text>
          <v-data-table
            v-model="selectedSedes"
            :headers="headersCarrerasSedes"
            :items="sedesCarrera.items"
            show-select
            hide-default-footer
            density="compact"
            class="rounded-sm border"
            return-object
          >
            <template v-slot:no-data>
              <v-alert type="error">Datos no encontrados</v-alert>
            </template>
          </v-data-table>
        </v-card-text>
      </v-card>
      <v-dialog v-model="analistaAdded" max-width="500px">
        <v-card>
          <v-card-title class="pa-4 bg-primary">
            <span class="text-h5">{{ "Advertencia" }}</span>
          </v-card-title>
          <v-card-text>
            <span class="text-h5">{{
              "Ya este usuario fue agregado con un cargo diferente"
            }}</span>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="error"
              variant="flat"
              dark
              @click="analistaAdded = false"
              >Cancelar</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-dialog>
    </template>
  </WFormData>
</template>
