<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { tipoGraduacion } from "@/utils/models/Academico/tipoGraduacion";
import TiposGraduacionesService from "@/services/Academicos/tipoGraduacionService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const tiposGraduaciones = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<tipoGraduacion>({
  id: 0,
  descripcion: "",
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(tipoGraduacion: tipoGraduacion) {
  Object.assign(item.value, tipoGraduacion);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await TiposGraduacionesService.getTiposGraduaciones(
    value,
    pageNumber,
    pageSize
  );
  tiposGraduaciones.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateTipoGraduacion() {
  loading.value = true;
  await apiQuery.put(
    `api/academicos/tipos-graduaciones/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addTipoGraduacion() {
  loading.value = true;
  await apiQuery.post(`api/academicos/tipos-graduaciones`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estatus: false,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Tipos Graduaciones'"
    :filters="null"
    :headers="headers"
    :items="tiposGraduaciones.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tiposGraduaciones.pageNumber"
    :pageSize="tiposGraduaciones.pageSize"
    :totalPages="tiposGraduaciones.totalPages"
    :totalRecords="tiposGraduaciones.totalRecords"
    @editItem="editItem"
    @update="updateTipoGraduacion()"
    @save="addTipoGraduacion()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estatus"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
