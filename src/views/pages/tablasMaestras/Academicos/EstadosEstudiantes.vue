<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { estadoEstudiante } from "@/utils/models/Academico/EstadoEstudiante";
import EstadosEstudiantesService from "@/services/Academicos/EstadosEstudiantesService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Condición", key: "condicion" },
  { title: "Activo", key: "activo" },
  { title: "Permite Modificar", key: "permiteModificar" },
  { title: "Indica Graduado", key: "indicaGraduado" },
  { title: "Indica Retirado", key: "indicaRetirado" },
  { title: "Indica Suspensión", key: "indicaSuspension" },
  { title: "Indica Nuevo", key: "indicaNuevo" },
  { title: "Indica Readmisión", key: "indicaReadmision" },
  { title: "Indica Estatus Activo", key: "indicaEstatusActivo" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const estadosEstudiantes = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<estadoEstudiante>({
  id: 0,
  descripcion: "",
  condicion: false,
  activo: false,
  permiteModificar: false,
  indicaGraduado: false,
  indicaRetirado: false,
  indicaSuspension: false,
  indicaNuevo: false,
  indicaReadmision: false,
  indicaEstatusActivo: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(estadoEstudiante: estadoEstudiante) {
  Object.assign(item.value, estadoEstudiante);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await EstadosEstudiantesService.getEstadosEstudiantes(
    value,
    pageNumber,
    pageSize
  );
  estadosEstudiantes.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateEstadoEstudiante() {
  loading.value = true;
  await apiQuery.put(
    `api/academicos/estados-estudiantes/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addEstadoEstudiante() {
  loading.value = true;
  await apiQuery.post(`api/academicos/estados-estudiantes`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    condicion: false,
    activo: true,
    permiteModificar: false,
    indicaGraduado: false,
    indicaRetirado: false,
    indicaSuspension: false,
    indicaNuevo: false,
    indicaReadmision: false,
    indicaEstatusActivo: false,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Estados Estudiantes'"
    :filters="null"
    :headers="headers"
    :items="estadosEstudiantes.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="estadosEstudiantes.pageNumber"
    :pageSize="estadosEstudiantes.pageSize"
    :totalPages="estadosEstudiantes.totalPages"
    :totalRecords="estadosEstudiantes.totalRecords"
    @editItem="editItem"
    @update="updateEstadoEstudiante()"
    @save="addEstadoEstudiante()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Indica Nuevo"
          v-model="item.indicaNuevo"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Indica Retirado"
          v-model="item.indicaRetirado"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Indica Graduado"
          v-model="item.indicaGraduado"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Indica Estatus Activo"
          v-model="item.indicaEstatusActivo"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Indica Readmisión"
          v-model="item.indicaReadmision"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Indica Suspensión"
          v-model="item.indicaSuspension"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
