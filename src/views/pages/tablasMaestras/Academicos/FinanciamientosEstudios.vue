<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { financiamientoEstudio } from "@/utils/models/Academico/financiamientoEstudio";
import FinanciamientosEstudiosService from "@/services/Academicos/financiamientoEstudioService";

const headers = [
  { title: "Descripción", key: "nombre" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const financiamientosEstudios = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<financiamientoEstudio>({
  id: 0,
  nombre: "",
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(financiamientoEstudio: financiamientoEstudio) {
  Object.assign(item.value, financiamientoEstudio);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response =
    await FinanciamientosEstudiosService.getFinanciamientosEstudios(
      value,
      pageNumber,
      pageSize
    );
  financiamientosEstudios.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateFinanciamientoEstudio() {
  loading.value = true;
  await apiQuery.put(
    `api/academicos/financiamientos-estudios/${item.value.id}`,
    item.value
  );
  searchItem(item.value.nombre);

  loading.value = false;
}

async function addFinanciamientoEstudio() {
  loading.value = true;
  await apiQuery.post(`api/academicos/financiamientos-estudios`, item.value);
  searchItem(item.value.nombre);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    nombre: "",
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Financiamientos Estudios'"
    :filters="null"
    :headers="headers"
    :items="financiamientosEstudios.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="financiamientosEstudios.pageNumber"
    :pageSize="financiamientosEstudios.pageSize"
    :totalPages="financiamientosEstudios.totalPages"
    :totalRecords="financiamientosEstudios.totalRecords"
    @editItem="editItem"
    @update="updateFinanciamientoEstudio()"
    @save="addFinanciamientoEstudio()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.nombre"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estatus"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
