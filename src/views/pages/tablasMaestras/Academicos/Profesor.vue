<script setup lang="ts">
import { onMounted, ref, shallowRef, computed } from "vue";
import type { IProfesor } from "@/utils/models/Academico/IProfesor";
import wFormData from "../../../../components/apps/wFormData.vue";
import wDataTable from "../../../../components/apps/wDataTable.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import { ProfesoresService } from "@/services/Academicos/ProfesorServices";
import TipoEmailService from "@/services/Generales/TipoEmailServices";
import PaisService from "@/services/Academicos/PaisService";
import ProvinciaService from "@/services/Generales/ProvinciaServices";
import NacionalidadService from "@/services/Generales/NacionalidadServices";
import TipoSangreService from "@/services/Generales/TipoSangreServices";
import TipoTelefonoService from "@/services/Generales/TipoTelefono";
import TipoDocumentosServices from "@/services/Generales/TipoDocumento";
import CarrerasServices from "@/services/Academicos/CarrerasServicess";
import EstadoCivilService from "@/services/Generales/EstadoCivilServices";
import EstatusService from "@/services/Generales/EstatusServices";
import SedesServices from "@/services/Planta_Fisica/SedesServices";
import MunicipioServices from "@/services/Generales/MunicipioServices";
import wProfileCard from "@/components/apps/wProfileCard.vue";
import wTabsCard from "@/components/apps/wTabsCard.vue";
import { CaretUpIcon } from "vue-tabler-icons";

//interfaces
interface DocumentoIdentificacion {
  id: number;
  tipoDocumentoIdentificacion: number;
  valor: string;
}
interface tipoTelefonoInterfaz {
  tipoTelefono: number;
  id: number;
  telefono: string;
}
interface tipoEmailInterfaz {
  id: number | null;
  tipoEmail: number;
  email: string;
  tipoEmailDescripcion: string;
  principal: boolean;
  indice: number;
  estado: boolean;
}

const headers = [
  { title: "Codigo", key: "codigoReferencia" },
  { title: "Nombre", key: "nombreCompleto" },
  { title: "Carrera", key: "carreraCodigo" },
  { title: "Acciones", key: "acciones" },
];

const headerSeccionesClases = [
  { title: "Codigo", key: "codigoReferencia" },
  { title: "Descripcion", key: "nombreCompleto" },
  { title: "Seccion", key: "seccion" },
  { title: "creditos", key: "creditos" },
  { title: "Dia", key: "dia" },
  { title: "Hora desde", key: "seccion" },
  { title: "Hora Hasta", key: "seccion" },
  { title: "Acciones", key: "acciones" },
];

const Telefonoheaders = [
  { title: "Tipo", key: "telefonoDescripcion" },
  { title: "Telefono", key: "telefono" },
  { title: "Acciones", key: "acciones" },
];
const headersDocumentos = [
  { title: "Tipo de Documento", key: "documentoDescripcion" },
  { title: "Número de Documento", key: "valor" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const headerEmail = [
  { title: "Tipo", key: "tipoEmailDescripcion" },
  { title: "Email", key: "email" },
  { title: "Principal", key: "principal" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const paises = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const municipio = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const estatus = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const sedes = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const tipoEmails = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const carreras = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const nacionalidad = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const provincias = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const profesores = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const tipoSangre = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const tipoTelefono = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const tipoDocumentos = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const personaDocumentos = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const direccionPersona = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const personas = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const telefonosPersonas = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const emailPersona = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const estadoCivil = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<IProfesor>({
  id: 0,
  company: 0,
  emailEstado: true,
  codigoReferencia: "",
  sede: null,
  emailPrincipal: true,
  tipoTelefonos: null,
  personaId: 0,
  esElegible: false,
  email: "",
  tipoEmail: null,
  paisDireccion: null,
  categoriaPago: 0,
  fechaIngreso: new Date(),
  fechaInhabilidad: new Date(),
  inhabilidadObservacion: "",
  carrera: null,
  tipoDocumento: 0,
  carreraCodigo: "",
  carreraDescripcion: "",
  codigoPostal: "",
  documento: "",
  telefono: "",
  esDirector: false,
  esCoordinador: false,
  esAdministrativo: false,
  casoEmergencia: "",
  casoEmergenciaTelefono: "",
  esExterno: false,
  educacionContinua: false,
  estado: 1,
  estadoDescripcion: "",
  nombreCompleto: "",
  nombres: "",
  apellidos: "",
  sexoId: null,
  estadoCivil: null,
  fechaNacimiento: null,
  pais: 0,
  ciudad: null,
  nacionalidad: null,
  tipoSangre: null,
  alergico: "",
  foto: null,
  firmaUrl: null,
  direccion: "",
  provinciaDireccion: null,
  municipioDireccion: null,
  personaTelefonos: [],
  personaDocumentosIdentificacion: [],
  personaDireccion: [],
  personaEmails: [],
  personas: {
    nombres: "",
    apellidos: "",
    sexo: 0,
    estadoCivil: 0,
    fechaNacimiento: null,
    pais: null,
    ciudad: null,
    nacionalidad: null,
    tipoSangre: null,
    alergias: "",
  },
});

let loading = ref(false);

//abrir dialogo para cre los documentos de identificacion, email y telefonos
const dialogoDocumento = shallowRef(false);
const dialogoTelefono = shallowRef(false);
const dialogoEmail = shallowRef(false);
//Estados para Detectar los errores agregando y editando
const errorAddDocumento = ref("");
const errorTelefono = ref("");
const errorEmail = ref("");
//para obtener los Id de email, docuemnto y tleefonos para editar
const emailEditandoId = ref<number | null>(null);
const editingDocumentoId = ref<number | null>(null);
const editingTelefonoId = ref<number | null>(null);

const prueba = shallowRef(false);
const showDatePicker = ref(false);
const editarEmail = ref(false);

const tabsProfesores = [
  {
    label: "General",
    value: "general",
    icon: "mdi-account-badge",
    content: "This is Tab 1 content",
  },
  {
    label: "Secciones Clases",
    value: "secciones",
    icon: "mdi-school",
    content: "This is Tab 2 content",
  },
  {
    label: "Horario",
    value: "horario",
    icon: "mdi-alarm",
    content: "This is Tab 3 content",
  },
];

// Montar
onMounted(async () => {
  await Promise.all([
    searchItem(""),
    getPaises(),
    getNacionalidad(),
    getProvincias(),
    getTipoSangre(),
    getTipoTelefono(),
    getTipoDocumentos(),
    getCarreras(),
    getTipoEmail(),
    getEstadoCivil(),
    getEstatus(),
    getSedes(),
    getMunicipio(),
  ]);
});

//Metodos
function editItem(profesor: IProfesor) {
  Object.assign(item.value, {
    ...profesor,
    fechaNacimiento: profesor.fechaNacimiento
      ? new Date(profesor.fechaNacimiento)
      : null,
  });
  personaDocumentos.value.items = profesor.personaDocumentosIdentificacion;
  telefonosPersonas.value.items = profesor.personaTelefonos;
  emailPersona.value.items = profesor.personaEmails;
  const direcciones = profesor.personaDireccion;
  direcciones.map((x) => {
    item.value.direccion = x.direccion;
    item.value.paisDireccion = x.pais;
    item.value.codigoPostal = x.codigoPostal;
    item.value.provinciaDireccion = x.provincia;
    item.value.municipioDireccion = x.municipio;
  });
  console.log(profesor.personaDireccion);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await ProfesoresService.getProfesores(
    value,
    pageNumber,
    pageSize
  );

  profesores.value = response; // <- ¡Aquí está el cambio importante!
  loading.value = false;
}
const formatearFecha = computed({
  get() {
    if (!item.value.fechaNacimiento) return "";
    return item.value.fechaNacimiento.toISOString().split("T")[0];
  },
  set(value: string) {
    item.value.fechaNacimiento = value ? new Date(value) : null;
  },
});

function onDateSelected(value: any) {
  item.value.fechaNacimiento = value;
  showDatePicker.value = false;
}
const sexoOptions = [
  { label: "Masculino", value: 1 },
  { label: "Femenino", value: 2 },
];


function newItem() {
  emailPersona.value.items = [];
  telefonosPersonas.value.items = [];
  personaDocumentos.value.items = [];
  obtenerEstadoNombre();
  item.value = {
    id: 0,
    codigoPostal: "",
    emailPrincipal: true,
    company: 0,
    paisDireccion: null,
    codigoReferencia: "",
    sede: null,
    personaId: 0,
    esElegible: false,
    tipoDocumento: null,
    tipoEmail: null,
    documento: "",
    emailEstado: true,
    email: "",
    telefono: "",
    categoriaPago: 0,
    fechaIngreso: new Date(),
    fechaInhabilidad: new Date(),
    inhabilidadObservacion: "",
    carrera: null,
    carreraCodigo: "",
    carreraDescripcion: "",
    esDirector: false,
    tipoTelefonos: null,
    esCoordinador: false,
    esAdministrativo: false,
    casoEmergencia: "",
    casoEmergenciaTelefono: "",
    esExterno: false,
    educacionContinua: false,
    estado: 1,
    estadoDescripcion: "Activo",
    nombreCompleto: "",
    nombres: "",
    apellidos: "",
    sexoId: null,
    estadoCivil: null,
    fechaNacimiento: null,
    pais: 50,
    ciudad: null,
    nacionalidad: null,
    tipoSangre: null,
    alergico: "",
    foto: null,
    firmaUrl: null,
    direccion: "",
    provinciaDireccion: null,
    municipioDireccion: null,
    personaTelefonos: [],
    personaDocumentosIdentificacion: [],
    personaDireccion: [],
    personaEmails: [],
    personas: {
      nombres: "",
      apellidos: "",
      sexo: 0,
      estadoCivil: 0,
      fechaNacimiento: null,
      pais: null,
      ciudad: null,
      nacionalidad: null,
      tipoSangre: null,
      alergias: "",
    },
  };
}
async function getPaises() {
  loading.value = true;
  const response = await PaisService.getPaises("", 1, 100000);
  paises.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}
async function getProvincias() {
  loading.value = true;
  const response = await ProvinciaService.getProvincias("", 1, 100000);
  provincias.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}
async function getNacionalidad() {
  loading.value = true;
  const response = await NacionalidadService.getNacionalidad("", 1, 100000);
  nacionalidad.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}
async function getTipoSangre() {
  loading.value = true;
  const response = await TipoSangreService.getTipoSangre("", 1, 100000);
  tipoSangre.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}

async function getTipoTelefono() {
  loading.value = true;
  const response = await TipoTelefonoService.getTipoTelefono("", 1, 100000);
  tipoTelefono.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}

async function getTipoDocumentos() {
  loading.value = true;
  const response = await TipoDocumentosServices.getTipoDocumentos(
    "",
    1,
    100000
  );
  tipoDocumentos.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}

async function getCarreras() {
  loading.value = true;
  const response = await CarrerasServices.getCarrerasServicios("", 1, 100000);
  carreras.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}

async function getTipoEmail() {
  loading.value = true;
  const response = await TipoEmailService.getTipoEmail("", 1, 100000);
  tipoEmails.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}
async function getEstadoCivil() {
  loading.value = true;
  const response = await EstadoCivilService.getEstadosCiviles("", 1, 100000);
  estadoCivil.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}

async function getEstatus() {
  loading.value = true;
  const response = await EstatusService.getEstatus("", 1, 100000);
  estatus.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}

async function getSedes() {
  loading.value = true;
  const response = await SedesServices.getSedes("", 1, 100000);
  sedes.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}

async function getMunicipio() {
  loading.value = true;
  const response = await MunicipioServices.getMunicipio("", 1, 100000);
  municipio.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}
function editItemDocumentos(doc: DocumentoIdentificacion) {
  editingDocumentoId.value = doc.id;
  item.value.tipoDocumento = doc.tipoDocumentoIdentificacion;
  item.value.documento = doc.valor;
  dialogoDocumento.value = true;
}
function editTipoTelefono(doc: tipoTelefonoInterfaz) {
  editingTelefonoId.value = doc.id;
  item.value.tipoTelefonos = doc.tipoTelefono;
  item.value.telefono = doc.telefono;
  dialogoTelefono.value = true;
}
function ediTipoEmail(doc: tipoEmailInterfaz) {
  emailEditandoId.value = doc.id;
  item.value.tipoEmail = doc.tipoEmail;
  item.value.email = doc.email;
  item.value.emailEstado = doc.estado;
  item.value.emailPrincipal = doc.principal;
  editarEmail.value = true;
}
async function AddDocumento() {
  errorAddDocumento.value = "";
  item.value.tipoDocumento = null;
  item.value.documento = "";
  dialogoDocumento.value = true;
}

async function AddTelefonos() {
  errorTelefono.value = "";
  item.value.tipoTelefonos = null;
  item.value.telefono = "";
  dialogoTelefono.value = true;
}
async function AddEmail() {
  errorEmail.value = "";
  item.value.tipoEmail = null;
  item.value.email = "";
  dialogoEmail.value = true;
}

let tempIdCounter = -1;
async function guardarDocumentoIdentificacion() {
  // Busca la descripción del tipo de documento
  const tipoSeleccionado = tipoDocumentos.value.items.find(
    (t) => t.id === item.value.tipoDocumento
  );
  if (!tipoSeleccionado) {
    errorAddDocumento.value = "Tipo de documento no válido.";
    return;
  }

  // Validación de duplicado: IGNORA el registro que estés editando
  const documentoExistente = personaDocumentos.value.items.some(
    (doc) =>
      doc.tipoDocumentoIdentificacion === item.value.tipoDocumento &&
      doc.id !== editingDocumentoId.value
  );
  if (documentoExistente) {
    errorAddDocumento.value =
      "Ya existe un Documento de identificación con este tipo.";
    return;
  }

  if (editingDocumentoId.value !== null) {
    // —— MODO EDICIÓN ——————————————————————————————
    const idx = personaDocumentos.value.items.findIndex(
      (d) => d.id === editingDocumentoId.value
    );
    if (idx !== -1) {
      personaDocumentos.value.items[idx].valor = item.value.documento;
      personaDocumentos.value.items[idx].tipoDocumentoIdentificacion =
        item.value.tipoDocumento;
      personaDocumentos.value.items[idx].documentoDescripcion =
        tipoSeleccionado.descripcion;
    }
  } else {
    // —— MODO CREACIÓN —————————————————————————————
    const nuevoId = tempIdCounter--;
    const nuevoDocumento = {
      id: nuevoId,
      persona: 0,
      tipoDocumentoIdentificacion: item.value.tipoDocumento,
      valor: item.value.documento,
      documentoDescripcion: tipoSeleccionado.descripcion,
    };
    personaDocumentos.value.items.push(nuevoDocumento);
  }

  // Sincroniza el array en tu modelo principal
  item.value.personaDocumentosIdentificacion = [
    ...personaDocumentos.value.items,
  ];

  // ———— LIMPIEZA ————————————————————————————————
  errorAddDocumento.value = "";
  item.value.documento = "";
  item.value.tipoDocumento = null;
  editingDocumentoId.value = null; // aquí reseteas para la próxima creación
  dialogoDocumento.value = false;
}

let idCounterTelefono = -1;
async function guardarPersonaTelefono() {
  // 1) Busca la descripción del tipo de teléfono
  const tipoSeleccionado = tipoTelefono.value.items.find(
    (t) => t.id === item.value.tipoTelefonos
  );
  if (!tipoSeleccionado) {
    errorTelefono.value = "Tipo de teléfono no válido.";
    return;
  }

  // 2) Validación de duplicado, ignorando el que editas
  const existe = telefonosPersonas.value.items.some(
    (doc) =>
      doc.tipoTelefono === item.value.tipoTelefonos &&
      doc.id !== editingTelefonoId.value
  );
  if (existe) {
    errorTelefono.value = "Ya existe un Teléfono con este tipo.";
    return;
  }

  if (editingTelefonoId.value !== null) {
    // —— MODO EDICIÓN ——————————————————————————————
    const idx = telefonosPersonas.value.items.findIndex(
      (d) => d.id === editingTelefonoId.value
    );
    if (idx !== -1) {
      telefonosPersonas.value.items[idx].tipoTelefono =
        item.value.tipoTelefonos;
      telefonosPersonas.value.items[idx].telefono = item.value.telefono;
      telefonosPersonas.value.items[idx].telefonoDescripcion =
        tipoSeleccionado.descripcion;
    }
  } else {
    // —— MODO CREACIÓN —————————————————————————————
    const nuevoId = idCounterTelefono--;
    const nuevoTelefono = {
      id: nuevoId,
      persona: 0, // o el ID real
      tipoTelefono: item.value.tipoTelefonos,
      telefono: item.value.telefono,
      telefonoDescripcion: tipoSeleccionado.descripcion,
    };
    telefonosPersonas.value.items.push(nuevoTelefono);
  }

  // 3) Sincroniza el array en tu modelo principal
  item.value.personaTelefonos = [...telefonosPersonas.value.items];

  // 4) Reset UI / Estado
  errorTelefono.value = "";
  item.value.telefono = "";
  item.value.tipoTelefonos = null;
  editingTelefonoId.value = null;
  dialogoTelefono.value = false;
}

let idCounterEmail = -1;
async function guardarPersonasEmail() {
  // 2.1) Valida tipo
  const tipoSeleccionado = tipoEmails.value.items.find(
    (t) => t.id === item.value.tipoEmail
  );
  if (!tipoSeleccionado) {
    errorEmail.value = "Se requiere un Email valido.";
    return;
  }

  // 2.2) Comprueba duplicados (ignorando el email en edición)
  const existe = emailPersona.value.items.some(
    (doc) =>
      doc.tipoEmail === item.value.tipoEmail && doc.id !== emailEditandoId.value
  );
  if (existe) {
    errorEmail.value = "Ya existe un email con este tipo.";
    return;
  }

  if (emailEditandoId.value !== null) {
    // —— MODO EDICIÓN ——————————————————————————————
    const idx = emailPersona.value.items.findIndex(
      (e) => e.id === emailEditandoId.value
    );
    if (idx !== -1) {
      const target = emailPersona.value.items[idx];
      target.email = item.value.email;
      target.tipoEmail = item.value.tipoEmail;
      target.estado = item.value.emailEstado;
      target.principal = item.value.emailPrincipal;
      target.tipoEmailDescripcion = tipoSeleccionado.descripcion;
      target.modificado = 1;
    }
  } else {
    // —— MODO CREACIÓN —————————————————————————————
    const nuevoId = idCounterEmail--;
    const nuevoEmail = {
      id: nuevoId,
      email: item.value.email,
      tipoEmail: item.value.tipoEmail,
      estado: item.value.emailEstado,
      tipoEmailDescripcion: tipoSeleccionado.descripcion,
      principal: item.value.emailPrincipal,
      modificado: 1,
    };
    emailPersona.value.items.push(nuevoEmail);
  }

  // 2.3) Sincroniza tu modelo principal
  item.value.personaEmails = [...emailPersona.value.items];

  // 2.4) Limpieza de UI / estado
  errorEmail.value = "";
  item.value.email = "";
  item.value.tipoEmail = null;
  item.value.emailPrincipal = false;
  dialogoEmail.value = false;
}

async function crearPersonaDireccion() {
  const nuevaDireccion = {
    id: 0,
    pais: item.value.pais ?? 0,
    provincia: item.value.provinciaDireccion ?? 0,
    municipio: item.value.municipioDireccion ?? 0,
    ciudad: item.value.ciudad ?? 0,
    codigoPostal: item.value.codigoPostal,
    direccion: item.value.direccion,
    persona: 0,
  };

  const personaInfo = {
    nombres: item.value.nombres,
    apellidos: item.value.apellidos,
    sexo: item.value.sexoId ?? 0,
    estadoCivil: item.value.estadoCivil ?? 0,
    fechaNacimiento: item.value.fechaNacimiento,
    pais: item.value.pais,
    ciudad: item.value.ciudad,
    nacionalidad: item.value.nacionalidad ?? null,
    tipoSangre: item.value.tipoSangre ?? null,
    alergias: item.value.alergico,
  };

  direccionPersona.value.items.push(nuevaDireccion);

  item.value.personaDireccion = [nuevaDireccion];
  item.value.personas = personaInfo;
}

async function saveProfesor() {
  try {
    loading.value = true;

    // Llamada a función previa
    await crearPersonaDireccion();

    // Petición al backend
    const response = await apiQuery.post(
      `api/academicos/profesores`,
      item.value
    );

    // Procesamiento si todo sale bien
    searchItem(item.value.nombres);
  } catch (error) {
    console.error("Error al guardar el profesor:", error);
  } finally {
    // Esto se ejecuta siempre, haya error o no
    loading.value = false;
  }
}

async function updateProfesor() {
  try {
    loading.value = true;

    // Llamada a función previa
    await crearPersonaDireccion();

    // Petición al backend
    const response = await apiQuery.put(
      `api/academicos/profesores/${item.value.id}`,
      item.value
    );
    // Procesamiento si todo sale bien
    searchItem(item.value.nombres);
  } catch (error) {
    console.error("Error al guardar el profesor:", error);
  } finally {
    // Esto se ejecuta siempre, haya error o no
    loading.value = false;
  }
}

async function obtenerCarreraNombre() {
  const tipoSeleccionado = carreras.value.items.find(
    (t) => t.id === item.value.carrera
  );
  item.value.carreraDescripcion = tipoSeleccionado.descripcion;
}

async function obtenerEstadoNombre() {
  const tipoSeleccionado = estatus.value.items.find(
    (t) => t.id === item.value.estado
  );
  item.value.estadoDescripcion = tipoSeleccionado.descripcion;
}
</script>

<template>
  <wFormData
    :panel="true"
    :loading="loading"
    :title="'Profesores'"
    :filters="null"
    :headers="headers"
    :items="profesores.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'1200px'"
    :filtervalue="null"
    :pageNumber="profesores.pageNumber"
    :pageSize="profesores.pageSize"
    :totalPages="profesores.totalPages"
    :totalRecords="profesores.totalRecords"
    @editItem="editItem"
    @update="updateProfesor"
    @searchItem="searchItem"
    @newItem="newItem"
    @save="saveProfesor"
  >
    <template #editItemPanel>
      <v-row>
        <!-- Avatar y datos del profesor -->
        <v-col cols="12" sm="12" md="4" lg="3">
          <wProfileCard
            :codigo="item.codigoReferencia"
            :firstName="item.nombres"
            :lastName="item.apellidos"
            :iconIdentificacion="'mdi-school'"
            :identificacion="item.carreraDescripcion"
            email=""
            :status="item.estadoDescripcion"
            :activo="true"
            :profilePicture="'hola'"
          />
        </v-col>
        <!-- Contenido de Tabs -->
        <v-col cols="12" sm="12" md="8" lg="9">
          <wTabsCard :tabs="tabsProfesores">
            <template #general>
              <v-toolbar color="primary" class="rounded" density="compact" flat>
                <v-btn icon="mdi-account"></v-btn>
                <v-toolbar-title>Datos Generales</v-toolbar-title>
              </v-toolbar>
              <v-card class="pa-4">
                <v-row>
                  <v-col cols="12" sm="6">
                    <v-text-field
                      v-model="item.codigoReferencia"
                      label="Codigo"
                      :rules="[(v) => !!v || 'El Codigo Profesor es requerido']"
                    />
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-autocomplete
                      v-model="item.estado"
                      :items="estatus.items"
                      item-title="descripcion"
                      item-value="id"
                      label="Estatus"
                      density="compact"
                      variant="outlined"
                      @update:modelValue="obtenerEstadoNombre()"
                    ></v-autocomplete>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="12" sm="6">
                    <v-text-field
                      v-model="item.nombres"
                      label="Nombres"
                      :rules="[(v) => !!v || 'El Nombre es requerido']"
                    />
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-text-field
                      v-model="item.apellidos"
                      label="Apellidos"
                      :rules="[(v) => !!v || 'El Apellido es requerido']"
                    />
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-autocomplete
                      v-model="item.sede"
                      :items="sedes.items"
                      item-title="descripcion"
                      item-value="id"
                      label="Recintos"
                      density="compact"
                      variant="outlined"
                      :rules="[(v) => !!v || 'El Recinto es requerido']"
                    ></v-autocomplete>
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-text-field
                      v-model="item.casoEmergencia"
                      label="Caso Emergencia"
                    />
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-text-field
                      v-model="item.casoEmergenciaTelefono"
                      label="Teléfono Emergencia"
                    />
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-autocomplete
                      v-model="item.carrera"
                      :items="carreras.items"
                      item-title="descripcion"
                      item-value="id"
                      label="Carrera"
                      density="compact"
                      variant="outlined"
                      @update:modelValue="obtenerCarreraNombre()"
                      :rules="[(v) => !!v || 'La Carrera es requerida']"
                    ></v-autocomplete>
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-autocomplete
                      v-model="item.estadoCivil"
                      :items="estadoCivil.items"
                      item-title="descripcion"
                      item-value="id"
                      label="Estado Civil"
                      density="compact"
                      variant="outlined"
                      :rules="[(v) => !!v || 'El estado civil es requerido']"
                    ></v-autocomplete>
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-select
                      v-model="item.sexoId"
                      :items="sexoOptions"
                      label="Sexo"
                      item-value="value"
                      item-title="label"
                      :rules="[(v) => !!v || 'El sexo es requerido']"
                    />
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-autocomplete
                      v-model="item.pais"
                      :items="paises.items"
                      item-title="descripcion"
                      item-value="id"
                      label="Pais de Nacimiento"
                      density="compact"
                      variant="outlined"
                    ></v-autocomplete>
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-autocomplete
                      v-model="item.nacionalidad"
                      :items="nacionalidad.items"
                      item-title="descripcion"
                      item-value="id"
                      label="Nacionalidad"
                      density="compact"
                      variant="outlined"
                      :rules="[(v) => !!v || 'La Nacionalidad es requerida']"
                    ></v-autocomplete>
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-autocomplete
                      v-model="item.ciudad"
                      :items="provincias.items"
                      item-title="descripcion"
                      item-value="id"
                      label="Provincias"
                      density="compact"
                      variant="outlined"
                    ></v-autocomplete>
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-autocomplete
                      v-model="item.tipoSangre"
                      :items="tipoSangre.items"
                      item-title="descripcion"
                      item-value="id"
                      label="Tipo Sangre"
                      density="compact"
                      variant="outlined"
                    ></v-autocomplete>
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-text-field
                      v-model="item.alergico"
                      label="Alergias"
                      density="compact"
                      variant="outlined"
                    />
                  </v-col>

                  <v-col cols="12" sm="6">
                    <v-dialog v-model="showDatePicker" width="270px">
                      <template #activator="{ props }">
                        <v-text-field
                          v-model="formatearFecha"
                          label="Fecha Nacimiento"
                          hint="YYYY-MM-DD"
                          persistent-hint
                          v-bind="props"
                          :rules="[(v) => !!v || 'La Fecha es requerida']"
                        />
                      </template>
                      <v-date-picker
                        show-adjacent-months
                        v-model="item.fechaNacimiento"
                        @update:model-value="onDateSelected"
                        color="primary"
                        title=""
                      />
                    </v-dialog>
                  </v-col>

                  <v-col cols="12" sm="6">
                    <v-switch
                      color="primary"
                      label="Es Director"
                      v-model="item.esDirector"
                      outlined
                      dense
                    ></v-switch>
                  </v-col>
                  <v-col cols="12" sm="6">
                    <v-switch
                      color="primary"
                      label="Es Coordinador"
                      v-model="item.esCoordinador"
                      outlined
                      dense
                    ></v-switch>
                  </v-col>

                  <v-col cols="12" sm="6">
                    <v-switch
                      color="primary"
                      label="Es Administrativo"
                      v-model="item.esAdministrativo"
                      outlined
                      dense
                    ></v-switch>
                  </v-col>

                  <v-col cols="12" sm="6">
                    <v-switch
                      color="primary"
                      label="Es Elegible"
                      v-model="item.esElegible"
                      outlined
                      dense
                    ></v-switch>
                  </v-col>

                  <!-- Documento de indetificacion -->
                  <v-col class="mt-12" cols="12" sm="12" md="12">
                    <v-card class="rounded-xl">
                      <v-toolbar color="primary" dark class="rounded-xl mb-5">
                        <v-btn icon>
                          <v-icon>mdi-file-document</v-icon>
                        </v-btn>
                        <v-toolbar-title
                          >Documento Identificacion</v-toolbar-title
                        >
                        <v-btn icon @click.stop="AddDocumento">
                          <v-icon start>mdi-plus</v-icon>
                        </v-btn>
                      </v-toolbar>

                      <wDataTable
                        :dialogWidth="'800px'"
                        :loading="loading"
                        :title="'Documentos Identidad'"
                        :headers="headersDocumentos"
                        :items="personaDocumentos.items"
                        :pageNumber="personaDocumentos.pageNumber"
                        :pageSize="personaDocumentos.pageSize"
                        :totalPages="personaDocumentos.totalPages"
                        v-model:add="dialogoDocumento"
                        :totalRecords="personaDocumentos.totalRecords"
                        @editItem="editItemDocumentos"
                        @update="guardarDocumentoIdentificacion"
                        @save="guardarDocumentoIdentificacion"
                        @goTo="1"
                      >
                        <template #editItem>
                          <v-card-text>
                            <v-row dense>
                              <v-col cols="12" md="12" sm="12">
                                <v-autocomplete
                                  v-model="item.tipoDocumento"
                                  :items="tipoDocumentos.items"
                                  item-title="descripcion"
                                  item-value="id"
                                  label="Tipo Documentos"
                                  density="compact"
                                  variant="outlined"
                                ></v-autocomplete>
                              </v-col>
                              <v-col cols="12" md="12" sm="12">
                                <v-text-field
                                  label="Documento"
                                  v-model="item.documento"
                                />
                              </v-col>
                              <v-col>
                                <v-col cols="12"> </v-col>
                              </v-col>
                            </v-row>
                            <v-col cols="12" md="12" sm="5">
                              <span class="text-error">{{
                                errorAddDocumento
                              }}</span>
                            </v-col>
                          </v-card-text>
                        </template>
                      </wDataTable>
                    </v-card>
                  </v-col>

                  <!-- Direccion -->
                  <v-col class="mt-12" cols="12" sm="12" md="12">
                    <v-toolbar color="primary" dark class="rounded-xl mb-2">
                      <v-btn icon>
                        <v-icon>mdi-map-marker-radius</v-icon>
                      </v-btn>
                      <v-toolbar-title>Direccion</v-toolbar-title>
                    </v-toolbar>

                    <v-card class="rounded-xl pa-4">
                      <!-- Fila 1: País + Provincia -->
                      <v-row dense>
                        <v-col cols="12" sm="6">
                          <v-autocomplete
                            v-model="item.paisDireccion"
                            :items="paises.items"
                            item-title="descripcion"
                            item-value="id"
                            label="País"
                            density="compact"
                            variant="outlined"
                          />
                        </v-col>
                        <v-col cols="12" sm="6">
                          <v-autocomplete
                            v-model="item.provinciaDireccion"
                            :items="provincias.items"
                            item-title="descripcion"
                            item-value="id"
                            label="Provincia"
                            density="compact"
                            variant="outlined"
                          />
                        </v-col>
                      </v-row>

                      <!-- Fila 2: Municipio + Código Postal -->
                      <v-row dense>
                        <v-col cols="12" sm="6">
                          <v-autocomplete
                            v-model="item.municipioDireccion"
                            :items="municipio.items"
                            item-title="descripcion"
                            item-value="id"
                            label="Municipio"
                            density="compact"
                            variant="outlined"
                          />
                        </v-col>
                        <v-col cols="12" sm="6">
                          <v-text-field
                            v-model="item.codigoPostal"
                            label="Código Postal"
                            outlined
                            dense
                          />
                        </v-col>
                      </v-row>

                      <!-- Fila 3: Dirección (una sola columna) -->
                      <v-row dense>
                        <v-col cols="12">
                          <v-textarea
                            v-model="item.direccion"
                            label="Dirección"
                            outlined
                            dense
                          />
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-col>

                  <!--Telefono-->
                  <v-col cols="12" sm="12" md="12">
                    <v-card class="rounded-xl">
                      <v-toolbar color="primary" dark class="rounded-xl mb-2">
                        <v-btn icon>
                          <v-icon>mdi-phone</v-icon>
                        </v-btn>
                        <v-toolbar-title>Telefono</v-toolbar-title>
                        <v-btn icon @click="AddTelefonos">
                          <v-icon start>mdi-plus</v-icon>
                        </v-btn>
                      </v-toolbar>
                      <wDataTable
                        :dialogWidth="'800px'"
                        :loading="loading"
                        :title="'Telefonos'"
                        :headers="Telefonoheaders"
                        :items="telefonosPersonas.items"
                        :pageNumber="telefonosPersonas.pageNumber"
                        :pageSize="telefonosPersonas.pageSize"
                        :totalPages="telefonosPersonas.totalPages"
                        v-model:add="dialogoTelefono"
                        :totalRecords="telefonosPersonas.totalRecords"
                        @editItem="editTipoTelefono"
                        @update="guardarPersonaTelefono()"
                        @save="guardarPersonaTelefono()"
                        @goTo="1"
                      >
                        <template #editItem>
                          <v-card-text>
                            <v-row dense>
                              <v-col cols="12" md="12" sm="12">
                                <v-autocomplete
                                  v-model="item.tipoTelefonos"
                                  :items="tipoTelefono.items"
                                  item-title="descripcion"
                                  item-value="id"
                                  label="Tipo Telefono"
                                  density="compact"
                                  variant="outlined"
                                ></v-autocomplete>
                              </v-col>
                              <v-col cols="12" md="12" sm="12">
                                <v-text-field
                                  label="Telefono"
                                  v-model="item.telefono"
                                  :rules="[
                                    (v) => !!v || 'El teléfono es requerido',
                                    (v) =>
                                      /^\d{3}-\d{3}-\d{4}$/.test(v) ||
                                      'Formato inválido. Use ************',
                                  ]"
                                />
                              </v-col>
                              <v-col>
                                <v-col cols="12"> </v-col>
                              </v-col>
                            </v-row>
                            <v-col cols="12" md="12" sm="5">
                              <span class="text-error">{{
                                errorTelefono
                              }}</span>
                            </v-col>
                          </v-card-text>
                        </template>
                      </wDataTable>
                    </v-card>
                  </v-col>

                  <!--Email-->
                  <v-col class="mt-8" cols="12" sm="12" md="12">
                    <v-card>
                      <v-toolbar color="primary" dark class="rounded-xl mb-2">
                        <v-btn icon>
                          <v-icon>mdi-email-multiple</v-icon>
                        </v-btn>
                        <v-toolbar-title>Email</v-toolbar-title>
                        <v-btn icon @click="AddEmail">
                          <v-icon start>mdi-plus</v-icon>
                        </v-btn>
                      </v-toolbar>
                      <wDataTable
                        :dialogWidth="'800px'"
                        :loading="loading"
                        :title="'Email'"
                        :headers="headerEmail"
                        :items="emailPersona.items"
                        :pageNumber="emailPersona.pageNumber"
                        :pageSize="emailPersona.pageSize"
                        :totalPages="emailPersona.totalPages"
                        v-model:add="dialogoEmail"
                        :totalRecords="emailPersona.totalRecords"
                        @editItem="ediTipoEmail"
                        @update="guardarPersonasEmail"
                        @save="guardarPersonasEmail"
                        @goTo="1"
                      >
                        <template #editItem>
                          <v-card-text>
                            <v-row dense>
                              <v-col cols="12" md="12" sm="12">
                                <v-autocomplete
                                  v-model="item.tipoEmail"
                                  :items="tipoEmails.items"
                                  item-title="descripcion"
                                  item-value="id"
                                  label="Tipo Email"
                                  density="compact"
                                  variant="outlined"
                                ></v-autocomplete>
                              </v-col>
                              <v-col cols="12" md="12" sm="12">
                                <v-text-field
                                  label="Email"
                                  v-model="item.email"
                                  :rules="[
                                    (v) => !!v || 'El email es requerido',
                                    (v) =>
                                      /.+@.+\..+/.test(v) ||
                                      'El email no es válido',
                                  ]"
                                />
                              </v-col>
                              <v-col cols="12" sm="6">
                                <v-switch
                                  color="primary"
                                  label="Estado"
                                  v-model="item.emailEstado"
                                  outlined
                                  dense
                                ></v-switch>
                              </v-col>
                              <v-col cols="12" sm="6">
                                <v-switch
                                  color="primary"
                                  label="Principal"
                                  v-model="item.emailPrincipal"
                                  outlined
                                  dense
                                ></v-switch>
                              </v-col>
                            </v-row>
                            <v-col cols="12" md="12" sm="5">
                              <span class="text-error">{{ errorEmail }}</span>
                            </v-col>
                          </v-card-text>
                        </template>
                      </wDataTable>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card>
            </template>
            <template #secciones>
              <v-card class="pa-4">
                <v-toolbar
                  color="primary"
                  class="rounded"
                  density="compact"
                  flat
                >
                  <v-btn icon="mdi-school"></v-btn>
                  <v-toolbar-title
                    >Contenido de Secciones Clases</v-toolbar-title
                  >
                </v-toolbar>

                <wDataTable
                  :dialogWidth="'300px'"
                  :loading="loading"
                  :title="'Secciones Clases'"
                  :headers="headerSeccionesClases"
                  :items="emailPersona.items"
                  :pageNumber="emailPersona.pageNumber"
                  :pageSize="emailPersona.pageSize"
                  :totalPages="emailPersona.totalPages"
                  v-model:add="prueba"
                  :totalRecords="emailPersona.totalRecords"
                  @editItem="ediTipoEmail"
                  @update="guardarPersonasEmail"
                  @save="guardarPersonasEmail"
                  @goTo="1"
                >
                  <template> </template>
                </wDataTable>
              </v-card>
            </template>
            <template #horario>
              <v-card class="pa-4">
                <v-card class="pa-4">
                  <v-toolbar
                    color="primary"
                    class="rounded"
                    density="compact"
                    flat
                  >
                    <v-btn icon="mdi-school"></v-btn>
                    <v-toolbar-title
                      >Horario Disponible para Impartir Clases</v-toolbar-title
                    >
                  </v-toolbar>
                  <wDataTable
                    :dialogWidth="'800px'"
                    :loading="loading"
                    :title="'Horario Disponible para Impartir Clases'"
                    :headers="headerSeccionesClases"
                    :items="emailPersona.items"
                    :pageNumber="emailPersona.pageNumber"
                    :pageSize="emailPersona.pageSize"
                    :totalPages="emailPersona.totalPages"
                    v-model:add="prueba"
                    :totalRecords="emailPersona.totalRecords"
                    @editItem="ediTipoEmail"
                    @update="guardarPersonasEmail"
                    @save="guardarPersonasEmail"
                    @goTo="1"
                  >
                    <template> </template>
                  </wDataTable>
                </v-card>

                <V-card class="mt-8 pa-4">
                  <!-- <v-toolbar
                    color="primary"
                    class="rounded"
                    density="compact"
                    flat
                  >
                    <v-btn icon="mdi-school"></v-btn>
                    <v-toolbar-title
                      >Asignaturas que Puedo Impartir</v-toolbar-title
                    >
                  </v-toolbar> -->
                </V-card>
              </v-card>
            </template>
          </wTabsCard>
        </v-col>
      </v-row>
    </template>
  </wFormData>
</template>
