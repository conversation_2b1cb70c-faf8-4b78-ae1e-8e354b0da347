<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { carreraTipoIntegrante } from "@/utils/models/Academico/carreraTipoIntegrante";
import CarreraTipoIntegranteService from "@/services/Academicos/CarreraTipoIntegranteService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Es Analista", key: "esAnalista" },
  { title: "Es Asistente", key: "esAsistente" },
  { title: "Es Coordinador", key: "esCoordinador" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const carreraTipoIntegrantes = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<carreraTipoIntegrante>({
  id: 0,
  descripcion: "",
  estatus: true,
  esCoordinador: false,
  esAnalista: false,
  esAsistente: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(carreraTipoIntegrante: carreraTipoIntegrante) {
  Object.assign(item.value, carreraTipoIntegrante);
  verifyCargo();
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await CarreraTipoIntegranteService.getCarrerasTipoIntegrante(
    value,
    pageNumber,
    pageSize
  );
  carreraTipoIntegrantes.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateCarreraTipoIntegrante() {
  loading.value = true;
  await apiQuery.put(
    `api/academicos/carrera-tipo-integrante/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addCarreraTipoIntegrante() {
  loading.value = true;
  await apiQuery.post(`api/academicos/carrera-tipo-integrante`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estatus: false,
    esCoordinador: false,
    esAnalista: false,
    esAsistente: false,
  };
}

function verifyCargo() {
  if (item.value.esAnalista) {
    item.value.esAsistente = false;
    item.value.esCoordinador = false;
  } else if (item.value.esAsistente) {
    item.value.esAnalista = false;
    item.value.esCoordinador = false;
  } else if (item.value.esCoordinador) {
    item.value.esAnalista = false;
    item.value.esAsistente = false;
  }
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Carrera Tipo Integrantes'"
    :filters="null"
    :headers="headers"
    :items="carreraTipoIntegrantes.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="carreraTipoIntegrantes.pageNumber"
    :pageSize="carreraTipoIntegrantes.pageSize"
    :totalPages="carreraTipoIntegrantes.totalPages"
    :totalRecords="carreraTipoIntegrantes.totalRecords"
    @editItem="editItem"
    @update="updateCarreraTipoIntegrante()"
    @save="addCarreraTipoIntegrante()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estatus"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Es Analista"
          v-model="item.esAnalista"
          outlined
          dense
          :disabled="item.esAsistente || item.esCoordinador"
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Es Asistente"
          v-model="item.esAsistente"
          outlined
          dense
          :disabled="item.esAnalista || item.esCoordinador"
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Es Coordinador"
          v-model="item.esCoordinador"
          outlined
          dense
          :disabled="item.esAnalista || item.esAsistente"
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
