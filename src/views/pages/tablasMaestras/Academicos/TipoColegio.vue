<script setup lang="ts">
import { onMounted, ref } from "vue";
import type { ITipoColegio } from "@/utils/models/Academico/ITipoColegio";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import {TipoColegioService} from "@/services/Academicos/TipoColegioServices"

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Publico", key: "publico" },
  { title: "Estado", key: "estado" },
  { title: "Acciones", key: "acciones" },
];

const tipoColegios = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});


const paises = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<ITipoColegio>({
  id: 0,
  company: 0,
  descripcion: "",
  publico:false,
  estado: false
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});

//Metodos
function editItem(TipoColegio: ITipoColegio) {
  Object.assign(item.value, TipoColegio);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await TipoColegioService.getTipoColegio(
    value,
    pageNumber,
    pageSize
  );
  tipoColegios.value = response; // <- ¡Aquí está el cambio importante!
  loading.value = false;
}

async function tipoColegioUpdate() {
  loading.value = true;
  const response = await apiQuery.put(
    `api/academicos/tipos-colegios/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);
  loading.value = false;
}

async function saveTipoColegio(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/academicos/tipos-colegios`,
    item.value
  );
  
  searchItem(item.value.descripcion);
  loading.value = false;
}


function newItem() {
  item.value = {
    id: 0,
    company: 0,
    descripcion: "string",
    estado: false,
    publico:false
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Tipos Colegios'"
    :filters="null"
    :headers="headers"
    :items="tipoColegios.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tipoColegios.pageNumber"
    :pageSize="tipoColegios.pageSize"
    :totalPages="tipoColegios.totalPages"
    :totalRecords="tipoColegios.totalRecords"
    @editItem="editItem"
    @update="tipoColegioUpdate()"
    @searchItem="searchItem"
    @newItem="newItem"
    @save="saveTipoColegio"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Publico"
          v-model="item.publico"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
