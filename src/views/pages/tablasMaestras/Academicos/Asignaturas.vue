<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { asignatura } from "@/utils/models/Academico/asignatura";
import AsignaturasService from "@/services/Academicos/AsignaturasService";
import TiposAsignaturasService from "@/services/Academicos/TiposAsignaturasService";
import TiposAsignaturasElectivasService from "@/services/Academicos/TiposAsignaturasElectivasService";
import EscuelasSevices from "@/services/Academicos/EscuelasServices";
import lazyFetch from "@/utils/lazyFetch";
import customFilter from "@/utils/helpers/customFilter";

const headers = [
  { title: "Código", key: "codigo" },
  { title: "Descripción", key: "descripcion" },
  { title: "<PERSON><PERSON><PERSON><PERSON>", key: "creditos" },
  { title: "HT", key: "horasTeoricas" },
  { title: "HP", key: "horasPracticas" },
  { title: "HI", key: "horasInvestigacion" },
  { title: "HNP ", key: "horasNoPresenciales" },
  { title: "Escuela ", key: "escuelaDescripcion" },
  { title: "Tipo Asignatura ", key: "tipoAsignaturaDescripcion" },
  {
    title: "Tipo Asignatura Electiva",
    key: "tipoAsignaturaElectivaDescripcion",
  },
  { title: "Calcula Índice", key: "calculaIndice" },
  { title: "Exonera", key: "exonera" },
  { title: "Paga Hora", key: "pagaHora" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const asignaturas = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const tiposAsignaturas = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});
const tiposAsignaturasElectivas = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const escuelas = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<asignatura>({
  id: 0,
  descripcion: "",
  codigo: "",
  creditos: 0,
  calculaIndice: false,
  escuela: null,
  escuelaDescripcion: "",
  exonera: false,
  horasInvestigacion: 0,
  horasNoPresenciales: 0,
  horasTeoricas: 0,
  horasPracticas: 0,
  tipoAsignatura: 0,
  tipoAsignaturaDescripcion: "",
  tipoAsignaturaElectiva: null,
  tipoAsignaturaElectivaDescripcion: "",
  pagaHora: false,
  estatus: true,
});

let loading = ref(false);
let searchTextEscuela = ref("");
let searchTextTipoAsignatura = ref("");
let searchTextTipoAsignaturaElectiva = ref("");

// Montar
onMounted(async () => {
  await Promise.all([
    getTiposAsignaturas(),
    getTiposAsignaturasElectivas(),
    getEscuelas(),
    searchItem(""),
  ]);
});

watch(searchTextEscuela, (newValue) => {
  getEscuelas(newValue);
});

watch(searchTextTipoAsignatura, (newValue) => {
  getTiposAsignaturas(newValue);
});

watch(searchTextTipoAsignaturaElectiva, (newValue) => {
  getTiposAsignaturasElectivas(newValue);
});

//Metodos
async function editItem(asignatura: asignatura) {
  Object.assign(item.value, asignatura);
  await getEscuelas(asignatura.escuelaDescripcion);
  await getTiposAsignaturas(asignatura.tipoAsignaturaDescripcion);
  await getTiposAsignaturasElectivas(
    asignatura.tipoAsignaturaElectivaDescripcion
  );
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await AsignaturasService.getAsignaturas(
    value,
    pageNumber,
    pageSize
  );
  asignaturas.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function getTiposAsignaturas(value: string = "") {
  loading.value = true;
  const response = await lazyFetch(() =>
    TiposAsignaturasService.getTiposAsignaturas(value)
  );
  tiposAsignaturas.value = response;
  loading.value = false;
}

async function getTiposAsignaturasElectivas(value: string = "") {
  loading.value = true;
  const response = await lazyFetch(() =>
    TiposAsignaturasElectivasService.getTiposAsignaturasElectivas(value)
  );
  tiposAsignaturasElectivas.value = response;
  loading.value = false;
}

async function getEscuelas(value: string = "") {
  loading.value = true;
  const response = await lazyFetch(() => EscuelasSevices.searchItem(value));
  escuelas.value = response;
  loading.value = false;
}

async function updateAsignatura() {
  loading.value = true;
  await apiQuery.put(`api/academicos/asignaturas/${item.value.id}`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}
async function addAsignatura() {
  loading.value = true;
  await apiQuery.post(`api/academicos/asignaturas`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    codigo: "",
    creditos: 0,
    calculaIndice: false,
    escuela: null,
    escuelaDescripcion: "",
    exonera: false,
    horasInvestigacion: 0,
    horasNoPresenciales: 0,
    horasTeoricas: 0,
    horasPracticas: 0,
    tipoAsignatura: 0,
    tipoAsignaturaDescripcion: "",
    tipoAsignaturaElectiva: null,
    tipoAsignaturaElectivaDescripcion: "",
    pagaHora: false,
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :panel="true"
    :loading="loading"
    :title="'Asignaturas'"
    :filters="null"
    :headers="headers"
    :items="asignaturas.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="asignaturas.pageNumber"
    :pageSize="asignaturas.pageSize"
    :totalPages="asignaturas.totalPages"
    :totalRecords="asignaturas.totalRecords"
    @editItem="editItem"
    @update="updateAsignatura()"
    @save="addAsignatura()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Código"
          v-model="item.codigo"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.escuela"
          :items="escuelas.items"
          item-title="descripcion"
          item-value="id"
          label="Escuelas"
          density="compact"
          variant="outlined"
          :loading="loading"
          v-model:search="searchTextEscuela"
          :custom-filter="customFilter"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.escuela = value)"
          @update:search="(value) => (searchTextEscuela = value)"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.tipoAsignatura"
          :items="tiposAsignaturas.items"
          item-title="descripcion"
          item-value="id"
          label="Tipo Asignatura"
          density="compact"
          variant="outlined"
          :loading="loading"
          v-model:search="searchTextTipoAsignatura"
          :custom-filter="customFilter"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.tipoAsignatura = value)"
          @update:search="(value) => (searchTextTipoAsignatura = value)"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.tipoAsignaturaElectiva"
          :items="tiposAsignaturasElectivas.items"
          item-title="descripcion"
          item-value="id"
          label="Tipo Asignatura Electiva"
          density="compact"
          variant="outlined"
          :loading="loading"
          v-model:search="searchTextTipoAsignaturaElectiva"
          :custom-filter="customFilter"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.tipoAsignaturaElectiva = value)"
          @update:search="(value) => (searchTextTipoAsignaturaElectiva = value)"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="2">
        <v-text-field
          label="Créditos"
          v-model="item.creditos"
          type="number"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="2">
        <v-text-field
          label="HP"
          v-model="item.horasPracticas"
          type="number"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="2">
        <v-text-field
          label="HT"
          v-model="item.horasTeoricas"
          type="number"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="2">
        <v-text-field
          label="HI"
          v-model="item.horasInvestigacion"
          type="number"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="2">
        <v-text-field
          label="HNP"
          v-model="item.horasNoPresenciales"
          type="number"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="2">
        <v-checkbox
          color="primary"
          label="Calcula Índice"
          v-model="item.calculaIndice"
          outlined
          dense
        ></v-checkbox
      ></v-col>
      <v-col cols="12" sm="2">
        <v-checkbox
          color="primary"
          label="Exonera"
          v-model="item.exonera"
          outlined
          dense
        ></v-checkbox
      ></v-col>
      <v-col cols="12" sm="2">
        <v-checkbox
          color="primary"
          label="Paga Horas"
          v-model="item.pagaHora"
          outlined
          dense
        ></v-checkbox
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estatus"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
