<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { facultades } from "@/utils/models/Academico/facultadesAcademico";
import facultadesService from "@/services/Academicos/facultadesAcademicoService";
import lazyFetch from "@/utils/lazyFetch";
import customFilter from "@/utils/helpers/customFilter";

let loading = ref(false);
let loadingEdificios = ref(false);

let searchTextEdificio = ref("");

const showDatePickerFechaInicio = ref(false);
const showDatePickerFechaFinal = ref(false);

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Edificio", key: "edificioDescripcion" },
  { title: "Activo", key: "estado" },
  { title: "Acciones", key: "acciones" },
];

const facultad = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const item = ref<facultades>({
  id: 0,
  descripcion: "",
  fechaInicio: null,
  fechaFinal: null,
  edificioId: 0,
  edificioDescripcion: "",
  pisoId: 0,
  telefono: "",
  decano: "",
  cuentaContableGP: "",
  cuentaContableGPDimension: "",
  estado: false,
});

const formateaFechaInicio = computed({
  get() {
    if (!item.value.fechaInicio) return "";
    return item.value.fechaInicio.toISOString().split("T")[0];
  },
  set(value: string) {
    item.value.fechaInicio = value ? new Date(value) : null;
  },
});

const formateaFechaFinal = computed({
  get() {
    if (!item.value.fechaFinal) return "";
    return item.value.fechaFinal.toISOString().split("T")[0];
  },
  set(value: string) {
    item.value.fechaFinal = value ? new Date(value) : null;
  },
});

const edificio = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

onMounted(async () => {
  await searchItem("");
  await getEdificio("");
});

watch(searchTextEdificio, (newValue) => {
  getEdificio(newValue);
});

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await facultadesService.searchItem(
    value,
    pageNumber,
    pageSize
  );
  facultad.value = response;
  loading.value = false;
}

async function getEdificio(value: string = "") {
  loadingEdificios.value = true;
  const response = await lazyFetch(() => facultadesService.getEdificio(value));
  edificio.value = response;
  loadingEdificios.value = false;
}

async function editItem(facultadesEdit: facultades) {
  Object.assign(item.value, {
    ...facultadesEdit,
    fechaInicio: facultadesEdit.fechaInicio
      ? new Date(facultadesEdit.fechaInicio)
      : null,
    fechaFinal: facultadesEdit.fechaFinal
      ? new Date(facultadesEdit.fechaFinal)
      : null,
  });

  searchTextEdificio.value = facultadesEdit.edificioDescripcion || "";
}

async function updateFacultades() {
  try {
    const response = await apiQuery.put(
      `api/academicos/facultades/${item.value.id}`,
      item.value
    );

    if (!response) {
      throw new Error("Network response was not ok");
    }

    searchItem(item.value.descripcion);
    loading.value = false;
    return response;
  } catch (error) {
    console.error("Error fetching documentos:", error);
    throw error;
  }
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    fechaInicio: null,
    fechaFinal: null,
    edificioId: null,
    edificioDescripcion: "",
    pisoId: null,
    telefono: "",
    decano: "",
    cuentaContableGP: "",
    cuentaContableGPDimension: "",
    estado: false,
  };
}

async function saveFacultades() {
  loading.value = true;
  const response = await apiQuery.post(`api/academicos/facultades`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function onDateSelectedFechaInicio(value: any) {
  item.value.fechaInicio = value;
  showDatePickerFechaInicio.value = false;
}

function onDateSelectedFechaFinal(value: any) {
  item.value.fechaFinal = value;
  showDatePickerFechaFinal.value = false;
}
</script>

<template>
  <WFormData
    :loading="loading"
    :title="'Facultades'"
    :filters="null"
    :headers="headers"
    :items="facultad.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="facultad.pageNumber"
    :pageSize="facultad.pageSize"
    :totalPages="facultad.totalPages"
    :totalRecords="facultad.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateFacultades()"
    @newItem="newItem()"
    @save="saveFacultades()"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="12">
        <v-autocomplete
          v-model="item.edificioId"
          v-model:search="searchTextEdificio"
          :items="edificio.items"
          item-title="descripcion"
          item-value="id"
          label="Edificio"
          density="compact"
          variant="outlined"
          :loading="loadingEdificios"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.edificioId = value)"
          @update:search="(value) => (searchTextEdificio = value)"
          :custom-filter="customFilter"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="12">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-dialog v-model="showDatePickerFechaInicio" width="290px">
          <template #activator="{ props }">
            <v-text-field
              v-model="formateaFechaInicio"
              label="Fecha inicio"
              hint="YYYY-MM-DD"
              persistent-hint
              v-bind="props"
            />
          </template>
          <v-date-picker
            show-adjacent-months
            v-model="item.fechaInicio"
            @update:model-value="onDateSelectedFechaInicio"
            color="primary"
            title=""
          />
        </v-dialog>
      </v-col>
      <v-col cols="12" sm="6">
        <v-dialog v-model="showDatePickerFechaFinal" width="290px">
          <template #activator="{ props }">
            <v-text-field
              v-model="formateaFechaFinal"
              label="Fecha final"
              hint="YYYY-MM-DD"
              persistent-hint
              v-bind="props"
            />
          </template>
          <v-date-picker
            show-adjacent-months
            v-model="item.fechaFinal"
            @update:model-value="onDateSelectedFechaFinal"
            color="primary"
            title=""
          />
        </v-dialog>
      </v-col>
      <v-col cols="12" sm="12">
        <v-text-field
          label="Piso"
          v-model="item.pisoId"
          outlined
          dense
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Teléfono"
          v-model="item.telefono"
          outlined
          dense
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Decano"
          v-model="item.decano"
          outlined
          dense
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Cuenta contable GP"
          v-model="item.cuentaContableGP"
          outlined
          dense
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Dimensión cuenta GP"
          v-model="item.cuentaContableGPDimension"
          outlined
          dense
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-switch
          label="Activo"
          color="primary"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </WFormData>
</template>
