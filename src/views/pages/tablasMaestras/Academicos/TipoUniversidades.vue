<script setup lang="ts">
import { onMounted, ref } from "vue";
import type { ITipoUniversidades } from "@/utils/models/Academico/ITipoUniversidades";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import {TipoUniversidadesService} from "@/services/Academicos/TipoUniversidadServices"


const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estado", key: "estado" },
  { title: "Acciones", key: "acciones" },
];

const universidad = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});


const paises = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<ITipoUniversidades>({
  id: 0,
  company: 0,
  descripcion: "",
  estado: false
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});

//Metodos
function editItem(TipoUniversidad: ITipoUniversidades) {
  Object.assign(item.value, TipoUniversidad);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await TipoUniversidadesService.getTipoUniversidades(
    value,
    pageNumber,
    pageSize
  );
  universidad.value = response; // <- ¡Aquí está el cambio importante!
  loading.value = false;
}

async function updateUniversidades() {
  loading.value = true;
  const response = await apiQuery.put(
    `api/academicos/tipos-universidades/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);
  loading.value = false;
}

async function saveTipoUniversidades(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/academicos/tipos-universidades`,
    item.value
  );
  
  searchItem(item.value.descripcion);
  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    company: 0,
    descripcion: "",
    estado: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Tipos Universidades'"
    :filters="null"
    :headers="headers"
    :items="universidad.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="universidad.pageNumber"
    :pageSize="universidad.pageSize"
    :totalPages="universidad.totalPages"
    :totalRecords="universidad.totalRecords"
    @editItem="editItem"
    @update="updateUniversidades()"
    @searchItem="searchItem"
    @newItem="newItem"
    @save="saveTipoUniversidades()"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>

     
      <!-- <v-col cols="12" sm="6">
        <v-text-field
          label="Archivo Digital"
          v-model="item.nombreDigital"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-checkbox
          color="primary"
          label="Detiene Proceso"
          v-model="item.detieneProceso"
          outlined
          dense
        ></v-checkbox
      ></v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.procesoAcademico"
          :items="procesosacademicos.items"
          item-title="descripcion"
          item-value="id"
          label="Proceso Académico"
          density="compact"
          variant="outlined"
        ></v-autocomplete>
      </v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          label="Días de Prórroga"
          v-model="item.diasProrroga"
          type="number"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-checkbox
          label="¿Es Título?"
          v-model="item.esTitulo"
          outlined
          dense
        ></v-checkbox
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col> -->
    </template>
  </wFormData>
</template>
