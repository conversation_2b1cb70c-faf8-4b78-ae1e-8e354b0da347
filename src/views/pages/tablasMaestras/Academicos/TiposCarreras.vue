<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { tipoCarrera } from "@/utils/models/Academico/tipoCarrera";
import TiposCarrerasService from "@/services/Academicos/TipoCarreraService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Es Grado", key: "esGrado" },
  { title: "Es Técnico Superior", key: "esTecnicoSuperior" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const tiposCarreras = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<tipoCarrera>({
  id: 0,
  esTecnicoSuperior: false,
  esGrado: false,
  descripcion: "",
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(tipoCarrera: tipoCarrera) {
  Object.assign(item.value, tipoCarrera);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await TiposCarrerasService.getTiposCarreras(
    value,
    pageNumber,
    pageSize
  );
  tiposCarreras.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateTipoCarrera() {
  loading.value = true;
  await apiQuery.put(
    `api/academicos/tipos-carreras/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addTipoCarrera() {
  loading.value = true;
  await apiQuery.post(`api/academicos/tipos-carreras`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    esGrado: false,
    esTecnicoSuperior: false,
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Tipos Carreras'"
    :filters="null"
    :headers="headers"
    :items="tiposCarreras.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tiposCarreras.pageNumber"
    :pageSize="tiposCarreras.pageSize"
    :totalPages="tiposCarreras.totalPages"
    :totalRecords="tiposCarreras.totalRecords"
    @editItem="editItem"
    @update="updateTipoCarrera()"
    @save="addTipoCarrera()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Es Grado"
          v-model="item.esGrado"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Es Técnico Superior"
          v-model="item.esTecnicoSuperior"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estatus"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
