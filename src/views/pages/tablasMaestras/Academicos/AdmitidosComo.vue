<script setup lang="ts">
import { onMounted, ref } from "vue";
import type { IAdmitidoComo } from "@/utils/models/Academico/IAdmitidoComo";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import { Logger } from "msal";
import {AdmitidoComoService} from "@/services/Academicos/AdmitidoComoService"

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estado", key: "estado" },
  { title: "Acciones", key: "acciones" },
];


const admitidosComo = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<IAdmitidoComo>({
    id: 0,
    company: 1,
    descripcion: "",
    egresado:false,
    graduado: false,
    transferido: false,
    estado:false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});

//Metodos
function editItem(admitidoComo: IAdmitidoComo) {
  Object.assign(item.value, admitidoComo);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await AdmitidoComoService.getAdmitidosComo(value,pageNumber,pageSize);

  admitidosComo.value = response; // <- ¡Aquí está el cambio importante!
  loading.value = false;
}

async function updateAdmitidoComo() {
  loading.value = true;
  const response = await apiQuery.put(
    `api/academicos/admitido-como/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function saveAdmitidoComo() {
  loading.value = true;
  const response = await apiQuery.post(
    `api/academicos/admitido-como`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    company: 1,
    descripcion: "",
    egresado:false,
    graduado: false,
    transferido: false,
    estado:false,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Admitidos Como'"
    :filters="null"
    :headers="headers"
    :items="admitidosComo.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="admitidosComo.pageNumber"
    :pageSize="admitidosComo.pageSize"
    :totalPages="admitidosComo.totalPages"
    :totalRecords="admitidosComo.totalRecords"
    @editItem="editItem"
    @update="updateAdmitidoComo()"
    @searchItem="searchItem"
    @newItem="newItem"
    @save="saveAdmitidoComo()"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Egresado"
          v-model="item.egresado"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Transferido"
          v-model="item.transferido"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Graduado"
          v-model="item.graduado"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
