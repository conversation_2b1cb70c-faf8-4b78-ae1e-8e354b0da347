<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import type { IUniversidad } from "@/utils/models/Academico/IUniversidad";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import { UniversidadesService } from "@/services/Academicos/UniversidadesServices";
import { PaisService } from "@/services/Academicos/PaisService";
import { TipoUniversidadesService } from "@/services/Academicos/TipoUniversidadServices";
import lazyFetch from "@/utils/lazyFetch";
import customFilter from "@/utils/helpers/customFilter";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estado", key: "estado" },
  { title: "Acciones", key: "acciones" },
];

const universidad = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const paises = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const tipoUniversidades = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<IUniversidad>({
  id: 0,
  company: 0,
  descripcion: "string",
  pais: 0,
  paisDescripcion: "",
  tipoUniversidad: 0,
  tipoUniversidadDescripcion: "",
  webSite: "",
  direccion: "",
  telefono: "",
  email: "",
  fundadaPor: "",
  decreto: "",
  ley: "",
  fechaFundada: new Date(),
  leyFecha: new Date(),
  intercambio: new Date(),
  estado: false,
});

let loading = ref(false);
const searchTiposUniversidades = ref("");
const searchPaises = ref("");

// Montar
onMounted(async () => {
  await Promise.all([getPaises(""), getTipoUniversidades(""), searchItem("")]);
});

watch(searchTiposUniversidades, (newValue) => {
  getTipoUniversidades(newValue);
});

watch(searchPaises, (newValue) => {
  getPaises(newValue);
});

//Metodos
async function editItem(universidad: IUniversidad) {
  Object.assign(item.value, universidad);
  await getTipoUniversidades(item.value.tipoUniversidadDescripcion);
  await getPaises(item.value.paisDescripcion);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await UniversidadesService.getUniversidades(
    value,
    pageNumber,
    pageSize
  );
  universidad.value = response; // <- ¡Aquí está el cambio importante!
  loading.value = false;
}

async function updateUniversidades() {
  loading.value = true;
  const response = await apiQuery.put(
    `api/academicos/universidades/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);
  loading.value = false;
}
async function saveUniversidades() {
  loading.value = true;
  const response = await apiQuery.post(
    `api/academicos/universidades`,
    item.value
  );

  searchItem(item.value.descripcion);
  loading.value = false;
}

async function getPaises(value: string) {
  loading.value = true;
  const response = await PaisService.getPaises(value);
  paises.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}

async function getTipoUniversidades(value: string) {
  loading.value = true;
  const response = await lazyFetch(() =>
    TipoUniversidadesService.getTipoUniversidades(value)
  );
  tipoUniversidades.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    company: 0,
    descripcion: "",
    pais: null,
    paisDescripcion: "",
    tipoUniversidad: null,
    tipoUniversidadDescripcion: "",
    webSite: "",
    direccion: "",
    telefono: "",
    email: "",
    fundadaPor: "",
    decreto: "",
    ley: "",
    fechaFundada: new Date(),
    leyFecha: new Date(),
    intercambio: new Date(),
    estado: false,
  };
}
</script>

<template>
  <wFormData
    :panel="true"
    :loading="loading"
    :title="'Universidad'"
    :filters="null"
    :headers="headers"
    :items="universidad.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="universidad.pageNumber"
    :pageSize="universidad.pageSize"
    :totalPages="universidad.totalPages"
    :totalRecords="universidad.totalRecords"
    @editItem="editItem"
    @update="updateUniversidades()"
    @searchItem="searchItem"
    @newItem="newItem"
    @save="saveUniversidades()"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Web Site"
          v-model="item.webSite"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.pais"
          :items="paises.items"
          item-title="descripcion"
          item-value="id"
          label="Pais"
          density="compact"
          variant="outlined"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.pais = value)"
          @update:search="(value) => (searchPaises = value)"
          :custom-filter="customFilter"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.tipoUniversidad"
          :items="tipoUniversidades.items"
          v-model:search="searchTiposUniversidades"
          item-title="descripcion"
          item-value="id"
          label="Tipo Universidad"
          density="compact"
          variant="outlined"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.tipoUniversidad = value)"
          @update:search="(value) => (searchTiposUniversidades = value)"
          :custom-filter="customFilter"
        ></v-autocomplete>
      </v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          label="Direccion"
          v-model="item.direccion"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          label="Telefono"
          v-model="item.telefono"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          label="Email"
          v-model="item.email"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          label="Fundada"
          v-model="item.fundadaPor"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          label="Decreto"
          v-model="item.decreto"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Fecha Fundada"
          v-model="item.fechaFundada"
          outlined
          type="date"
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          label="Numero Ley"
          v-model="item.ley"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          label="Ley Fecha"
          v-model="item.leyFecha"
          outlined
          type="date"
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          label="Ley Fecha"
          v-model="item.intercambio"
          outlined
          type="date"
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
