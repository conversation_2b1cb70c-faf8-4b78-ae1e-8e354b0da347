<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import MotivoDisciplinaService from "@/services/Academicos/MotivoDisciplinaService";
import type { motivoDisciplina } from "@/utils/models/Academico/motivoDisciplina";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Días Separación", key: "diasSeparacion" },
  { title: "Requiere Separación", key: "requiereSeparacion" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const motivosDisciplina = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<motivoDisciplina>({
  id: 0,
  descripcion: "",
  diasSeparacion: 0,
  requiereSeparacion: false,
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});

//Metodos
function editItem(motivoDisciplina: motivoDisciplina) {
  Object.assign(item.value, motivoDisciplina);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await MotivoDisciplinaService.getMotivosDisciplina(
    value,
    pageNumber,
    pageSize
  );
  motivosDisciplina.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateMotivoDisciplina() {
  loading.value = true;
  await apiQuery.put(
    `api/academicos/motivo-disciplina/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addMotivoDisciplina() {
  loading.value = true;
  await apiQuery.post(`api/academicos/motivo-disciplina`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    diasSeparacion: 0,
    requiereSeparacion: false,
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Motivos Disciplina'"
    :filters="null"
    :headers="headers"
    :items="motivosDisciplina.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="motivosDisciplina.pageNumber"
    :pageSize="motivosDisciplina.pageSize"
    :totalPages="motivosDisciplina.totalPages"
    :totalRecords="motivosDisciplina.totalRecords"
    @editItem="editItem"
    @update="updateMotivoDisciplina()"
    @save="addMotivoDisciplina()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Días Separación"
          v-model="item.diasSeparacion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Requiere Separación"
          v-model="item.requiereSeparacion"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estatus"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
