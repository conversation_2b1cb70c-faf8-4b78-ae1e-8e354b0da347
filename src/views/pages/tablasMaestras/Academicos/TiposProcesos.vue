<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { tipoProceso } from "@/utils/models/Academico/tipoProceso";
import TiposProcesosService from "@/services/Academicos/TipoProcesoService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const tiposProcesos = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<tipoProceso>({
  id: 0,
  descripcion: "",
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});

//Metodos
function editItem(tipoProceso: tipoProceso) {
  Object.assign(item.value, tipoProceso);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await TiposProcesosService.getTiposProcesos(
    value,
    pageNumber,
    pageSize
  );
  tiposProcesos.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateProceso() {
  loading.value = true;
  await apiQuery.put(
    `api/academicos/tipos-procesos/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addProceso() {
  loading.value = true;
  await apiQuery.post(`api/academicos/tipos-procesos`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Tipos Procesos'"
    :filters="null"
    :headers="headers"
    :items="tiposProcesos.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tiposProcesos.pageNumber"
    :pageSize="tiposProcesos.pageSize"
    :totalPages="tiposProcesos.totalPages"
    :totalRecords="tiposProcesos.totalRecords"
    @editItem="editItem"
    @update="updateProceso()"
    @save="addProceso()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estatus"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
