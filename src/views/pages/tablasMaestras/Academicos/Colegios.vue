<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import type { IColegio } from "@/utils/models/Academico/IColegio";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import { PaisService } from "@/services/Academicos/PaisService";
import { TipoColegioService } from "@/services/Academicos/TipoColegioServices";
import { ColegioServices } from "@/services/Academicos/ColegiosServices";
import lazyFetch from "@/utils/lazyFetch";
import customFilter from "@/utils/helpers/customFilter";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estado", key: "estado" },
  { title: "Acciones", key: "acciones" },
];

const colegio = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const paises = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const tipoColegio = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<IColegio>({
  id: 0,
  company: 0,
  descripcion: "",
  pais: 0,
  paisDescripcion: "",
  tipoColegio: 0,
  tipoColegioDescripcion: "",
  webSite: "",
  regional: "",
  distrito: "",
  direccion: "",
  director: "",
  telefono: "",
  email: "",
  acreditado: true,
  estado: true,
});

let loading = ref(false);
const searchTipoColegios = ref("");
const searchPaises = ref("");

// Montar
onMounted(async () => {
  await Promise.all([getPaises(""), getTiposColegio(""), searchItem("")]);
});

watch(searchTipoColegios, (newValue) => {
  getTiposColegio(newValue);
});

watch(searchPaises, (newValue) => {
  getPaises(newValue);
});

//Metodos
async function editItem(colegio: IColegio) {
  Object.assign(item.value, colegio);
  await getTiposColegio(item.value.tipoColegioDescripcion);
  await getPaises(item.value.paisDescripcion);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await ColegioServices.getColegio(
    value,
    pageNumber,
    pageSize
  );
  colegio.value = response; // <- ¡Aquí está el cambio importante!
  loading.value = false;
}

async function updateColegio() {
  loading.value = true;
  const response = await apiQuery.put(
    `api/academicos/colegios/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);
  loading.value = false;
}
async function guardarColegio() {
  loading.value = true;
  const response = await apiQuery.post(`api/academicos/colegios`, item.value);
  searchItem(item.value.descripcion);
  loading.value = false;
}

async function getPaises(value: string) {
  loading.value = true;
  const response = await lazyFetch(() => PaisService.getPaises(value));
  paises.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}

async function getTiposColegio(value: string) {
  loading.value = true;
  const response = await lazyFetch(() =>
    TipoColegioService.getTipoColegio(value)
  );
  tipoColegio.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    company: 0,
    descripcion: "",
    pais: null,
    paisDescripcion: "",
    tipoColegio: null,
    tipoColegioDescripcion: "",
    webSite: "",
    regional: "",
    distrito: "",
    direccion: "",
    director: "",
    telefono: "",
    email: "",
    acreditado: true,
    estado: true,
  };
}
</script>

<template>
  <wFormData
    :panel="true"
    :loading="loading"
    :title="'Colegios'"
    :filters="null"
    :headers="headers"
    :items="colegio.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="colegio.pageNumber"
    :pageSize="colegio.pageSize"
    :totalPages="colegio.totalPages"
    :totalRecords="colegio.totalRecords"
    @editItem="editItem"
    @update="updateColegio()"
    @searchItem="searchItem"
    @newItem="newItem"
    @save="guardarColegio()"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Web Site"
          v-model="item.webSite"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.pais"
          v-model:search="searchPaises"
          :items="paises.items"
          item-title="descripcion"
          item-value="id"
          label="Pais"
          density="compact"
          variant="outlined"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.pais = value)"
          @update:search="(value) => (searchPaises = value)"
          :custom-filter="customFilter"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.tipoColegio"
          v-model:search="searchTipoColegios"
          :items="tipoColegio.items"
          item-title="descripcion"
          item-value="id"
          label="Tipo Colegio"
          density="compact"
          variant="outlined"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.tipoColegio = value)"
          @update:search="(value) => (searchTipoColegios = value)"
        ></v-autocomplete>
      </v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          label="Direccion"
          v-model="item.direccion"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          label="Telefono"
          v-model="item.telefono"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          label="Email"
          v-model="item.email"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          label="Director"
          v-model="item.director"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Regional"
          v-model="item.regional"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="6">
        <v-text-field
          label="Distrito"
          v-model="item.distrito"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Acreditado"
          v-model="item.acreditado"
          outlined
          dense
        ></v-switch>
      </v-col>

      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
