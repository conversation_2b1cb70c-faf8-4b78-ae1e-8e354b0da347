<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import { TipoCalificacionPlantillaService } from "@/services/Academicos/TipoCalificacionPlantilla";

import type { TipoCalificacionPlantillaInterfaz } from "@/utils/models/Academico/ITipoCalificacionPlantilla";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estado", key: "estado" },
  { title: "Acciones", key: "acciones" },
];

const tipoCaPla = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<TipoCalificacionPlantillaInterfaz>({
  id: 0,
  company: 0,
  descripcion: "",
  estado: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});

//Metodos
function editItem(TipoCaPlantilla: TipoCalificacionPlantillaInterfaz) {
  Object.assign(item.value, TipoCaPlantilla);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response =
    await TipoCalificacionPlantillaService.GetTipoCalificacionPlantilla(
      value,
      pageNumber,
      pageSize
    );
  tipoCaPla.value = response;
  loading.value = false;
}

async function updateTipoCaPlantilla() {
  loading.value = true;
  const response = await apiQuery.put(
    `api/academicos/tipos-calificaciones-plantillas/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);
  loading.value = false;
}

async function saveTipoCaPlantilla() {
  loading.value = true;
  const response = await apiQuery.post(
    `api/academicos/tipos-calificaciones-plantillas`,
    item.value
  );

  searchItem(item.value.descripcion);
  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    company: 0,
    descripcion: "",
    estado: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Tipos Calificacion Plantilla'"
    :filters="null"
    :headers="headers"
    :items="tipoCaPla.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tipoCaPla.pageNumber"
    :pageSize="tipoCaPla.pageSize"
    :totalPages="tipoCaPla.totalPages"
    :totalRecords="tipoCaPla.totalRecords"
    @editItem="editItem"
    @update="updateTipoCaPlantilla()"
    @searchItem="searchItem"
    @newItem="newItem"
    @save="saveTipoCaPlantilla()"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
