<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { estudianteGradoObtenido } from "@/utils/models/Academico/estudianteGradoObtenido";
import EstudianteGradoObtenidoService from "@/services/Academicos/EstudianteGradoObtenidoService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const estudiantesGradosObtenidos = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<estudianteGradoObtenido>({
  id: 0,
  descripcion: "",
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(estudianteGradoObtenido: estudianteGradoObtenido) {
  Object.assign(item.value, estudianteGradoObtenido);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response =
    await EstudianteGradoObtenidoService.getEstudianteGradosObtenidos(
      value,
      pageNumber,
      pageSize
    );
  estudiantesGradosObtenidos.value = response; // <- ¡Aquí está el cambio importante!
  loading.value = false;
}

async function updateEstudianteGradoObtenido() {
  loading.value = true;
  await apiQuery.put(
    `api/academicos/estudiante-grado-obtenido/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addEstudianteGradoObtenido() {
  loading.value = true;
  await apiQuery.post(`api/academicos/estudiante-grado-obtenido`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Estudiantes Grados Obtenidos'"
    :filters="null"
    :headers="headers"
    :items="estudiantesGradosObtenidos.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="estudiantesGradosObtenidos.pageNumber"
    :pageSize="estudiantesGradosObtenidos.pageSize"
    :totalPages="estudiantesGradosObtenidos.totalPages"
    :totalRecords="estudiantesGradosObtenidos.totalRecords"
    @editItem="editItem"
    @update="updateEstudianteGradoObtenido()"
    @save="addEstudianteGradoObtenido()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-textarea
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-textarea>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estatus"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
