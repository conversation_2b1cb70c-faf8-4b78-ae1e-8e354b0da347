<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { tipoCarrera } from "@/utils/models/Academico/tipoCarrera";
import type { tipoExcepcionSeleccion } from "@/utils/models/Seleccion/tipoExcepcionSeleccion";
import TipoExcepcionSeleccionService from "@/services/Seleccion/TipoExcepcionSeleccionService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Es Beca", key: "esBeca" },
  { title: "Es Financiero", key: "esFinanciero" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const tiposExcepcionesSeleccion = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<tipoExcepcionSeleccion>({
  id: 0,
  descripcion: "",
  esBeca: false,
  esFinanciero: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(tipoCarrera: tipoCarrera) {
  Object.assign(item.value, tipoCarrera);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response =
    await TipoExcepcionSeleccionService.getTiposExcepcionesSeleccion(
      value,
      pageNumber,
      pageSize
    );
  tiposExcepcionesSeleccion.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateTipoExcepcionSeleccion() {
  loading.value = true;
  await apiQuery.put(
    `api/seleccion/tipo-excepcion-seleccion/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addTipoExcepcionSeleccion() {
  loading.value = true;
  await apiQuery.post(`api/seleccion/tipo-excepcion-seleccion`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    esBeca: false,
    esFinanciero: false,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Tipo Excepcion Selección'"
    :filters="null"
    :headers="headers"
    :items="tiposExcepcionesSeleccion.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tiposExcepcionesSeleccion.pageNumber"
    :pageSize="tiposExcepcionesSeleccion.pageSize"
    :totalPages="tiposExcepcionesSeleccion.totalPages"
    :totalRecords="tiposExcepcionesSeleccion.totalRecords"
    @editItem="editItem"
    @save="addTipoExcepcionSeleccion()"
    @update="updateTipoExcepcionSeleccion()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Es Beca"
          v-model="item.esBeca"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Es Financiero"
          v-model="item.esFinanciero"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
