<script setup lang="ts">

import { onMounted, ref } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { descuentosJustificacion } from "@/utils/models/Financiero/descuentosJustificacionesFinancieros";
import descuentosJustificacionesService from "@/services/Financieros/descuentosJustificacionesFinancierosService";

let loading = ref(false);


const headers = [
    {title:"Descripción", key:"descripcion"},
    {title:"Persona contacto", key:"personaContacto"},
    {title:"Teléfono", key:"telefonoContacto"},
    {title:"Email", key:"emailContacto"},
    {title:"Estatus", key:"estado"},
    {title:"Acciones", key:"acciones", sortable: false}
];

const descuentosJustificaciones =  ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});


const item = ref<descuentosJustificacion>({
    id: 0,
    descripcion: "",
    personaContacto: "",
    telefonoContacto: "",
    emailContacto: "",
    estado: false
});

onMounted(async () => {
  await Promise.all([searchItem("")]);
});

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await descuentosJustificacionesService.searchItem(value, pageNumber,pageSize);
    descuentosJustificaciones.value = response;
    loading.value = false;
}

function editItem(descuentoJustificacionEdit: descuentosJustificacion) {
    Object.assign(item.value, descuentoJustificacionEdit)
};

async function updateDescuentosJustificaciones(){
    try {
        const response = await apiQuery.put(
            `api/financieros/descuentos-justificaciones/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.descripcion);
        loading.value = false;
        //return response;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
}

function newItem(){

  item.value = {

    id: 0,
    descripcion: "",
    personaContacto: "",
    telefonoContacto: "",
    emailContacto: "",
    estado: false
}
}

async function saveDescuentosJustificaciones(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/financieros/descuentos-justificaciones`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}
</script>

<template>
    <WFormData
    :loading="loading"
    :title="'Descuentos Justificaciones'"
    :filters="null"
    :headers="headers"
    :items="descuentosJustificaciones.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="descuentosJustificaciones.pageNumber"
    :pageSize="descuentosJustificaciones.pageSize"
    :totalPages="descuentosJustificaciones.totalPages"
    :totalRecords="descuentosJustificaciones.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateDescuentosJustificaciones()"
    @newItem="newItem()"
    @save="saveDescuentosJustificaciones()"
    >
    <template #editItemPanel>
        <v-col cols="12" sm="12">
            <v-text-field
            label="Descripción"
            v-model="item.descripcion"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-text-field
            label="Persona contacto"
            v-model="item.personaContacto"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-text-field
            label="Teléfono"
            v-model="item.telefonoContacto"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="12">
            <v-text-field
            label="Código"
            v-model="item.emailContacto"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-switch
            color="primary"
            label="Estatus"
            v-model="item.estado"
            outlined
            dense
            ></v-switch>
        </v-col>
    </template>
    </WFormData>
</template>