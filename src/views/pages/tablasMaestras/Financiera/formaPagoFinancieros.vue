<script setup lang="ts" >

import { onMounted, ref } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import formaPagoService from "@/services/Financieros/formaPagoFinancierosService";
import type { formaPago } from "@/utils/models/Financiero/formaPagoFinancieros";
import { apiQuery } from "@/utils/helpers/apiQuery";

let loading = ref(false);

const headers = [
    {title:"Descripción", key:"descripcion"},
    {title:"Validar Referencia", key:"referencia"},
    {title:"Estatus", key:"estado"},
    {title:"Acciones", key:"acciones", sortable:false}
];

const formaPagos = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
})

const item = ref<formaPago>({
    id: 0,
    descripcion: "",
    referencia: false,
    estado: false,
    pagoTarjeta: false,
    pagoEfectivo: false,
    isSelect: false,
    descuentoPagoCompleto: false,
    isDescuentoPagoCompleto: false
})

onMounted(async () => {
  await Promise.all([searchItem("")]);
});

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await formaPagoService.searchItem(value ,pageNumber,pageSize);
    formaPagos.value = response;
    loading.value = false;
}

function editItem(formaPagoEdit: formaPago) {
    Object.assign(item.value, formaPagoEdit)
};

async function updateFormaPago(){
    try {
        const response = await apiQuery.put(
            `api/financieros/tipos-formas-pagos/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.descripcion);
        loading.value = false;
        //return response;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
}

function newItem(){

  item.value = {

    id: 0,
    descripcion: "",
    referencia: false,
    estado: false,
    pagoTarjeta: false,
    pagoEfectivo: false,
    isSelect: false,
    descuentoPagoCompleto: false,
    isDescuentoPagoCompleto: false
}
}
async function saveFormaPago(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/financieros/tipos-formas-pagos`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

</script>

<template>
    <WFormData
    :loading="loading"
    :title="'Tipos Formas Pagos'"
    :filters="null"
    :headers="headers"
    :items="formaPagos.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="formaPagos.pageNumber"
    :pageSize="formaPagos.pageSize"
    :totalPages="formaPagos.totalPages"
    :totalRecords="formaPagos.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateFormaPago()"
    @newItem="newItem()"
    @save="saveFormaPago"
    >
    <template #editItemPanel>
        <v-col cols="12" sm="8">
            <v-text-field
            label="Descripción"
            v-model="item.descripcion"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="4">
            <v-text-field
            label="% Descuento Pago Completo"
            v-model="item.descuentoPagoCompleto"
            outlined
            dense
            ></v-text-field>
        </v-col>    
        <v-col cols="12" sm="6">
            <v-switch
            color="primary"
            label="Validar Referencia"
            v-model="item.referencia"
            outlined
            dense
            ></v-switch>
        </v-col>    
        <v-col cols="12" sm="6">
            <v-switch
            color="primary"
            label="Descuento Pago Completo"
            v-model="item.isDescuentoPagoCompleto"
            outlined
            dense
            ></v-switch>
        </v-col>    
        <v-col cols="12" sm="6">
            <v-switch
            color="primary"
            label="Puede Seleccionar"
            v-model="item.isSelect"
            outlined
            dense
            ></v-switch>
        </v-col>    
        <v-col cols="12" sm="6">
            <v-switch
            color="primary"
            label="Pago Completo Efectivo"
            v-model="item.pagoEfectivo"
            outlined
            dense
            ></v-switch>
        </v-col>    
        <v-col cols="12" sm="6">
            <v-switch
            color="primary"
            label="Pago Completo Tarjeta"
            v-model="item.pagoTarjeta"
            outlined
            dense
            ></v-switch>
        </v-col>    
        <v-col cols="12" sm="6">
            <v-switch
            color="primary"
            label="Estado"
            v-model="item.estado"
            outlined
            dense
            ></v-switch>
        </v-col>    
    </template>
    </WFormData>
</template>