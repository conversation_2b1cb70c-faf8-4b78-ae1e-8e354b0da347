<script setup lang="ts">

import { onMounted, ref, watch } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { aulaPagos } from "@/utils/models/Financiero/aulaPagoFinancieros";
import aulaPagoService from "@/services/Financieros/aulaPagoFinancierosServices";
import lazyFetch from "@/utils/lazyFetch";

let loading = ref(false);
let loadingMonedaPago = ref(false);
let loadingSedes = ref(false);
let loadingAulas = ref(false);
let loadingCarreras = ref(false);

let searchTextMonedaPago = ref("");
let searchTextSedes = ref("");
let searchTextCarreras = ref("");
let searchTextAulas = ref("");

const headers = [
    {title:"Aula", key:"aulaDescripcion"},
    {title:"Recinto", key:"sedeDescripcion"},
    {title:"Carrera", key:"carreraDescripcion"},
    {title:"Moneda", key:"monedaPagoDescripcion"},
    {title:"Estatus", key:"estados"},
    {title:"Acciones", key:"acciones", sortable: false},
];

const aulaPago = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});

const item = ref<aulaPagos>({
    id: 0,
    aulaId: 0,
    aulaDescripcion : "",
    sedeId: 0,
    sedeDescripcion: "",
    carreraId: 0,
    carreraDescripcion: "",
    monedaPagoId : 0,
    monedaPagoDescripcion: "",
    monto: 0,
    estados: false
});

const monedaPago = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});

const aula = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});

const carrera = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});

const sedes = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});

onMounted(async () => {
    await searchItem("");
    await getSede("");
    await getAula("");
    await getCarrera("");
    await getMonedaPago("");  
});

watch(searchTextSedes, (newValue) => {
    getSede(newValue);
});

watch(searchTextAulas, (newValue) => {
    getAula(newValue);
});

watch(searchTextCarreras, (newValue) => {
    getCarrera(newValue);
});

watch(searchTextMonedaPago, (newValue) => {
    getMonedaPago(newValue);
});

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await aulaPagoService.searchItem(value ,pageNumber, pageSize);
    aulaPago.value = response;
    loading.value = false;
};

async function getSede(value: string = "") {

loadingSedes.value = true;
const response = await lazyFetch(() => aulaPagoService.getSede(value));
sedes.value = response;
loadingSedes.value = false;

};

async function getAula(value: string = "") {

loadingAulas.value = true;
const response = await lazyFetch(() => aulaPagoService.getAula(value));
aula.value = response;
loadingAulas.value = false;

};

async function getCarrera(value: string = "") {

loadingCarreras.value = true;
const response = await lazyFetch(() => aulaPagoService.getCarrera(value));
carrera.value = response;
loadingCarreras.value = false;

};

async function getMonedaPago(value: string = "") {

loadingMonedaPago.value = true;
const response = await lazyFetch(() => aulaPagoService.getMonedaPago(value));
monedaPago.value = response;
loadingMonedaPago.value = false;

};

async function editItem(aulaPagoEdit: aulaPagos) {
    Object.assign(item.value, aulaPagoEdit);

    searchTextSedes.value = aulaPagoEdit.sedeDescripcion || "";
    searchTextAulas.value = aulaPagoEdit.aulaDescripcion || "";
    searchTextCarreras.value = aulaPagoEdit.carreraDescripcion || "";
    searchTextMonedaPago.value = aulaPagoEdit.monedaPagoDescripcion || "";

};

async function updateAulaPago(){
    try {
        const response = await apiQuery.put(
            `api/financieros/AulaPago/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.monedaPagoDescripcion);
        loading.value = false;
        return response;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
}

function newItem(){

    item.value = {
        id: 0,
        aulaId: 2,
        aulaDescripcion : "",
        sedeId: null,
        sedeDescripcion: "",
        carreraId: null,
        carreraDescripcion: "",
        monedaPagoId : null,
        monedaPagoDescripcion: "",
        monto: null,
        estados: false
    }
}

async function saveAulaPago(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/financieros/AulaPago`,
    item.value
  );
  searchItem(item.value.carreraDescripcion);

  loading.value = false;
}
</script>

<template>
    <WFormData
    :loading="loading"
    :title="'Aula Pagos'"
    :filters="null"
    :headers="headers"
    :items="aulaPago.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="aulaPago.pageNumber"
    :pageSize="aulaPago.pageSize"
    :totalPages="aulaPago.totalPages"
    :totalRecords="aulaPago.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateAulaPago()"
    @newItem="newItem()"
    @save="saveAulaPago()"
    >
    <template #editItemPanel>
        <v-col cols="12" sm="6">
            <v-autocomplete
            v-model="item.sedeId"
            v-model:search="searchTextSedes"
            :items="sedes.items"
            item-title="descripcion"
            item-value="id"
            label="Recinto"
            density="compact"
            variant="outlined"
            :loading="loadingSedes"
            :loading-text="'Cargando...'"
            :no-data-text="'No hay datos'"
            :no-results-text="'No se encontraron resultados'"
            @update:modelValue="(value) => (item.sedeId = value)"
            @update:search="(value) => (searchTextSedes = value)"
            ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6">
            <v-autocomplete
            v-model="item.aulaId"
            v-model:search="searchTextAulas"
            :items="aula.items"
            item-title="descripcion"
            item-value="id"
            label="Aula"
            density="compact"
            variant="outlined"
            :loading="loadingAulas"
            :loading-text="'Cargando...'"
            :no-data-text="'No hay datos'"
            :no-results-text="'No se encontraron resultados'"
            @update:modelValue="(value) => (item.aulaId = value)"
            @update:search="(value) => (searchTextAulas = value)"
            ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="12">
            <v-autocomplete
            v-model="item.carreraId"
            v-model:search="searchTextCarreras"
            :items="carrera.items"
            item-title="descripcion"
            item-value="id"
            label="Carrera"
            density="compact"
            variant="outlined"
            :loading="loadingCarreras"
            :loading-text="'Cargando...'"
            :no-data-text="'No hay datos'"
            :no-results-text="'No se encontraron resultados'"
            @update:modelValue="(value) => (item.carreraId = value)"
            @update:search="(value) => (searchTextCarreras = value)"
            ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="4">
            <v-autocomplete
            v-model="item.monedaPagoId"
            v-model:search="searchTextMonedaPago"
            :items="monedaPago.items"
            item-title="codigo"
            item-value="id"
            label="Moneda"
            density="compact"
            variant="outlined"
            :loading="loadingMonedaPago"
            :loading-text="'Cargando...'"
            :no-data-text="'No hay datos'"
            :no-results-text="'No se encontraron resultados'"
            @update:modelValue="(value) => (item.monedaPagoId = value)"
            @update:search="(value) => (searchTextMonedaPago = value)"
            ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="4">
            <v-text-field
            label="Monto"
            v-model.number="item.monto"
            type="number"
            step="0.01"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="4">
            <v-switch
            label="Estado"
            color="primary"
            v-model="item.estados"
            outlined
            dense
            ></v-switch>
        </v-col>
    </template>
    </WFormData>
</template>