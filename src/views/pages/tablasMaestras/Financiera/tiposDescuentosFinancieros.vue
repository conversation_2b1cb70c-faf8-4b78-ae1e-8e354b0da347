<script setup lang="ts">

import type { PaginResponse } from "@/utils/PaginResponse";
import WFormData from "../../../../components/apps/wFormData.vue";
import { onMounted, ref } from "vue";
import type { tiposDescuentos } from "@/utils/models/Financiero/tiposDescuentoFinancieros";
import tipoDescuentoService from "@/services/Financieros/tiposDescuentoService";
import { apiQuery } from "@/utils/helpers/apiQuery";

let loading = ref(false);

const headers = [
    {title:"Descripción", key:"descripcion"},
    {title:"Beca", key:"beca"},
    {title:"Patrocinio", key:"patrocinio"},
    {title:"Estatus", key:"estado"},
    {title:"Acciones", key:"acciones", sortable:false}
];

const tiposDescuento = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
})

const item = ref<tiposDescuentos>({
    id: 0,
    descripcion: "",
    beca: false,
    patrocinio: false,
    estado: false
})

onMounted(async () => {
  await Promise.all([searchItem("")]);
});

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await tipoDescuentoService.searchItem(value ,pageNumber,pageSize);
    tiposDescuento.value = response;
    loading.value = false;
}

function newItem(){

item.value = {

    id: 0,
    descripcion: "",
    beca: false,
    patrocinio: false,
    estado: false
    }
}

function editItem(tipoDescuentoEdit: tiposDescuentos) {
    Object.assign(item.value, tipoDescuentoEdit)
};

async function saveTipoDescuento(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/financieros/tipos-descuentos`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function updateTipoDescuento(){
    try {
        const response = await apiQuery.put(
            `api/financieros/tipos-descuentos/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.descripcion);
        loading.value = false;
        //return response;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
};



</script>

<template>
    <WFormData
    :loading="loading"
    :title="'Tipos Descuentos'"
    :filters="null"
    :headers="headers"
    :items="tiposDescuento.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tiposDescuento.pageNumber"
    :pageSize="tiposDescuento.pageSize"
    :totalPages="tiposDescuento.totalPages"
    :totalRecords="tiposDescuento.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateTipoDescuento()"
    @newItem="newItem()"
    @save="saveTipoDescuento"
    >
    <template #editItemPanel>
        <v-col cols="12" sm="12">
            <v-text-field
            label="Descripción"
            v-model="item.descripcion"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-switch
            color="primary"
            label="Beca"
            v-model="item.beca"
            outlined
            dense
            ></v-switch>
        </v-col>
        <v-col cols="12" sm="6">
            <v-switch
            color="primary"
            label="Patrocinio"
            v-model="item.patrocinio"
            outlined
            dense
            ></v-switch>
        </v-col>
        <v-col cols="12" sm="6">
            <v-switch
            color="primary"
            label="Estado"
            v-model="item.estado"
            outlined
            dense
            ></v-switch>
        </v-col>
    </template>
    </WFormData>
</template>