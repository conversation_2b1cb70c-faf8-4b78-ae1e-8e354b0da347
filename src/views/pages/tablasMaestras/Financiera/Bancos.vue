<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { bancos } from "@/utils/models/Financiero/BancosFinancieros";
import bancoSevices from "@/services/Financieros/BancofinancierosServices";
import { apiQuery } from "@/utils/helpers/apiQuery";
import lazyFetch from "@/utils/lazyFetch";

let loading = ref(false);
let loadingProcesosAcademicos = ref(false);
let searchText = ref("");

const headers = [
        { title: "Descripción", key: "descripcion" },
        { title: "Siglas", key: "siglas" },
        { title: "Estatus", key: "estado" },
        { title: "Acciones", key: "acciones", sortable: false },
      ];

const banco = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const procesosacademicos = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<bancos>({
    id: 0,
    descripcion: "",
    siglas: "",
    paisId: null,
    descripcionPais: "",
    contacto : "",
    email: "",
    codigo: "",
    digitoVerificacion: "",
    telefono: "",
    estado: false
});

onMounted(async () => {
  await Promise.all([searchItem(""), getProcesosAcademicos("")]);
});

watch(searchText, (newValue) => {
  getProcesosAcademicos(newValue);
});

async function getProcesosAcademicos(value: string = "") {
  loadingProcesosAcademicos.value = true;

  const response = await lazyFetch(() =>
  bancoSevices.getPais(value)
  );

  procesosacademicos.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loadingProcesosAcademicos.value = false;
}


async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await bancoSevices.searchItem(value ,pageNumber,pageSize);
    banco.value = response;
    loading.value = false;
}

async function editItem(bancoEdit: bancos) {
  Object.assign(item.value, bancoEdit);

  searchText.value = bancoEdit.descripcionPais || "";
}

async function updateBanco(){
    try {
        const response = await apiQuery.put(
            `api/financieros/bancos/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.descripcion);
        loading.value = false;
        //return response;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
}

function newItem(){
  searchText.value = "";

  item.value = {

    id: 0,
    descripcion: "",
    siglas: "",
    paisId: null,
    descripcionPais: "",
    contacto : "",
    email: "" ,
    codigo: "",
    digitoVerificacion: "",
    telefono: "",
    estado: false  }
}

async function saveBanco(){
  loading.value = true;
  const response = await apiQuery.post(
`api/financieros/bancos`,
item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}
</script>

<template>
    <wFormData
    :loading="loading"
    :title="'Bancos'"
    :filters="null"
    :headers="headers"
    :items="banco.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="banco.pageNumber"
    :pageSize="banco.pageSize"
    :totalPages="banco.totalPages"
    :totalRecords="banco.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateBanco()"
    @newItem="newItem()"
    @save="saveBanco">

    <template #editItemPanel>
        <v-col cols="12" sm="8">
         <v-text-field
         label="Descripción"
         v-model="item.descripcion">
         </v-text-field>
       </v-col>
       <v-col cols="12" sm="4">
        <v-text-field
        label="Siglas"
        v-model="item.siglas">
        </v-text-field>
       </v-col>
       <v-col cols="12" sm="12">
        <v-autocomplete
          v-model="item.paisId"
          v-model:search="searchText"
          :items="procesosacademicos.items"
          item-title="descripcion"
          item-value="id"
          label="Paises"
          density="compact"
          variant="outlined"
          :loading="loadingProcesosAcademicos"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.paisId = value)"
          @update:search="(value) => (searchText = value)"
        ></v-autocomplete>
        </v-col>
       <v-col cols="12" sm="6">
        <v-text-field
        label="Persona Contacto"
        v-model="item.contacto"></v-text-field>
       </v-col>
       <v-col cols="12" sm="6">
        <v-text-field 
        label="Email Contacto"
        v-model="item.email"></v-text-field>
       </v-col>
       <v-col col="12" sm="4">
        <v-text-field
        label="Codigo Bancario"
        v-model="item.codigo"></v-text-field>
       </v-col>
       <v-col col="12" sm="4">
        <v-text-field
        label="Codigo Bancario"
        v-model="item.codigo"></v-text-field>
       </v-col>
       <v-col col="12" sm="4">
        <v-text-field
        label="Dígito Verificación"
        v-model="item.digitoVerificacion"></v-text-field>
       </v-col>
       <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
    </wFormData>
</template>

