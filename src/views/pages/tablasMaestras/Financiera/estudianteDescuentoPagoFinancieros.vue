<script setup lang="ts">

import {computed, onMounted, ref, watch } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { estudiantesAcademico } from "@/utils/models/Academico/estudiantesAcademico";
import type { estudianteDescuentoPagos } from "@/utils/models/Financiero/estudianteDescuentoPagoFinancieros";
import estudiantePagoService from "@/services/Financieros/estudianteDescuentoPagoFinancierosService";
import WDataTable from "@/components/apps/wDataTable.vue";
import customFilter from "@/utils/helpers/customFilter"
import type { iPerfilEstudiante } from "@/utils/models/Financiero/perfilEstudianteModel";
import wProfileCard from  '@/components/apps/wProfileCard.vue'


const loading = ref(false);
const loadingEstudianteDescuento = ref(false);
const loadingTipoDescuento = ref(false);
const loadingDescuentoJustificacion = ref(false);
const loadingEmpresa = ref(false);
const loadingPerfilEstudiante = ref(false);
const newItems = ref(false);

const showDatePicker = ref(false);

let searchTextTipoDescuento = ref("");
let searchTextDescuentoJustificacion = ref("");
let searchTextEmpresa = ref("");

const headers = [
    {title:"Matrícula", key:"matricula"},
    {title:"Nombres", key:"nombres"},
    {title:"Apellidos", key:"apellidos"},
    {title:"Acciones", key:"acciones", sortable:false},
];

const headersEdit = [
  { title: "Tipo", key: "descuentoDescripcion" },
  { title: "Valor", key: "valorDescuento" },
  { title: "Fecha", key: "fechaVencimiento" },
  { title: "Estatus", key: "estado" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const estudianteDescuentoPago = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const tipoDescuento = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const descuentoJustificacion = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const empresa = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const estudianteInfo = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const item = ref<estudianteDescuentoPagos>({
    id: 0,
    admisionId:  null,
    estuCarreraId:  null,
    monedaId: null,
    monedaDescripcion: "",
    descuentoId : null,
    descuentoDescripcion: "",
    descuentoJustificacionId : null,
    descuentoJustificacionDes: "",
    porcentaje: null,
    porcentajeMonto: null,
    isPermanente: false,
    isAfectaInscripcion: false,
    isAfectaMensualidad: false,
    empresasId: null,
    empresasDescripcion: "",
    estado: false,
    isAfectaServicios : false,
    isAfectaServiciosInscripcion: false,
    isVence : false,
    fechaVencimiento : null,
    isMonto : false,
    isPorcentaje: false,
    carreraDescripcion: "",
    estadoEstudianteDes: "",
    valorDescuento:""
});

const itemUpdate = ref<estudianteDescuentoPagos>({
    id: 0,
    admisionId:  null,
    estuCarreraId:  null,
    monedaId: null,
    monedaDescripcion: "",
    descuentoId : null,
    descuentoDescripcion: "",
    descuentoJustificacionId : null,
    descuentoJustificacionDes: "",
    porcentaje: null,
    porcentajeMonto: null,
    isPermanente: false,
    isAfectaInscripcion: false,
    isAfectaMensualidad: false,
    empresasId: null,
    empresasDescripcion: "",
    estado: false,
    isAfectaServicios : false,
    isAfectaServiciosInscripcion: false,
    isVence : false,
    fechaVencimiento : null,
    isMonto : false,
    isPorcentaje: false,
    carreraDescripcion: "",
    estadoEstudianteDes: "",
    valorDescuento:""
});

const perfilEstudiante = ref<iPerfilEstudiante>({
    id: 0,
    nombre: "",
    apellido: "",
    email: "",
    identificacion: "",
    monedaId: 0,
    monedaDescripcion: "",
    monedaCodigo: "",
    carrId: 0,
    carreraDescrion : "",
    estadoEstudianteId : 0,
    estadoEstudianteDescripcion: ""
});

const datosGenerales = ref<estudiantesAcademico>({
    id: 0,
    matricula: "",
    nombres: "",
    apellidos: "",
    documentoIdentidad: "",
    emailPrincipal: "",
    telefonoPrincipal: "",
    carrera : "",
    foto: ""
});

const estudiantesPlanos = computed(() =>
  estudianteInfo.value.items.map(item => ({
    ...item,
    nombres: item.persona?.nombres ?? '',
    apellidos: item.persona?.apellidos ?? '',
  }))
);

const formattedFecha = computed({
  get() {
    if (!item.value.fechaVencimiento) return '';
    return item.value.fechaVencimiento.toISOString().split('T')[0]; 
  },
  set(value: string) {
    item.value.fechaVencimiento = value ? new Date(value) : null;
  },
});

const formattedFechaUpdate = computed({
  get() {
    if (!itemUpdate.value.fechaVencimiento) return '';
    return itemUpdate.value.fechaVencimiento.toISOString().split('T')[0]; 
  },
  set(value: string) {
    itemUpdate.value.fechaVencimiento = value ? new Date(value) : null;
  },
});

const rules = {
  required: (value: any) => value !== null && value !== undefined && value !== '' || "Requerido"
};

const showPorcentaje = computed(() => {
  return item.value.isPorcentaje || (!item.value.isMonto && !item.value.isPorcentaje);
});

const showMonto = computed(() => {
  return item.value.isMonto;
});

const showPorcentajeUpdate = computed(() => {
  return itemUpdate.value.isPorcentaje || (!itemUpdate.value.isMonto && !itemUpdate.value.isPorcentaje);
});

const showMontoUpdate = computed(() => {
  return itemUpdate.value.isMonto;
});

onMounted(async () => {
  await Promise.all([searchItem("")]);
  await getTipoDescuento("");
  await getDescuentoJustificacion("");
  await getEmpresa("");
});

watch(searchTextTipoDescuento, (newValue) => {
  getTipoDescuento(newValue);
});

watch(searchTextDescuentoJustificacion, (newValue) => {
  getDescuentoJustificacion(newValue);
});

watch(searchTextEmpresa, (newValue) => {
  getEmpresa(newValue);
});

watch(() => item.value.isPorcentaje, (val) => {
  if (val) {
    item.value.isMonto = false;
  }
});

watch(() => item.value.isMonto, (val) => {
  if (val) {
    item.value.isPorcentaje = false;
  }
});

watch(() => itemUpdate.value.isPorcentaje, (val) => {
  if (val) {
    itemUpdate.value.isMonto = false;
  }
});

watch(() => itemUpdate.value.isMonto, (val) => {
  if (val) {
    itemUpdate.value.isPorcentaje = false;
  }
});

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await estudiantePagoService.searchItem(value ,pageNumber, pageSize);
    estudianteInfo.value = response;
    loading.value = false;
};


async function getEstudianteDescuentoById(estudianteId: number, pageNumber: number = 1, pageSize: number = 10 ) {
  loadingEstudianteDescuento.value = true;
  const response = await estudiantePagoService.getEstudianteDescuentoById(estudianteId);
  estudianteDescuentoPago.value = response;
  loadingEstudianteDescuento.value = false;
}

async function getPerfilEstudiante(estudianteId: number){
  loadingPerfilEstudiante.value = true;
  const response = await estudiantePagoService.getPerfilEstudiante(estudianteId);
  perfilEstudiante.value = response;
  loadingPerfilEstudiante.value = false;
}

async function getTipoDescuento(value: string, pageNumber: number = 1, pageSize: number = 10){
  loadingTipoDescuento.value = true;
  const response = await estudiantePagoService.getTipoDescuento(value ,pageNumber, pageSize);
  tipoDescuento.value = response;
  loadingTipoDescuento.value = false;
};

async function getEmpresa(value: string, pageNumber: number = 1, pageSize: number = 10){
  loadingEmpresa.value = true;
  const response = await estudiantePagoService.getEmpresa(value ,pageNumber, pageSize);
  empresa.value = response;
  loadingEmpresa.value = false;
}

async function getDescuentoJustificacion(value: string, pageNumber: number = 1, pageSize: number = 10){
  loadingDescuentoJustificacion.value = true;
  const response = await estudiantePagoService.getJustificacionesDescuento(value ,pageNumber, pageSize);
  descuentoJustificacion.value = response;
  loadingDescuentoJustificacion.value = false;
};

function editItem(datosGeneralesEdit: estudiantesAcademico) {
    Object.assign(datosGenerales.value, datosGeneralesEdit);
    newItem();
    getEstudianteDescuentoById(datosGeneralesEdit.id);
    getPerfilEstudiante(datosGeneralesEdit.id);
};

function editItemNew(estudianteDescuentoPagoEdit: estudianteDescuentoPagos){
  Object.assign(estudianteDescuentoPago.value, estudianteDescuentoPagoEdit);

  itemUpdate.value.estado = estudianteDescuentoPagoEdit.estado;
  itemUpdate.value.isVence = estudianteDescuentoPagoEdit.isVence;
  itemUpdate.value.descuentoDescripcion = estudianteDescuentoPagoEdit.descuentoDescripcion;
  itemUpdate.value.empresasDescripcion = estudianteDescuentoPagoEdit.empresasDescripcion;
  itemUpdate.value.isPermanente = estudianteDescuentoPagoEdit.isPermanente;
  itemUpdate.value.isAfectaInscripcion = estudianteDescuentoPagoEdit.isAfectaInscripcion;
  itemUpdate.value.isAfectaMensualidad = estudianteDescuentoPagoEdit.isAfectaMensualidad;
  itemUpdate.value.isAfectaServicios = estudianteDescuentoPagoEdit.isAfectaServicios;
  itemUpdate.value.isAfectaServiciosInscripcion = estudianteDescuentoPagoEdit.isAfectaServiciosInscripcion;
  itemUpdate.value.descuentoJustificacionDes = estudianteDescuentoPagoEdit.descuentoJustificacionDes;
  itemUpdate.value.isPorcentaje = estudianteDescuentoPagoEdit.isPorcentaje;
  itemUpdate.value.isMonto = estudianteDescuentoPagoEdit.isMonto;
  itemUpdate.value.porcentaje = estudianteDescuentoPagoEdit.porcentaje;
  itemUpdate.value.porcentajeMonto = estudianteDescuentoPagoEdit.porcentajeMonto;
  itemUpdate.value.id = estudianteDescuentoPagoEdit.id;
  itemUpdate.value.monedaId = estudianteDescuentoPagoEdit.monedaId;
  itemUpdate.value.descuentoId = estudianteDescuentoPagoEdit.descuentoId;
  itemUpdate.value.empresasId = estudianteDescuentoPagoEdit.empresasId;
  itemUpdate.value.descuentoJustificacionId = estudianteDescuentoPagoEdit.descuentoJustificacionId;
  itemUpdate.value.estuCarreraId = estudianteDescuentoPagoEdit.estuCarreraId;
  itemUpdate.value.admisionId = estudianteDescuentoPagoEdit.admisionId;


    const fecha = estudianteDescuentoPagoEdit.fechaVencimiento;
  itemUpdate.value.fechaVencimiento = fecha ? new Date(fecha) : null;

};

function newItem(){

  item.value = {
    id: 0,
    admisionId:  null,
    estuCarreraId:  null,
    monedaId: null,
    monedaDescripcion: "",
    descuentoId : null,
    descuentoDescripcion: "",
    descuentoJustificacionId : null,
    descuentoJustificacionDes: "",
    porcentaje: null,
    porcentajeMonto: null,
    isPermanente: false,
    isAfectaInscripcion: false,
    isAfectaMensualidad: false,
    empresasId: null,
    empresasDescripcion: "",
    estado: false,
    isAfectaServicios : false,
    isAfectaServiciosInscripcion: false,
    isVence : false,
    fechaVencimiento : null,
    isMonto : false,
    isPorcentaje: false,
    carreraDescripcion: "",
    estadoEstudianteDes: "",
    valorDescuento: ""
  }
};

async function NewEstudianteDescuentoPago(){
    try {
        loadingEstudianteDescuento.value = true
        item.value.monedaId = perfilEstudiante.value.monedaId;
        const response = await apiQuery.put(
            `api/financieros/estudiante-descuento-pago/${datosGenerales.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(datosGenerales.value.apellidos);
        loadingEstudianteDescuento.value = false;        
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
};

async function UpdateEstudianteDescuentoPago(){
    try {
        loadingEstudianteDescuento.value = true;
        const response = await apiQuery.put(
            `api/financieros/estudiante-descuento-pago/${datosGenerales.value.id}`,
            itemUpdate.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        getEstudianteDescuentoById(datosGenerales.value.id);
        loadingEstudianteDescuento.value = false;
        
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
};

function onDateSelected(value: any) {
  itemUpdate.value.fechaVencimiento = value
  showDatePicker.value = false
}

</script>

<template>
    <WFormData
    :panel="true"
    :loading="loading"
    :title="'Estudiante Descuentos'"
    :filters="null"
    :headers="headers"
    :items="estudiantesPlanos"
    :icon="'mdi-cash-multiple'"
    :text="``"
    :dialogWidth="'100%'"
    :filtervalue="null"
    :pageNumber="estudianteInfo.pageNumber"
    :pageSize="estudianteInfo.pageSize"
    :totalPages="estudianteInfo.totalPages"
    :totalRecords="estudianteInfo.totalRecords"
    @searchItem="searchItem"
    @update="NewEstudianteDescuentoPago()"
    @editItem="editItem"
    :disableInternalNewModal="true">
    <template #editItemPanel>
      <v-row>
        <v-col cols="12" sm="12" md="3">
          <v-card>
            <v-col cols="12" md="3">
              <v-card class="pa-4 text-center">
                <v-col cols="12" sm="12" md="3"> 
                  <wProfileCard
                  :codigo="datosGenerales.matricula"
                  :firstName="datosGenerales.nombres"
                  :lastName="datosGenerales.apellidos"
                  :iconIdentificacion="'mdi-school'"
                  :identificacion="perfilEstudiante.carreraDescrion"
                  email=""
                  :status="perfilEstudiante.estadoEstudianteDescripcion "
                  :activo="true"
                  :profilePicture="datosGenerales.foto"/>
                </v-col>
              </v-card>
            </v-col>
          </v-card>
        </v-col>

        <v-col cols="12" sm="12" md="9">
          <v-card> 
            <v-card-text class="px-4 pt-4"> 
              <v-row>
                <v-col cols="12" sm="3">
                    <v-switch
                    label="Permanente"
                    variant="outlined"
                    density="compact"            
                    color="primary"
                    v-model="item.isPermanente"
                    persistent-hint
                    ></v-switch>
                </v-col>
                <v-col cols="12" sm="3">
                    <v-switch
                    label="Afecta inscripción"
                    variant="outlined"
                    density="compact"            
                    color="primary"
                    v-model="item.isAfectaInscripcion"
                    persistent-hint
                    ></v-switch>
                </v-col>
                <v-col cols="12" sm="3">
                    <v-switch
                    label="Afecta mensualidad"
                    variant="outlined"
                    density="compact"            
                    color="primary"
                    v-model="item.isAfectaMensualidad"
                    persistent-hint
                    ></v-switch>
                </v-col>
                <v-col cols="12" sm="3">
                    <v-switch
                    label="Afecta servicios"
                    variant="outlined"
                    density="compact"            
                    color="primary"
                    v-model="item.isAfectaServicios"
                    persistent-hint
                    ></v-switch>
                </v-col>
                <v-col cols="12" sm="4">
                    <v-switch
                    label="Afecta servicios inscripción"
                    variant="outlined"
                    density="compact"            
                    color="primary"
                    v-model="item.isAfectaServiciosInscripcion"
                    persistent-hint
                    ></v-switch>
                </v-col>
                <v-col cols="12" sm="4">
                    <v-autocomplete
                    label="Tipo de descuento"
                    v-model="item.descuentoId"
                    variant="outlined"
                    density="compact"
                    hint="Tipo de descuento"
                    v-model:search="searchTextTipoDescuento"
                    :items="tipoDescuento.items"
                    item-title="descripcion"
                    item-value="id"
                    :loading="loadingTipoDescuento"
                    :rules="[v => !!v || 'Tipo descuento requerido']"
                    :loading-text="'Cargando...'"
                    :custom-filter="customFilter"
                    :no-data-text="'No hay datos'"
                    :no-results-text="'No se encontraron resultados'"
                    @update:modelValue="(value) => (item.descuentoId = value)"
                    @update:search="(value) => (searchTextTipoDescuento = value)">
                    </v-autocomplete>
                </v-col>
                <v-col cols="12" sm="4">
                    <v-autocomplete
                    label="Patrocinador (Empresa)"
                    v-model="item.empresasId"
                    variant="outlined"
                    density="compact"
                    hint="Empresa"
                    v-model:search="searchTextEmpresa"
                    :items="empresa.items"
                    item-title="descripcion"
                    item-value="id"
                    :loading="loadingEmpresa"
                    :rules="[v => !!v || 'Empresa requerida']"
                    :loading-text="'Cargando...'"
                    :custom-filter="customFilter"
                    :no-data-text="'No hay datos'"
                    :no-results-text="'No se encontraron resultados'"
                    @update:modelValue="(value) => (item.empresasId = value)"
                    @update:search="(value) => (searchTextEmpresa = value)">               
                    ></v-autocomplete>
                </v-col>
                <v-col cols="12" sm="12">
                    <v-autocomplete
                    label="Justificación descuento"
                    v-model="item.descuentoJustificacionId"
                    variant="outlined"
                    density="compact"
                    hint="Justificación descuento"
                    v-model:search="searchTextDescuentoJustificacion"
                    :items="descuentoJustificacion.items"
                    item-title="descripcion"
                    item-value="id"
                    :loading="loadingDescuentoJustificacion"
                    :rules="[v => !!v || 'justificación requerida']"
                    :loading-text="'Cargando...'"
                    :custom-filter="customFilter"
                    :no-data-text="'No hay datos'"
                    :no-results-text="'No se encontraron resultados'"
                    @update:modelValue="(value) => (item.descuentoJustificacionId = value)"
                    @update:search="(value) => (searchTextDescuentoJustificacion = value)">
                    </v-autocomplete>
                </v-col>
                <v-col cols="12" sm="3">
                  <v-switch
                  label="Porcentaje"
                  variant="outlined"
                  density="compact"            
                  color="primary"
                  persistent-hint
                  v-model="item.isPorcentaje"
                  ></v-switch>
                </v-col>
                <v-col cols="12" sm="3">
                  <v-switch 
                  label="Monto"
                  variant="outlined"
                  density="compact"            
                  color="primary"
                  persistent-hint
                  v-model="item.isMonto"
                  ></v-switch>
                </v-col>
                <v-col cols="12" sm="6" v-show="showMonto">
                    <v-text-field
                    label="Valor descuento"
                    variant="outlined"
                    density="compact"
                    :rules="[v => !!v || 'Monto requerido']"
                    v-model="item.porcentajeMonto"            
                    ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6" v-show="showPorcentaje">
                    <v-text-field
                    label="Valor descuento"
                    variant="outlined"
                    density="compact"
                    :rules="[v => !!v || 'Monto requerido']"
                    v-model="item.porcentaje"            
                    ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-switch
                  label="Vence"
                  variant="outlined"
                  density="compact"
                  v-model="item.isVence"            
                  color="primary"
                  persistent-hint
                  ></v-switch>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-dialog v-model="showDatePicker" width="290px">
                      <template #activator="{ props }">
                          <v-text-field
                          v-model="formattedFecha"
                          label="Fecha vencimiento"
                          hint="YYYY-MM-DD"
                          persistent-hint
                          v-bind="props"/>
                      </template>
                      <v-date-picker show-adjacent-months
                      v-model="item.fechaVencimiento"
                      @update:model-value="onDateSelected"
                      color="primary"
                      title=""
                      />
                  </v-dialog>           
                </v-col>
                <v-col cols="12" sm="12">
                  <v-card class="rounded">
                    <v-card-title>
                      <v-toolbar flat color="primary" class="rounded">
                          <v-toolbar-title>Descuentos Pagos Históricos</v-toolbar-title>
                          <v-spacer></v-spacer>
                          <v-tooltip text="Descuentos Pagos Históricos" location="top">
                          </v-tooltip>
                      </v-toolbar>
                      </v-card-title>
                      <WDataTable
                      :dialogWidth="'800px'"
                      :loading="loadingEstudianteDescuento"
                      :title="'Descuentos Pagos Históricos'"
                      :headers="headersEdit"
                      :items="estudianteDescuentoPago.items"
                      :pageNumber="estudianteDescuentoPago.pageNumber"
                      :pageSize="estudianteDescuentoPago.pageSize"
                      :totalPages="estudianteDescuentoPago.totalPages"
                      :totalRecords="estudianteDescuentoPago.totalRecords"
                      @editItem="editItemNew"
                      @update="UpdateEstudianteDescuentoPago()"
                      v-model:add="newItems">
                    <template #editItem>
                      <v-col cols="12" sm="6">
                        <v-text-field
                        label="Tipo descuento"
                        variant="outlined"
                        density="compact"
                        v-model="itemUpdate.descuentoDescripcion"
                        readonly>
                        </v-text-field>
                      </v-col>
                      <v-col cols="12" sm="6">
                        <v-text-field
                        label="Patrocinador (Empresa)"
                        variant="outlined"
                        density="compact"
                        v-model="itemUpdate.empresasDescripcion"
                        readonly>
                        </v-text-field>
                      </v-col>
                      <v-col cols="12" sm="3">
                        <v-switch
                        label="Permanente"
                        variant="outlined"
                        density="compact"
                        color="primary"
                        persistent-hint
                        v-model="itemUpdate.isPermanente">
                        </v-switch>
                      </v-col>
                      <v-col cols="12" sm="3">
                        <v-switch
                        label="Afecta inscripción"
                        variant="outlined"
                        density="compact"
                        color="primary"
                        persistent-hint
                        v-model="itemUpdate.isAfectaInscripcion">
                        </v-switch>
                      </v-col>
                      <v-col cols="12" sm="3">
                        <v-switch
                        label="Afecta mensualidad"
                        variant="outlined"
                        density="compact"
                        color="primary"
                        persistent-hint
                        v-model="itemUpdate.isAfectaMensualidad">
                        </v-switch>
                      </v-col>
                      <v-col cols="12" sm="3">
                        <v-switch
                        label="Afecta servicios"
                        variant="outlined"
                        density="compact"
                        color="primary"
                        persistent-hint
                        v-model="itemUpdate.isAfectaServicios">
                        </v-switch>
                      </v-col>
                      <v-col cols="12" sm="3">
                        <v-switch
                        label="Afecta servicios inscripción"
                        variant="outlined"
                        density="compact"
                        color="primary"
                        persistent-hint
                        v-model="itemUpdate.isAfectaServiciosInscripcion">
                        </v-switch>
                      </v-col>
                      <v-col cols="12" sm="12">
                        <v-text-field
                        label="Justificación descuento"
                        variant="outlined"
                        density="compact"
                        v-model="itemUpdate.descuentoJustificacionDes"
                        readonly>
                        </v-text-field>
                      </v-col>
                      <v-col cols="12" sm="3">
                        <v-switch 
                        label="Por porcentaje"
                        variant="outlined"
                        density="compact"
                        color="primary"
                        persistent-hint
                        v-model="itemUpdate.isPorcentaje">
                      </v-switch>
                      </v-col>
                      <v-col cols="12" sm="3">
                        <v-switch
                        label="Por monto"
                        variant="outlined"
                        density="compact"
                        color="primary"
                        persistent-hint
                        v-model="itemUpdate.isMonto"></v-switch>
                      </v-col>
                      <v-col cols="12" sm="6" v-show="showMontoUpdate">
                          <v-text-field
                          label="Valor descuento"
                          type="number"
                          variant="outlined"
                          density="compact"
                          :rules="[v => !!v || 'Monto requerido']"
                          v-model="itemUpdate.porcentajeMonto"            
                          ></v-text-field>
                      </v-col>
                      <v-col cols="12" sm="6" v-show="showPorcentajeUpdate">
                          <v-text-field
                          label="Valor descuento"
                          variant="outlined"
                          density="compact"
                          type="number"
                          :rules="[v => !!v || 'Porcentaje requerido']"
                          v-model="itemUpdate.porcentaje"            
                          ></v-text-field>
                      </v-col>
                      <v-col cols="12" sm="6">
                        <v-switch 
                        label="Activo"
                        variant="outlined"
                        density="compact"
                        color="primary"
                        persistent-hint
                        v-model="itemUpdate.estado">
                      </v-switch>
                      </v-col>
                      <v-col cols="12" sm="3">
                        <v-switch
                        label="Vence"
                        variant="outlined"
                        density="compact"
                        color="primary"
                        persistent-hint
                        v-model="itemUpdate.isVence">
                        </v-switch>
                      </v-col>
                      <v-col cols="12" sm="3">
                        <v-dialog v-model="showDatePicker" width="290px">
                            <template #activator="{ props }">
                                <v-text-field
                                v-model="formattedFechaUpdate"
                                label="Fecha vencimiento"
                                hint="YYYY-MM-DD"
                                persistent-hint
                                v-bind="props"/>
                            </template>
                            <v-date-picker show-adjacent-months
                            v-model="itemUpdate.fechaVencimiento"
                            @update:model-value="onDateSelected"
                            color="primary"
                            title=""/>
                        </v-dialog>           
                      </v-col>
                    </template>
                    </WDataTable>
                  </v-card>
                </v-col>
                    </v-row>
                    </v-card-text>
                    </v-card>
                    </v-col>
            </v-row>
    </template>
    </WFormData>
</template>