<script setup lang="ts">

import { onMounted, ref, watch } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { cajas } from "@/utils/models/Financiero/cajaFinancieros";
import cajaService from "@/services/Financieros/cajaFinancierosService";
import lazyFetch from "@/utils/lazyFetch";

let loading = ref(false);
let loadingSede = ref(false);
let searchText = ref("");

const headers = [
    {title: "Descripción", key: "descripcion"},
    {title: "Sede", key: "sedeDescripcion"},
    {title: "Estatus", key: "estado"},
    {title: "Acciones", key: "acciones", sortable: false}
]

const caja = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});

const sede = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});

const item = ref<cajas>({
    id: 0,
    descripcion: "",
    sedeId: 0,
    sedeDescripcion: "",
    estado: false
});

onMounted(async () => {
  await Promise.all([searchItem(""), getSedes("")]);
});

watch(searchText, (newValue) => {
    getSedes(newValue);
});

async function getSedes(value: string = "") {
  loadingSede.value = true;

  const response = await lazyFetch(() =>
  cajaService.getSede(value)
  );

  sede.value = response;
  loadingSede.value = false;
}


async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await cajaService.searchItem(value ,pageNumber,pageSize);
    caja.value = response;
    loading.value = false;
}

function editItem(cajaEdit: cajas) {
    Object.assign(item.value, cajaEdit)

    searchText.value = cajaEdit.sedeDescripcion || "";
};

async function updateCaja(){
    try {
        const response = await apiQuery.put(
            `api/financieros/cajas/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.descripcion);
        loading.value = false;
        //return response;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
}

function newItem(){

  item.value = {

    id: 0,
    descripcion: "",
    sedeId: null,
    sedeDescripcion: "",
    estado: false
    }
}

async function saveCaja(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/financieros/cajas`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}
</script>

<template>
    <WFormData
    :loading="loading"
    :title="'Cajas'"
    :filters="null"
    :headers="headers"
    :items="caja.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="caja.pageNumber"
    :pageSize="caja.pageSize"
    :totalPages="caja.totalPages"
    :totalRecords="caja.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateCaja()"
    @newItem="newItem()"
    @save="saveCaja()"
    >
    <template #editItemPanel>
        <v-col cols="12" sm="12">
            <v-text-field
            label="Descripción"
            v-model="item.descripcion"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="12">
        <v-autocomplete
          v-model="item.sedeId"
          v-model:search="searchText"
          :items="sede.items"
          item-title="descripcion"
          item-value="id"
          label="Paises"
          density="compact"
          variant="outlined"
          :loading="loadingSede"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.sedeId = value)"
          @update:search="(value) => (searchText = value)"
        ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="12">
            <v-switch
            color="primary"
            label="Estatus"
            v-model="item.estado"
            outlined
            dense
            ></v-switch>
        </v-col>
    </template>
    </WFormData>
</template>