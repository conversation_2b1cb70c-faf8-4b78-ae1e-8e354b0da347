<script setup lang="ts">

import { onMounted, ref } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { tipoCajeros } from "@/utils/models/Financiero/tipoCajeroFinancieros";
import tipoCajeroService from "@/services/Financieros/tipoCajeroFinancierosService";
import { apiQuery } from "@/utils/helpers/apiQuery";

let loading = ref(false);

const headers = [
    {title:"Descripción", key:"descripcion"},
    {title:"Cobra", key:"cobra"},
    {title:"Estatus", key:"estado"},
    {title:"Acciones", key:"acciones", sortable:false}
];

const tipoCajero = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});

const item = ref<tipoCajeros>({
    id: 0,
    descripcion: "",
    cobra: false,
    estado: false
});

onMounted(async () => {
  await Promise.all([searchItem("")]);
});

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await tipoCajeroService.searchItem(value ,pageNumber,pageSize);
    tipoCajero.value = response;
    loading.value = false;
}

function newItem(){

item.value = {

    id: 0,
    descripcion: "",
    cobra: false,
    estado: false
    }
}

function editItem(tipoCajeroEdit: tipoCajeros) {
    Object.assign(item.value, tipoCajeroEdit)
};

async function saveTipoCajero(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/financieros/tipos-cajeros`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function updateTipoCajero(){
    try {
        const response = await apiQuery.put(
            `api/financieros/tipos-cajeros/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.descripcion);
        loading.value = false;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
};

</script>

<template>
    <WFormData
    :loading="loading"
    :title="'Tipos Cajeros'"
    :filters="null"
    :headers="headers"
    :items="tipoCajero.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tipoCajero.pageNumber"
    :pageSize="tipoCajero.pageSize"
    :totalPages="tipoCajero.totalPages"
    :totalRecords="tipoCajero.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateTipoCajero()"
    @newItem="newItem()"
    @save="saveTipoCajero"
    >
    <template #editItemPanel>
        <v-col cols="12" sm="12">
            <v-text-field
            label="Descripción"
            v-model="item.descripcion"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-switch
            color="primary"
            label="Cobra"
            v-model="item.cobra"
            outlined
            dense></v-switch>
        </v-col>
        <v-col cols="12" sm="6">
            <v-switch
            color="primary"
            label="Estatus"
            v-model="item.estado"
            outlined
            dense></v-switch>
        </v-col>
    </template>
    </WFormData>
</template>