<script setup lang="ts">

import {computed, onMounted, ref } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { ncf } from "@/utils/models/Financiero/ncfFinancieros";
import ncfService from "@/services/Financieros/ncfFinancierosService";

let loading = ref(false);
const showDatePicker = ref(false);

const headers = [
        { title: "Tipo Ncf", key: "tipoNcfDes" },
        { title: "Recinto", key: "sedeSDes" },
        { title: "Referencia", key: "referencia" },
        { title: "Secuencia", key: "secuencia" },
        { title: "Estado", key: "estado" },
        { title: "Acciones", key: "acciones", sortable: false },
      ];

const ncfs = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<ncf>({
    id: 0,
    tipoNcfId: 0,
    tipoNcfDes: "",
    sedeId: 0,
    sedeSDes: "",
    referencia: "",
    secuencia: 0,
    secuenciaDesde: 0,
    secuenciaHasta: 0,
    fechaVencimiento: null,
    estado: false
});

const tipoNcf = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const recinto = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const formattedFecha = computed({
  get() {
    if (!item.value.fechaVencimiento) return '';
    return item.value.fechaVencimiento.toISOString().split('T')[0]; 
  },
  set(value: string) {
    item.value.fechaVencimiento = value ? new Date(value) : null;
  },
});

const selectedYear = computed(() => {
  return item.value.fechaVencimiento
    ? new Date(item.value.fechaVencimiento).getFullYear()
    : null;
});

onMounted(async () => {
  await Promise.all([searchItem(""), getTipoNcf(),getRecinto()]);
});

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await ncfService.searchItem(value ,pageNumber, pageSize);
    ncfs.value = response;
    loading.value = false;
};

function editItem(ncfEdit: ncf) {
    Object.assign(item.value, {
    ...ncfEdit,
    fechaVencimiento: ncfEdit.fechaVencimiento
      ? new Date(ncfEdit.fechaVencimiento)
      : null, 
  });
};

async function updateTipoNcf(){
    try {
        const response = await apiQuery.put(
            `api/financieros/Ncf/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.referencia);
        loading.value = false;
        //return response;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
};

async function getRecinto(){
    loading.value = true;
    const response = await apiQuery.get(
        `api/planta-fisica/sedes`
    );
    recinto.value = response;
    loading.value = false;
};

async function getTipoNcf(){
    loading.value = true;
    const response = await apiQuery.get(
        `api/financieros/tipos-ncf/GetTiposNcf`
    );
    tipoNcf.value = response;
    loading.value = false;
};

function newItem(){
  item.value = {

    id: 0,
    tipoNcfId: 0,
    tipoNcfDes: "",
    sedeId: 0,
    sedeSDes: "",
    referencia: "",
    secuencia: 0,
    secuenciaDesde: 0,
    secuenciaHasta: 0,
    fechaVencimiento: null,
    estado: false  
}
};

async function saveNcf(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/financieros/Ncf`,
    item.value
  );
  searchItem(item.value.tipoNcfDes);

  loading.value = false;
};

function onDateSelected(value: any) {
  item.value.fechaVencimiento = value
  showDatePicker.value = false
}

</script>

<template>
    <WFormData
    :loading="loading"
    :title="'NCF'"
    :filters="null"
    :headers="headers"
    :items="ncfs.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="ncfs.pageNumber"
    :pageSize="ncfs.pageSize"
    :totalPages="ncfs.totalPages"
    :totalRecords="ncfs.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateTipoNcf()"
    @newItem="newItem()"
    @save="saveNcf"
    >
    <template #editItemPanel>
        <v-col cols="12" sm="6">
            <v-autocomplete
            label="Recinto"
            v-model="item.sedeSDes"
            :items="recinto.items"
            item-title="descripcion"
            item-value="id"
            density="compact"
            variant="outlined"
            ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6">
            <v-autocomplete
            label="Tipo Ncf"
            v-model="item.tipoNcfDes"
            :items="tipoNcf.items"
            item-title="descripcion"
            item-value="id"
            density="compact"
            variant="outlined"
            ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="4">
            <v-text-field
            label="Referencia"
            v-model="item.referencia"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="4">
            <v-text-field
            label="Secuencia"
            v-model="item.secuencia"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="4">
            <v-text-field
            label="Secuencia desde"
            v-model="item.secuenciaDesde"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="4">
            <v-text-field
            label="Secuencia hasta"
            v-model="item.secuenciaHasta"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="4">
            <v-dialog v-model="showDatePicker" width="290px">
                <template #activator="{ props }">
                    <v-text-field
                    v-model="formattedFecha"
                    label="Fecha vencimiento"
                    hint="YYYY-MM-DD"
                    persistent-hint
                    v-bind="props"/>
                </template>
                <v-date-picker show-adjacent-months
                v-model="item.fechaVencimiento"
                @update:model-value="onDateSelected"
                color="primary"
                title=""
                />
            </v-dialog>           
        </v-col>
        <v-col cols="12" sm="4">
            <v-switch
            color="primary"
            label="Estado"
            v-model="item.estado"
            outlined
            dense
            ></v-switch>
        </v-col>
    </template>       
    </WFormData>
</template>