<script setup lang="ts">

import { onMounted, ref, watch } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { profesorCategoriaPago } from "@/utils/models/Financiero/profesorCategoriaPagoFinancieros";
import categoriaProfesorService from "@/services/Financieros/profesorCategoriaPagoFinancierosService";
import lazyFetch from "@/utils/lazyFetch";

let loading = ref(false);
let loadingMonedaPago = ref(false);
const searchText = ref("");

const headers = [
    {title:"Descripción", key:"descripcion"},
    {title:"Moneda", key:"monedaCodigo"},
    {title:"Estatus", key:"estado"},
    {title:"Acciones", key:"acciones", sortable: false},
];

const categoriaProfesor = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});

const monedaPago = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});

const item = ref<profesorCategoriaPago>({
    id: 0,
    descripcion: "",
    monedaId: 0,
    monedaCodigo: "",
    valor: 0,
    puntuacionInicial: 0,
    puntuacionFinal: 0,
    categoriza: false,
    estado: false,
    referencia: ""
});

onMounted(async () => {
    await Promise.all([searchItem(""),  getMonedaPago("")]);  
});

watch(searchText, (newValue) => {
    getMonedaPago(newValue);
});

async function getMonedaPago(value: string = "") {

  loadingMonedaPago.value = true;
  const response = await lazyFetch(() => categoriaProfesorService.monedaPago(value));
  monedaPago.value = response;
  loadingMonedaPago.value = false;

}

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await categoriaProfesorService.searchItem(value ,pageNumber,pageSize);
    categoriaProfesor.value = response;
    loading.value = false;
}

async function editItem(profesorCategoriaEdit: profesorCategoriaPago) {
    Object.assign(item.value, profesorCategoriaEdit);

    searchText.value = profesorCategoriaEdit.monedaCodigo || "";
};

async function updateProfesorCategoriaPago(){
    try {
        const response = await apiQuery.put(
            `api/financieros/profesor-categoria-pago/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.descripcion);
        loading.value = false;
        return response;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
}

function newItem(){
    searchText.value = "";

    item.value = {
    id: 0,
    descripcion: "",
    monedaId: null,
    monedaCodigo: "",
    valor: null,
    puntuacionInicial: null,
    puntuacionFinal: null,
    categoriza: false,
    estado: false,
    referencia: ""
    }
}

async function saveProfesorCategoriaPago(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/financieros/profesor-categoria-pago`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}
</script>

<template>
    <WFormData
    :loading="loading"
    :title="'Categoría Pago Profesor'"
    :filters="null"
    :headers="headers"
    :items="categoriaProfesor.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="categoriaProfesor.pageNumber"
    :pageSize="categoriaProfesor.pageSize"
    :totalPages="categoriaProfesor.totalPages"
    :totalRecords="categoriaProfesor.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateProfesorCategoriaPago()"
    @newItem="newItem()"
    @save="saveProfesorCategoriaPago()"
    >
    <template #editItemPanel>
        <v-col cols="12" sm="6">
            <v-text-field
            label="Descripción"
            v-model="item.descripcion"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-text-field
            label="Código de Referencia"
            v-model="item.referencia"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-autocomplete
            v-model="item.monedaId"
            v-model:search="searchText"
            :items="monedaPago.items"
            item-title="codigo"
            item-value="id"
            label="Paises"
            density="compact"
            variant="outlined"
            :loading="loadingMonedaPago"
            :loading-text="'Cargando...'"
            :no-data-text="'No hay datos'"
            :no-results-text="'No se encontraron resultados'"
            @update:modelValue="(value) => (item.monedaId = value)"
            @update:search="(value) => (searchText = value)"
            ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6">
            <v-text-field
            label="Valor"
            v-model="item.valor"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-text-field
            label="Rango puntuación inicial"
            type="number"
            v-model="item.puntuacionInicial"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-text-field
            label="Rango puntuación final"
            type="number"
            v-model="item.puntuacionFinal"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-switch
            label="Categoriza?"
            color="primary"
            v-model="item.categoriza"
            outlined
            dense
            ></v-switch>
        </v-col>
        <v-col cols="12" sm="6">
            <v-switch
            label="Activo"
            color="primary"
            v-model="item.estado"
            outlined
            dense
            ></v-switch>
        </v-col>
    </template>
    </WFormData>
</template>