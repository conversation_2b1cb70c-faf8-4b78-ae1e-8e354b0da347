<script setup lang="ts">

import { onMounted, ref } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { servicioContableGrupo } from "@/utils/models/Financiero/servicioContableGrupoFinancieros";
import servicioContableService from "@/services/Financieros/servicioContableGrupoFinancierosService";``

let loading = ref(false);

const headers = [
    {title: "Descripción", key: "descripcion"},
    {title: "Estatus", key: "estado"},
    {title: "Acciones", key: "acciones", sortable: false},
];

const servicioContable = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});

const item = ref<servicioContableGrupo>({
    id: 0,
    descripcion: "",
    estado: false
});

onMounted(async () => {
  await Promise.all([searchItem("")]);
});

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await servicioContableService.searchItem(value ,pageNumber,pageSize);
    servicioContable.value = response;
    loading.value = false;
}

function editItem(servicioContableEdit: servicioContableGrupo) {
    Object.assign(item.value, servicioContableEdit)

};

async function updateServicioContable(){
    try {
        const response = await apiQuery.put(
            `api/financieros/servicio-contable-grupo/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.descripcion);
        loading.value = false;
        //return response;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
}

function newItem(){

  item.value = {

    id: 0,
    descripcion: "",
    estado: false
    }
}

async function saveServicioContable(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/financieros/servicio-contable-grupo`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}
</script>

<template>
    <WFormData
    :loading="loading"
    :title="'Servicios Contables Grupos'"
    :filters="null"
    :headers="headers"
    :items="servicioContable.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="servicioContable.pageNumber"
    :pageSize="servicioContable.pageSize"
    :totalPages="servicioContable.totalPages"
    :totalRecords="servicioContable.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateServicioContable()"
    @newItem="newItem()"
    @save="saveServicioContable()"
    >
    <template #editItemPanel>
        <v-col cols="12" sm="12">
            <v-text-field
            label="Descripción"
            v-model="item.descripcion"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-switch
            label="Estado"
            color="primary"
            v-model="item.estado"
            outlined
            dense
            ></v-switch>
        </v-col>
    </template>
    </WFormData>
</template>