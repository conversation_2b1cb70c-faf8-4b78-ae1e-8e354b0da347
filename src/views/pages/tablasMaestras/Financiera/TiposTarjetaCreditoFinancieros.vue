<script setup lang="ts">

import { onMounted, ref } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { tiposTarjetaCredito } from "@/utils/models/Financiero/tiposTarjetaCreditoFinancieros";
import tiposTarjetaCreditoService from "@/services/Financieros/tiposTarjetaCreditoFinancierosService";

let loading = ref(false);
const searchText = ref("");

const headers = [
    {title:"Descripción", key:"descripcion"},
    {title:"Estatus", key:"estado"},
    {title:"Acciones", key:"acciones", sortable: false},
];

const tiposTarjetaCreditos = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
})

const item = ref<tiposTarjetaCredito>({
    id: 0,
    descripcion: "",
    estado: false
})

onMounted(async () => {
  await Promise.all([searchItem("")]);
});

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await tiposTarjetaCreditoService.searchItem(value ,pageNumber,pageSize);
    tiposTarjetaCreditos.value = response;
    loading.value = false;
}

function editItem(tiposTarjetaCredito: tiposTarjetaCredito) {
    Object.assign(item.value, tiposTarjetaCredito)
};

async function updateTiposTarjetaCredito(){
    try {
        const response = await apiQuery.put(
            `api/financieros/tipos-tarjetas-creditos/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.descripcion);
        loading.value = false;
        //return response;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
}

function newItem() {
  searchText.value = "";

  item.value = {
    id: 0,
    descripcion: "",
    estado: false
  };
}

async function saveTipoTarjetaCredito() {
  loading.value = true;
  const response = await apiQuery.post(
    `api/financieros/tipos-tarjetas-creditos`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

</script>

<template>
    <WFormData
    :loading="loading"
    :title="'Tipos Tarjeta Credito'"
    :filters="null"
    :headers="headers"
    :items="tiposTarjetaCreditos.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tiposTarjetaCreditos.pageNumber"
    :pageSize="tiposTarjetaCreditos.pageSize"
    :totalPages="tiposTarjetaCreditos.totalPages"
    :totalRecords="tiposTarjetaCreditos.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateTiposTarjetaCredito()"
    @newItem="newItem"
    @save="saveTipoTarjetaCredito">

    <template #editItemPanel>
        <v-col col="12" sm="12">
            <v-text-field
            label="Descripción"
            v-model="item.descripcion"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="12">
            <v-switch
            color="primary"
            label="Estatus"
            v-model="item.estado"
            outlined
            dense
            ></v-switch>
        </v-col>
    </template>
    </WFormData>
</template>