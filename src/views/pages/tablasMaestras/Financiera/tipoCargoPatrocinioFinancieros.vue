<script setup lang="ts">

import { onMounted, ref } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { tipoCargoPatrocinio } from "@/utils/models/Financiero/tipoCargoPatrocinioFinancieros";
import tipoPatrocinioService from "@/services/Financieros/tipoCargoPatrociniofinancierosService";

let loading = ref(false);

const headers = [
    {title: "Descripción", key: "descripcion"},
    {title: "Estatus", key: "estado"},
    {title: "Acciones", key: "acciones", sortable: false},
];

const tipoPatrocinio = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});

const item = ref<tipoCargoPatrocinio>({
    id: 0,
    descripcion: "",
    estado: false
});

onMounted(async () => {
  await Promise.all([searchItem("")]);
});

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await tipoPatrocinioService.searchItem(value ,pageNumber,pageSize);
    tipoPatrocinio.value = response;
    loading.value = false;
}

function editItem(tipoPatrocinioEdit: tipoCargoPatrocinio) {
    Object.assign(item.value, tipoPatrocinioEdit)

};

async function updateTipoCargoPatrocinio(){
    try {
        const response = await apiQuery.put(
            `api/financieros/tipo-cargo-patrocinio/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.descripcion);
        loading.value = false;
        //return response;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
}

function newItem(){

  item.value = {

    id: 0,
    descripcion: "",
    estado: false
    }
}

async function saveTipoCargoPatrocinio(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/financieros/tipo-cargo-patrocinio`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

</script>

<template>
    <WFormData
    :loading="loading"
    :title="'Tipos Cargos Patrocinios'"
    :filters="null"
    :headers="headers"
    :items="tipoPatrocinio.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tipoPatrocinio.pageNumber"
    :pageSize="tipoPatrocinio.pageSize"
    :totalPages="tipoPatrocinio.totalPages"
    :totalRecords="tipoPatrocinio.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateTipoCargoPatrocinio()"
    @newItem="newItem()"
    @save="saveTipoCargoPatrocinio()"
    >
    <template #editItemPanel>
        <v-col cols="12" sm="12">
            <v-text-field
            label="Descripción"
            v-model="item.descripcion"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-switch
            label="Estado"
            color="primary"
            v-model="item.estado"
            outlined
            dense
            ></v-switch>
        </v-col>
    </template>
    </WFormData>
</template>