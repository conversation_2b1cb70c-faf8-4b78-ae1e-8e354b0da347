<script setup lang="ts">

import { onMounted, ref } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { monedaPagos } from "@/utils/models/Financiero/monedaPagofinancieros";
import { apiQuery } from "@/utils/helpers/apiQuery";
import monedaPagoService from "@/services/Financieros/monedaPagoFinancierosService";

let loading = ref(false);

const headers = [
    {title: "Código", key: "codigo"},
    {title: "Descripción", key: "descripcion"},
    {title: "Moneda por defecto", key: "default"},
    {title: "Estatus", key: "estado"},
    {title: "Acciones", key: "acciones", sortable: false},
];

const monedaPago = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});

const item = ref<monedaPagos>({
    id : 0,
    codigo: "",
    descripcion : "",
    tasa: 0.00,
    default: false,
    estado: false
});

onMounted(async () => {
  await Promise.all([searchItem("")]);
});

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await monedaPagoService.searchItem(value ,pageNumber,pageSize);
    monedaPago.value = response;
    loading.value = false;
}

function editItem(monedaPagoEdit: monedaPagos) {
    Object.assign(item.value, monedaPagoEdit)
};

async function updateMonedaPago(){
    try {
        const response = await apiQuery.put(
            `api/financieros/monedas-pagos/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.descripcion);
        loading.value = false;
        //return response;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
}

function newItem(){

  item.value = {

    id : 0,
    codigo: "",
    descripcion: "",
    tasa: 0.00,
    default: false,
    estado: false
}
}

async function saveMonedaPago(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/financieros/monedas-pagos`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

</script>

<template>
    <WFormData
    :loading="loading"
    :title="'Monedas Pagos'"
    :filters="null"
    :headers="headers"
    :items="monedaPago.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="monedaPago.pageNumber"
    :pageSize="monedaPago.pageSize"
    :totalPages="monedaPago.totalPages"
    :totalRecords="monedaPago.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateMonedaPago()"
    @newItem="newItem()"
    @save="saveMonedaPago"
    >
    <template #editItemPanel>
        <v-col cols="12" sm="4">
            <v-text-field
            label="Código"
            v-model="item.codigo"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="8">
            <v-text-field
            label="Descripción"
            v-model="item.descripcion"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="12">
            <v-text-field
            label="Tasa"
            v-model="item.tasa"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-switch
            color="primary"
            label="Default"
            v-model="item.default"
            outlined
            dense
            ></v-switch>
        </v-col>
        <v-col cols="12" sm="6">
            <v-switch
            color="primary"
            label="Estatus"
            v-model="item.estado"
            outlined
            dense
            ></v-switch>
        </v-col>

    </template>
    </WFormData>
</template>