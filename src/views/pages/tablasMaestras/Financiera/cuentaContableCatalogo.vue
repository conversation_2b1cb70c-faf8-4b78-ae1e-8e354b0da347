<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { cuentaContableCatologo } from "@/utils/models/Financiero/cuentaContableCatalogo";
import cuentaContableCatologoService from "@/services/Financieros/cuentaContableCatalogoFinancierosService";
import lazyFetch from "@/utils/lazyFetch";

let loading = ref(false);
let loadingTipoCuentaContable = ref(false);
let loadingCuentaContableOrigen = ref(false);
let searchText = ref("");
let searchText2 = ref("");

const headers = [
  { title: "Código", key: "codigo" },
  { title: "Descripción", key: "descripcion" },
  { title: "Tipo cuenta", key: "tipoCuentaDescripcion" },
  { title: "Origen cuenta", key: "cuentaOrigenDescripcion" },
  { title: "Estatus", key: "estado" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const cuentaCatalogo = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const item = ref<cuentaContableCatologo>({
  id: 0,
  descripcion: "",
  codigo: "",
  tipoCuentaId: 0,
  tipoCuentaDescripcion: "",
  cuentaOrigenId: 0,
  cuentaOrigenDescripcion: "",
  aceptaMovimientos: false,
  cuentaCobrarPesos: false,
  cuentaCobrarDolares: false,
  cuentaPrimaDolares: false,
  cuentaCobrarPrimaDolares: false,
  cuentaGp: false,
  estado: false,
});

const tipoCuentaContable = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const cuentaOrigen = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

onMounted(async () => {
  await searchItem("");
  await getCuentaContableOrigen("");
  await getTipoCuentaContable("");
});

watch(searchText, (newValue) => {
  getTipoCuentaContable(newValue);
});

watch(searchText2, (newValue) => {
  getCuentaContableOrigen(newValue);
});

async function getTipoCuentaContable(value: string = "") {
  loadingTipoCuentaContable.value = true;

  const response = await lazyFetch(() =>
    cuentaContableCatologoService.getTipoCuentaContable(value)
  );

  tipoCuentaContable.value = response;
  loadingTipoCuentaContable.value = false;
}

async function getCuentaContableOrigen(value: string = "") {
  loadingCuentaContableOrigen.value = true;

  const response = await lazyFetch(() =>
    cuentaContableCatologoService.getCuentaContableOrigen(value)
  );

  cuentaOrigen.value = response;
  loadingCuentaContableOrigen.value = false;
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await cuentaContableCatologoService.searchItem(
    value,
    pageNumber,
    pageSize
  );
  cuentaCatalogo.value = response;
  loading.value = false;
}

function editItem(cuentaCatalogoEdit: cuentaContableCatologo) {
  Object.assign(item.value, cuentaCatalogoEdit);

  searchText.value = cuentaCatalogoEdit.tipoCuentaDescripcion || "";
  searchText2.value = cuentaCatalogoEdit.cuentaOrigenDescripcion || "";
}

async function updateCuentaContableCatalogo() {
  try {
    const response = await apiQuery.put(
      `api/financieros/catalogo-cuenta/${item.value.id}`,
      item.value
    );

    if (!response) {
      throw new Error("Network response was not ok");
    }

    searchItem(item.value.descripcion);
    loading.value = false;
    //return response;
  } catch (error) {
    console.error("Error fetching documentos:", error);
    throw error;
  }
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    codigo: "",
    tipoCuentaId: null,
    tipoCuentaDescripcion: "",
    cuentaOrigenId: null,
    cuentaOrigenDescripcion: "",
    aceptaMovimientos: false,
    cuentaCobrarPesos: false,
    cuentaCobrarDolares: false,
    cuentaPrimaDolares: false,
    cuentaCobrarPrimaDolares: false,
    cuentaGp: false,
    estado: false,
  };
}

async function saveCuentaContableCatalogo() {
  loading.value = true;
  const response = await apiQuery.post(
    `api/financieros/catalogo-cuenta`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}
</script>

<template>
  <WFormData
    :panel="true"
    :loading="loading"
    :title="'Catálogo de Cuentas'"
    :filters="null"
    :headers="headers"
    :items="cuentaCatalogo.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="cuentaCatalogo.pageNumber"
    :pageSize="cuentaCatalogo.pageSize"
    :totalPages="cuentaCatalogo.totalPages"
    :totalRecords="cuentaCatalogo.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateCuentaContableCatalogo()"
    @newItem="newItem()"
    @save="saveCuentaContableCatalogo()"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="4">
        <v-text-field
          label="Código"
          v-model="item.codigo"
          outlined
          dense
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="8">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          label="Tipo cuenta contable"
          v-model="item.tipoCuentaId"
          v-model:search="searchText"
          :items="tipoCuentaContable.items"
          item-title="descripcion"
          item-value="id"
          density="compact"
          variant="outlined"
          :loading="loadingTipoCuentaContable"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.tipoCuentaId = value)"
          @update:search="(value) => (searchText = value)"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          label="Origen cuenta contable"
          v-model="item.cuentaOrigenId"
          v-model:search="searchText2"
          :items="cuentaOrigen.items"
          item-title="descripcion"
          item-value="id"
          density="compact"
          variant="outlined"
          :loading="loadingCuentaContableOrigen"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.cuentaOrigenId = value)"
          @update:search="(value) => (searchText2 = value)"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="4">
        <v-switch
          label="Cuenta CXC estudiante pesos"
          color="primary"
          v-model="item.cuentaCobrarPesos"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="4">
        <v-switch
          label="Cuenta CXC estudiante dolares"
          color="primary"
          v-model="item.cuentaCobrarDolares"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="4">
        <v-switch
          label="Cuenta prima dolares"
          color="primary"
          v-model="item.cuentaPrimaDolares"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="4">
        <v-switch
          label="Cuenta CXC prima dolares"
          color="primary"
          v-model="item.cuentaCobrarPrimaDolares"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="4">
        <v-switch
          label="Acepta movimientos"
          color="primary"
          v-model="item.aceptaMovimientos"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="4">
        <v-switch
          label="Cuenta GP"
          color="primary"
          v-model="item.cuentaGp"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="4">
        <v-switch
          label="Estado"
          color="primary"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </WFormData>
</template>
