<script setup lang="ts">

import {computed, onMounted, ref, toRaw, watch } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import planPagoService from "@/services/Financieros/planPagoFinancierosServices";
import type { planesPagos } from "@/utils/models/Financiero/planPagoFinancieros";
import wDataTable from "../../../../components/apps/wDataTable.vue";
import lazyFetch from "@/utils/lazyFetch";
import type { IplanPagoDet } from "@/utils/models/Financiero/planPagoDetFinancieros";
import formatearMontoService from "@/utils/models/Financiero/formatearMontoService";

let loading = ref(false);
let loadingDet = ref(false);
let loadingRecinto = ref(false);
let loadingMonedaPago = ref(false);
let loadingCarrera = ref(false);
let loadingCargarPlanPagoDet = ref(false);

const planPagoDetNuevoModal = ref(false);

let searchTextCarrera = ref("");
let searchTextMonedaPago = ref("");
let searchTextRecinto = ref("");

const nuevoItem = ref(false);
const disableInternalNewModal = ref(true);
const showModalStepGuide = ref(false);
const showModalCompleto = ref(false);
const showSelectorModal = ref(false);

const mostrarAdvertenciaGuide = ref(false);
const mostrarAdvertenciaCompleto = ref(false);
let mensajeAdvertencia = ref("");

let step = ref(1);


const stepItems = [
  { title: 'Datos Generales' },
  { title: 'Costos' },
  { title: 'Recargos' },
  {title:  'Cuotas'}
];

const mostrarAlertaCuotas = computed(() => {
  return (
    !item.value.montoTotal || item.value.montoTotal <= 0 ||
    !item.value.cantidadPago || item.value.cantidadPago <= 0
  );
});

const formStep1 = ref()
const isValidStep1 = ref(false);
const formStep2 = ref()
const isValidStep2 = ref(false);
const formStep3 = ref()
const isValidStep3 = ref(false);
const formComplete = ref();
const isValidComplete = ref(false);

const headers = [
    {title:"Descripción", key:"descripcion"},
    {title:"Carrera", key:"carrAlias"},
    {title:"Moneda pago", key:"monPagoCodigo"},
    {title:"Recinto", key:"recintoDescripcion"},
    {title:"Estatus", key:"estado"},
    {title:"Acciones", key:"acciones", sortable:false},

];

const headersDet = [
    {title:"Secuencia cuota", key:"secuenciaDet"},
    {title:"Monto", key:"montoConCodigo"},
    {title:"Dia vencimiento", key:"diasVencimientosDet"},
    {title:"Acciones", key:"acciones", sortable:false},
];

const planPago = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const planPagoDet = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const planPagoDetNuevo = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const carrera = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const sede = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const monedaPago = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const item = ref<planesPagos>({
    id: 0, 
    descripcion: "",
    recintoId: null,
    recintoDescripcion: "",
    carrId:  null,
    carrAlias: "",
    carrDescripcion: "",
    monPagoId: null,
    monPagoDescripcion: "",
    monPagoCodigo: "",
    montoTotal:  null,
    porcentajeDescuento : null,
    recargoInscripcion:  null,
    cantidadPago: null,
    montoInscripcion: null,
    montoReinscripcion: null,
    isPorCredito: false,
    isDefault: false,
    costoCredito:  null, //opcional
    creditoMinimo:  null, // opcional
    isRecargoPorciento: false,
    montoRecargoCuota:  null,
    montoInscripcionMonografico:  null, //opcional
    montoInscripcionTutorias:  null, //opcional
    costoAdmision:  null, //opcional
    isCalculaRecargoInscripcionPorciento: false, 
    estado: false,
    planPagoDet: []
});


const rules = {
  required: (value: any) => value !== null && value !== undefined && value !== '' || "Requerido"
};

const customFilter = (
  itemTitle: string,
  queryText: string): boolean => {
  const normalize = (text: string): string =>
    text
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "");

  return normalize(itemTitle).includes(normalize(queryText));
};


onMounted(async () => {
  await searchItem("");
  await getCarrera();
  await getSede();
  await getMonedaPago();
});

watch(searchTextCarrera, (newValue) => {
  getCarrera(newValue);
});

watch(searchTextRecinto, (newValue) => {
  getSede(newValue);
});

watch(searchTextMonedaPago, (newValue) => {
  getMonedaPago(newValue);
});
 

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await planPagoService.searchItem(value ,pageNumber, pageSize);
    planPago.value = response;
    loading.value = false;
};

async function getPlanPagoDet(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loadingDet.value = true;
    const response = await planPagoService.getPlanPagoDet(value ,pageNumber, pageSize);
    planPagoDet.value = response;
    loading.value = false;
}

async function getCarrera(value: string = ""){
  loadingCarrera.value = true;
  const response = await lazyFetch(() =>
  planPagoService.getCarrera(value));
  carrera.value = response;
  loadingCarrera.value = false;
}

async function getSede(value: string = ""){
  loadingRecinto.value = true;
  const response = await lazyFetch(() =>
  planPagoService.getSede(value));
  sede.value = response;
  loadingRecinto.value = false;
}

async function getMonedaPago(value: string = ""){
  loadingMonedaPago.value = true;
  const response = await lazyFetch(() => planPagoService.getMonedaPago(value));

  monedaPago.value = response;
  loadingMonedaPago.value = false;
}

function editItem(planPagoEdit: planesPagos) {
    Object.assign(item.value, planPagoEdit);
    mostrarAdvertenciaCompleto.value = false;

    searchTextCarrera.value = planPagoEdit.carrDescripcion || "";
    searchTextRecinto.value =  planPagoEdit.recintoDescripcion || "";
    searchTextMonedaPago.value =  planPagoEdit.monPagoDescripcion || "";
    getPlanPagoDet(planPagoEdit.id.toString());
};

function esNumeroValido(valor: any): boolean {
  return valor !== null && valor !== "" && !isNaN(valor) && Number(valor) >= 0;
}

function validarCampos() {
  if (
    item.value.descripcion === "" || item.value.carrId === null ||
    item.value.recintoId === null || item.value.monPagoId === null ||
    item.value.montoTotal === null || item.value.montoTotal < 0 ||  
    item.value.cantidadPago === null || item.value.cantidadPago < 0 ||
    item.value.porcentajeDescuento === null || item.value.montoInscripcion === null ||
    item.value.montoInscripcion < 0 || item.value.montoReinscripcion === null ||
    item.value.montoReinscripcion < 0 || item.value.recargoInscripcion === null ||
    item.value.recargoInscripcion < 0 || item.value.montoRecargoCuota === null ||
    item.value.montoRecargoCuota < 0
  ) {
    mostrarAdvertenciaCompleto.value = true
    mensajeAdvertencia.value = "Debe completar todos los campos requeridos.";
    return false;
  }

  if (
  !esNumeroValido(item.value.montoTotal) ||
  !esNumeroValido(item.value.cantidadPago) ||
  !esNumeroValido(item.value.montoInscripcion) ||
  !esNumeroValido(item.value.montoReinscripcion) ||
  !esNumeroValido(item.value.recargoInscripcion) ||
  !esNumeroValido(item.value.montoRecargoCuota)
) {
    mostrarAdvertenciaCompleto.value = true
    mensajeAdvertencia.value = "Debe completar todos los campos requeridos.";
    return false;    
}

  if(planPagoDetNuevo.value.items.length <= 0){
    if(planPagoDet.value.items.length <= 0){
    mostrarAdvertenciaCompleto.value = true
    mensajeAdvertencia.value = "Debe cargar las cuotas en la tabla.";
    return false;
    }
  }

  if(item.value.montoTotal <= 0 && item.value.cantidadPago <= 0) {
    mostrarAdvertenciaCompleto.value = true
    mensajeAdvertencia.value = "El monto total y cantidad de cuotas debe ser mayor a 0";
    return false;  }

  const sumaMontos = planPagoDetNuevo.value.items.reduce((acc, cuota) => {
    return acc + (parseFloat(cuota.montosDet) || 0);
  }, 0);

  const cantidadCuotas = planPagoDetNuevo.value.items.length;
  const cuotas = Number(item.value.cantidadPago);

  const totalEsperado = Number(item.value.montoTotal.toString());
  const sumaRedondeada = Number(sumaMontos);

  if (sumaRedondeada !== totalEsperado) {

      const sumaMontos2 = planPagoDet.value.items.reduce((acc, cuota) => {
      return acc + (parseFloat(cuota.montosDet) || 0);}, 0);

      const sumaRedondeada2 = Number(sumaMontos2);
      if(sumaRedondeada2 !== totalEsperado){
          mostrarAdvertenciaCompleto.value = true
          mensajeAdvertencia.value = "El monto total de las cuotas no coincide con la del campo monto total.";
          return false;
      }

  }

  if (cantidadCuotas !== cuotas) {
    const cantidadCuotas2 = planPagoDet.value.items.length;
    const cuotas2 = Number(item.value.cantidadPago);

    if(cantidadCuotas2 !== cuotas2){
    mostrarAdvertenciaCompleto.value = true
    mensajeAdvertencia.value = "La cantidad de cuotas no coincide";
    return false;
    }
  }

  mostrarAdvertenciaCompleto.value = false;
  return true;
};

function validarCamposNuevo() {
  if (
    item.value.descripcion === "" || item.value.carrId === null ||
    item.value.recintoId === null || item.value.monPagoId === null ||
    item.value.montoTotal === null || item.value.montoTotal < 0 ||  
    item.value.cantidadPago === null || item.value.cantidadPago < 0 ||
    item.value.porcentajeDescuento === null || item.value.montoInscripcion === null ||
    item.value.montoInscripcion < 0 || item.value.montoReinscripcion === null ||
    item.value.montoReinscripcion < 0 || item.value.recargoInscripcion === null ||
    item.value.recargoInscripcion < 0 || item.value.montoRecargoCuota === null ||
    item.value.montoRecargoCuota < 0
  ) {
    mostrarAdvertenciaGuide.value = true
    mensajeAdvertencia.value = "Debe completar todos los campos requeridos.";
    return false;
  }

  if (
  !esNumeroValido(item.value.montoTotal) ||
  !esNumeroValido(item.value.cantidadPago) ||
  !esNumeroValido(item.value.montoInscripcion) ||
  !esNumeroValido(item.value.montoReinscripcion) ||
  !esNumeroValido(item.value.recargoInscripcion) ||
  !esNumeroValido(item.value.montoRecargoCuota)
) {
    mostrarAdvertenciaGuide.value = true
    mensajeAdvertencia.value = "Debe completar todos los campos requeridos.";
    return false;
}
  if(planPagoDetNuevo.value.items.length <= 0){
    mostrarAdvertenciaGuide.value = true
    mensajeAdvertencia.value = "Debe cargar las cuotas en la tabla.";
    return false;
  }

  if (item.value.montoTotal <= 0 && item.value.cantidadPago <= 0) {
    mostrarAdvertenciaGuide.value = true
    mensajeAdvertencia.value = "El monto total y cantidad de cuotas debe ser mayor a 0";    
    return false;
  }

  const sumaMontos = planPagoDetNuevo.value.items.reduce((acc, cuota) => {
    return acc + (parseFloat(cuota.montosDet) || 0);
  }, 0);

  const cantidadCuotas = planPagoDetNuevo.value.items.length;
  const cuotas = Number(item.value.cantidadPago);

  const totalEsperado = Number(item.value.montoTotal.toString());
  const sumaRedondeada = Number(sumaMontos);


  if (sumaRedondeada !== totalEsperado) {
    mostrarAdvertenciaGuide.value = true
    mensajeAdvertencia.value = "El monto total de las cuotas no coincide con la del campo monto total.";    
    return false;
  }
  
  if (cantidadCuotas !== cuotas) {
    mostrarAdvertenciaGuide.value = true
    mensajeAdvertencia.value = "La cantidad de cuotas no coincide";
    return false;
  }

  mostrarAdvertenciaGuide.value = false;
  return true;
};

async function updatePlaPago(){
  if(validarCampos()){
    const esValido = await validarCamposRequeridosComplete();
      if (esValido){
    try{
      item.value.planPagoDet = toRaw(planPagoDet.value.items);

      const response = await apiQuery.put(
        `api/financieros/planes-pagos/${item.value.id}`,
        item.value
      );

      if(!response){
        throw new Error("Network response was not ok");
      }

      searchItem(item.value.descripcion);
      loading.value = false;

    }catch(error){
      console.error("Error fetching documentos:", error);
      throw error;
    }
  }
}
};

async function goTo(){}

function onNewItem(){
  showSelectorModal.value = true;
};

function openModalCompleto() {
  mostrarAdvertenciaCompleto.value = false;
  showSelectorModal.value = false;
  showModalCompleto.value = true;
  newItem();
  newItemDet();
}

function newItem(){
  
  item.value = {
    id: 0, 
    descripcion: "",
    recintoId: null,
    recintoDescripcion: "",
    carrId:  null,
    carrAlias: "",
    carrDescripcion: "",
    monPagoId: null,
    monPagoDescripcion: "",
    monPagoCodigo: "",
    montoTotal:  null,
    porcentajeDescuento : null,
    recargoInscripcion:  null,
    cantidadPago: null,
    montoInscripcion: null,
    montoReinscripcion: null,
    isPorCredito: false,
    isDefault: false,
    costoCredito:  null, //opcional
    creditoMinimo:  null, // opcional
    isRecargoPorciento: false,
    montoRecargoCuota:  null,
    montoInscripcionMonografico:  null, //opcional
    montoInscripcionTutorias:  null, //opcional
    costoAdmision:  null, //opcional
    isCalculaRecargoInscripcionPorciento: false, 
    estado: true,
    planPagoDet: []
    };

    itemPlanPagoDet.value = {
    id: 0,
    planPago: 0,
    montosDet: null,
    montoConCodigo: "",
    diasVencimientosDet: null,
    secuenciaDet: null,
    agregar: false,
    modificado: false,
    eliminar: false
    }

    planPagoDet.value.items = [];
    planPagoDetNuevo.value.items = [];
};

function newItemDet(){
  itemPlanPagoDet.value = {
    id: 0,
    planPago: 0,
    montosDet: 0,
    montoConCodigo: "",
    diasVencimientosDet: 0,
    secuenciaDet: 0,
    agregar: false,
    modificado: false,
    eliminar: false
  }

  planPagoDet.value.items = [];
  planPagoDetNuevo.value.items = [];
}

function openModalStepGuide() {
  showSelectorModal.value = false;
  showModalStepGuide.value = true;

  step.value = 1;

  newItem();
  newItemDet();
};

async function btnSiguiente() {

  switch (step.value) {
    case 1:
      const result = await formStep1.value?.validate()
      if (result?.valid) {
        step.value++
      }
      break
    case 2:
      const result2 = await formStep2.value?.validate()
      if (result2?.valid) {
        step.value++
      }
      break
    case 3:
      const result3 = await formStep3.value?.validate()
      if (result3?.valid) {
        step.value++
        mostrarAdvertenciaGuide.value = false;
      }
      break 
 
  }
};

//Plan Pago Detalle Editar

const itemPlanPagoDet = ref<IplanPagoDet>({
    id: 0,
    planPago: 0,
    montosDet: 0,
    montoConCodigo: "",
    diasVencimientosDet: 0,
    secuenciaDet: 0,
    agregar: false,
    modificado: false,
    eliminar: false
}); 

function openModalPlaPagoDet(){
  planPagoDetNuevoModal.value = true;

  itemPlanPagoDet.value = {
    id: 0,
    planPago: 0,
    montosDet: null,
    montoConCodigo: "",
    diasVencimientosDet: null,
    secuenciaDet: null,
    agregar: false,
    modificado: false,
    eliminar: false
  }
};

async function agregarNuevoPlanPagoDet() {
  loadingCargarPlanPagoDet.value = true;

  if (
    item.value.montoTotal == 0 || 
    item.value.montoTotal == null ||
    item.value.cantidadPago == 0 ||
    item.value.cantidadPago == null
  ) {
    loadingCargarPlanPagoDet.value = false;
    return false;
  }

  const montoTotal = Number(item.value.montoTotal);
  const cantidadCuotas = Number(item.value.cantidadPago);
  const montoBase = parseFloat((montoTotal / cantidadCuotas).toFixed(2));

    let monedaCodigo = ref("");
  const monPagoId = item.value?.monPagoId;

  if (monPagoId != null && monPagoId > 0) {
    monedaCodigo.value = await getMonedaPagoCodigo(monPagoId)
  };

  let tempIdCounter = -1;

  const cuotasExistentes = new Map<number, any>();
  for (const cuota of planPagoDet.value.items) {
    cuotasExistentes.set(cuota.secuenciaDet, cuota);
  }

  const nuevasCuotas: any[] = [];
  let totalAsignado = 0;

  for (let i = 1; i <= cantidadCuotas; i++) {
    let monto = montoBase;

    // Si es la última cuota, ajustamos para cerrar la diferencia
    if (i === cantidadCuotas) {
      const diferencia = parseFloat((montoTotal - totalAsignado).toFixed(2));
      monto = diferencia;
    } else {
      totalAsignado += montoBase;
    }

    const cuotaExistente = cuotasExistentes.get(i);
    if (cuotaExistente) {
      nuevasCuotas.push({
        ...cuotaExistente,
        montosDet: monto,
        montoConCodigo: `${monedaCodigo.value} ${monto.toFixed(2)}`,        
        diasVencimientosDet: 30, 
        modificado: true
      });
    } else {
      nuevasCuotas.push({
        id: tempIdCounter--,
        planPago: item.value.id || 0,
        montosDet: monto,
        montoConCodigo: `${monedaCodigo.value} ${monto.toFixed(2)}`,        
        diasVencimientosDet: 30,
        secuenciaDet: i,
        agregar: true,
        modificado: false,
        eliminar: false
      });
    }
  }

  planPagoDet.value.items = nuevasCuotas;

  itemPlanPagoDet.value = {
    id: 0,
    planPago: 0,
    montosDet: null,
    montoConCodigo: "",
    diasVencimientosDet: null,
    secuenciaDet: null,
    agregar: false,
    modificado: false,
    eliminar: false
  };

  loadingCargarPlanPagoDet.value = false;
}

function editItemPlanPagoDet(planPagoDetEdit: IplanPagoDet){
  loadingDet.value = false;
  Object.assign(itemPlanPagoDet.value, planPagoDetEdit);
};

async function updatePlanPagoDet() {

  const index = planPagoDet.value.items.findIndex(det => det.id === itemPlanPagoDet.value.id);
  const updatedItem = { ...itemPlanPagoDet.value };
  updatedItem.modificado = true;

  const monPagoId = item.value.monPagoId;
  if (monPagoId != null && monPagoId > 0) {
      const monto = updatedItem.montosDet?.toString() ?? "0";
      const moneda = await getMonedaPagoCodigo(monPagoId);
      updatedItem.montoConCodigo = `${moneda} ${formatearMontoService.formatearMonto(monto)}`;  
    } else {
    updatedItem.montoConCodigo = "";
  }

  planPagoDet.value.items[index] = updatedItem;
};

//Plan PAgo Detalle Nuevo Guiada

function editItemPlanPagoDetNuevo(planPagoDetNuevoEdit: IplanPagoDet){
  loadingDet.value = false;
  Object.assign(itemPlanPagoDet.value, planPagoDetNuevoEdit);
};

async function getMonedaPagoCodigo(id: number): Promise<string> {
  const response = await planPagoService.getMonedaPagoCodigo(id);
  return response.codigo || ""; 
}

async function agregarNuevoPlanPagoDetNuevo() {
  loadingCargarPlanPagoDet.value = true;
  if (
    item.value.montoTotal == 0 || 
    item.value.montoTotal == null ||
    item.value.cantidadPago == 0 ||
    item.value.cantidadPago == null
  ) {
    loadingCargarPlanPagoDet.value = false;
    mensajeAdvertencia.value = "Para cargar las cuotas, primero debes completar los campos obligatorios: Monto total y Cantidad de Cuotas.";
    mostrarAdvertenciaGuide.value = true
    return false;
  }

  const montoTotal = Number(item.value.montoTotal);
  const cantidadCuotas = Number(item.value.cantidadPago);
  const montoBase = parseFloat((montoTotal / cantidadCuotas).toFixed(2));

  let monedaCodigo = ref("");
  const monPagoId = item.value?.monPagoId;

  if (monPagoId != null && monPagoId > 0) {
    monedaCodigo.value = await getMonedaPagoCodigo(monPagoId);
  }

  let tempIdCounter = -1;

  const cuotasExistentes = new Map<number, any>();
  for (const cuota of planPagoDetNuevo.value.items) {
    cuotasExistentes.set(cuota.secuenciaDet, cuota);
  }

  const nuevasCuotas: any[] = [];
  let totalAsignado = 0;

  for (let i = 1; i <= cantidadCuotas; i++) {
    let monto = montoBase;

    // Si es la última cuota, ajustamos para cerrar la diferencia
    if (i === cantidadCuotas) {
      const diferencia = parseFloat((montoTotal - totalAsignado).toFixed(2));
      monto = diferencia;
    } else {
      totalAsignado += montoBase;
    }

    const cuotaExistente = cuotasExistentes.get(i);
    if (cuotaExistente) {
      nuevasCuotas.push({
        ...cuotaExistente,
          montosDet: monto,
          montoConCodigo: `${monedaCodigo.value} ${formatearMontoService.formatearMonto(monto.toFixed(2))}`,        
          diasVencimientosDet: 30, 
        modificado: true
      });
    } else {
      nuevasCuotas.push({
        id: tempIdCounter--,
        planPago: item.value.id || 0,
        montosDet: monto,
          montoConCodigo: `${monedaCodigo.value} ${formatearMontoService.formatearMonto(monto.toFixed(2))}`,        
        diasVencimientosDet: 30,
        secuenciaDet: i,
        agregar: true,
        modificado: false,
        eliminar: false
      });
    }
  }

  planPagoDetNuevo.value.items = nuevasCuotas;

  itemPlanPagoDet.value = {
    id: 0,
    planPago: 0,
    montosDet: null,
    montoConCodigo: "",
    diasVencimientosDet: null,
    secuenciaDet: null,
    agregar: false,
    modificado: false,
    eliminar: false
  };
  loadingCargarPlanPagoDet.value = false;
  showModalStepGuide.value = true;
};

async function agregarNuevoPlanPagoDetNuevoComplete() {
  loadingCargarPlanPagoDet.value = true;
  if (
    item.value.montoTotal == 0 || 
    item.value.montoTotal == null ||
    item.value.cantidadPago == 0 ||
    item.value.cantidadPago == null
  ) {
    loadingCargarPlanPagoDet.value = false;
    mensajeAdvertencia.value = "Para cargar las cuotas, primero debes completar los campos obligatorios: Monto total y Cantidad de Cuotas.";
    mostrarAdvertenciaCompleto.value = true
    return false;
  }

  const montoTotal = Number(item.value.montoTotal);
  const cantidadCuotas = Number(item.value.cantidadPago);
  const montoBase = parseFloat((montoTotal / cantidadCuotas).toFixed(2));

  let monedaCodigo = ref("");
  const monPagoId = item.value?.monPagoId;

  if (monPagoId != null && monPagoId > 0) {
    monedaCodigo.value = await getMonedaPagoCodigo(monPagoId);
  }

  let tempIdCounter = -1;

  const cuotasExistentes = new Map<number, any>();
  for (const cuota of planPagoDetNuevo.value.items) {
    cuotasExistentes.set(cuota.secuenciaDet, cuota);
  }

  const nuevasCuotas: any[] = [];
  let totalAsignado = 0;

  for (let i = 1; i <= cantidadCuotas; i++) {
    let monto = montoBase;

    if (i === cantidadCuotas) {
      const diferencia = parseFloat((montoTotal - totalAsignado).toFixed(2));
      monto = diferencia;
    } else {
      totalAsignado += montoBase;
    }

    const cuotaExistente = cuotasExistentes.get(i);
if (cuotaExistente) {
  nuevasCuotas.push({
    ...cuotaExistente,
    montosDet: monto,
          montoConCodigo: `${monedaCodigo.value} ${formatearMontoService.formatearMonto(monto.toFixed(2))}`,        
    diasVencimientosDet: 30, 
    modificado: true
  });
} else {
  nuevasCuotas.push({
    id: tempIdCounter--,
    planPago: item.value.id || 0,
    montosDet: monto,
          montoConCodigo: `${monedaCodigo.value} ${formatearMontoService.formatearMonto(monto.toFixed(2))}`,        
    diasVencimientosDet: 30,
    secuenciaDet: i,
    agregar: true,
    modificado: false,
    eliminar: false
  });
}  }


  planPagoDetNuevo.value.items = nuevasCuotas;

  itemPlanPagoDet.value = {
    id: 0,
    planPago: 0,
    montosDet: null,
    montoConCodigo: "",
    diasVencimientosDet: null,
    secuenciaDet: null,
    agregar: false,
    modificado: false,
    eliminar: false
  };
  loadingCargarPlanPagoDet.value = false;
  showModalCompleto.value = true;
};

async function updatePlanPagoDetNuevo() {

  const index = planPagoDetNuevo.value.items.findIndex(det => det.id === itemPlanPagoDet.value.id);
  const updatedItem = { ...itemPlanPagoDet.value };

  const monPagoId = item.value.monPagoId;
  if (monPagoId != null && monPagoId > 0) {
      const monto = updatedItem.montosDet?.toString() ?? "0";
      const moneda = await getMonedaPagoCodigo(monPagoId);
      updatedItem.montoConCodigo = `${moneda} ${formatearMontoService.formatearMonto(monto)}`;  
    } else {
    updatedItem.montoConCodigo = "";
  }

  planPagoDetNuevo.value.items[index] = updatedItem;
};

async function validarCamposRequeridosComplete() {
  const result = await formComplete.value?.validate();
  return result?.valid;
};

async function postPlanPagoNuevoGuide(){
  
  if(validarCamposNuevo()){
    try{
      loading.value = true;
      item.value.planPagoDet = toRaw(planPagoDetNuevo.value.items);
      const response = await apiQuery.post(
        `api/financieros/planes-pagos`,
        item.value
      );
      if(!response){
        throw new Error("Network response was not ok");
        showModalStepGuide.value = true;
        mensajeAdvertencia.value = "Network response was not ok";
        loading.value = false;
      }
      searchItem(item.value.descripcion);
      loading.value = false;
      showModalStepGuide.value = false;    
    }catch(error){
      console.error("Error fetching documentos:", error);
      throw error;
      }
  }
};

async function postPlanPagoNuevoComplete() {

    const esValido = await validarCamposRequeridosComplete();
    if (esValido) {
      if (validarCampos()){
      try {
        loading.value = true;
        item.value.planPagoDet = toRaw(planPagoDetNuevo.value.items);

        const response = await apiQuery.post(`api/financieros/planes-pagos`, item.value);

        if (!response) {
          throw new Error("Network response was not ok");
        }

        searchItem(item.value.descripcion);
        loading.value = false;
        showModalCompleto.value = false;
      } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
      }
    }
  }  
};

</script>

<template>
    <WFormData
    :loading="loading"
    :title="'Planes de Pagos'"
    :filters="null"
    :headers="headers"
    :items="planPago.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'100%'"
    :filtervalue="null"
    :pageNumber="planPago.pageNumber"
    :pageSize="planPago.pageSize"
    :totalPages="planPago.totalPages"
    :totalRecords="planPago.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @newItem="onNewItem"
    @update="updatePlaPago()"
    :disableInternalNewModal="disableInternalNewModal">

    <template #editItemPanel>
      <v-form ref="formComplete" v-model="isValidComplete">
        <v-row>
        <v-col cols="12" sm="6">
            <v-text-field 
            label="Descripción"
            :rules="[v => !!v || 'Requerido', v => v.length <= 100 || 'Máximo 80 caracteres']"
            counter="80"
            hint="Descripción"
            v-model="item.descripcion"
            variant="outlined"
            density="compact"            
            ></v-text-field>
        </v-col>
        <v-col col="12" sm="6">
            <v-autocomplete 
            label="Carrera" 
            v-model="item.carrId"
            variant="outlined"
            density="compact"
            hint="Carrera"
            v-model:search="searchTextCarrera"
            :items="carrera.items"
            item-title="descripcion"
            item-value="id"
            :loading="loadingCarrera"
            :loading-text="'Cargando...'"
            :custom-filter="customFilter"
            :no-data-text="'No hay datos'"
            :no-results-text="'No se encontraron resultados'"
            @update:modelValue="(value) => (item.carrId = value)"
            @update:search="(value) => (searchTextCarrera = value)">               
            </v-autocomplete>
        </v-col>
        <v-col col="12" sm="6">
            <v-autocomplete 
            label="Recinto"
            v-model="item.recintoId"
            variant="outlined"
            density="compact"
            hint="Recinto"
            v-model:search="searchTextRecinto"
            :items="sede.items"
            item-title="descripcion"
            item-value="id"
            :custom-filter="customFilter"
            :loading="loadingRecinto"
            :loading-text="'Cargando...'"
            :no-data-text="'No hay datos'"
            :no-results-text="'No se encontraron resultados'"
            @update:modelValue="(value) => (item.recintoId = value)"
            @update:search="(value) => (searchTextRecinto = value)">               
            ></v-autocomplete>
        </v-col>
        <v-col col="12" sm="6">
          <v-autocomplete 
          label="Moneda"
          hint="Moneda"
          v-model="item.monPagoId"
          variant="outlined"
          density="compact"
          v-model:search="searchTextMonedaPago"
          :items="monedaPago.items"
          item-title="descripcion"
          item-value="id"
          :loading="loadingMonedaPago"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :custom-filter="customFilter"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.monPagoId = value)"
          @update:search="(value) => (searchTextMonedaPago = value)">               
          ></v-autocomplete>
        </v-col>
        <v-col col="12" sm="4">
          <v-text-field 
          label="Monto Total"
          v-model="item.montoTotal"
          variant="outlined"
          density="compact"            
          type="number"
          counter="10"
          v-validate="'required|numeric|max:10'"
          :rules="[rules.required, v => !isNaN(parseFloat(v)) || parseFloat(v) >= 0 || 'Debe ser un número']">         
                <template #prepend-inner>
                  <v-icon>mdi-currency-usd</v-icon>
                </template>        
              </v-text-field>
        </v-col>
        <v-col col="12" sm="4">
          <v-text-field 
          label="Cantidad Cuotas Pagos"
          v-model="item.cantidadPago"
          variant="outlined"
          density="compact"            
          type="number"
          counter="3"
          :rules="[rules.required, v => !isNaN(parseFloat(v)) || 'Debe ser un número']">
          </v-text-field>
        </v-col>
        <v-col col="12" sm="4">
            <v-text-field 
            label="% Descuento"
            v-model="item.porcentajeDescuento"
            variant="outlined"
            density="compact"
            :rules="[rules.required, v => !isNaN(parseFloat(v)) || 'Debe ser un número']"         
            type="number"
            counter="3"
            v-validate="'required|numeric|max:3'"
            ></v-text-field>
        </v-col>
        <v-col col="12" sm="4">
            <v-text-field 
            label="Costo de Admision"
            v-model="item.costoAdmision"
            variant="outlined"
            density="compact"            
            type="number"
            counter="10"
            v-validate="'required|numeric|max:10'">
                <template #prepend-inner>
                    <v-icon>mdi-currency-usd</v-icon>
                </template>        
            </v-text-field>
        </v-col>
        <v-col col="12" sm="4">
            <v-text-field 
            label="Monto Inscripcion"
            v-model="item.montoInscripcion"
            type="number"
            counter="10"
            :rules="[rules.required, v => !isNaN(parseFloat(v)) || 'Debe ser un número']"         
            variant="outlined"
            density="compact"            
            v-validate="'required|numeric|max:10'">
                <template #prepend-inner>
                    <v-icon>mdi-currency-usd</v-icon>
                </template>        
            </v-text-field>
        </v-col>
        <v-col col="12" sm="4">
            <v-text-field 
            label="Monto ReInscripcion"
            v-model="item.montoReinscripcion"
            type="number"
            counter="10"
            :rules="[rules.required, v => !isNaN(parseFloat(v)) || 'Debe ser un número']"         
            variant="outlined"
            density="compact"            
            v-validate="'required|numeric|max:10'">
                <template #prepend-inner>
                    <v-icon>mdi-currency-usd</v-icon>
                </template>        
        </v-text-field>
        </v-col>
        <v-col col="12" sm="2">
            <v-switch 
            label="Por Creditos"
            variant="outlined"
            density="compact"            
            color="primary"
            v-model="item.isPorCredito"
            hint="Si está activo indica que el cálculo del valor de la matriculación se hará en base al Costo por Créditos."
            persistent-hint
            ></v-switch>
        </v-col>
        <v-col col="12" sm="2" v-show="item.isPorCredito">
            <v-text-field 
            label="Costo Creditos"
            tyoe="number"
            counter="12"
            v-model="item.costoCredito"
            variant="outlined"
            v-validate="'numeric|max:12'"
            density="compact">
                <template #prepend-inner>
                    <v-icon>mdi-currency-usd</v-icon>
                </template>        
        </v-text-field>
        </v-col>
        <v-col col="12" sm="2" v-show="item.isPorCredito">
            <v-text-field 
            label="Creditos Minimos"
            v-model="item.creditoMinimo"
            counter="6"
            type="number"
            v-validate="'numeric|max:6'"
            variant="outlined"
            density="compact"            
            ></v-text-field>
        </v-col>
        <v-col col="12" sm="3">
            <v-switch
            v-model="item.isCalculaRecargoInscripcionPorciento" 
            label="Calcular Recargo Inscrip. por %"
            hint="Si está activo indica que el Recargo de Inscripción se calculará en base al porcentaje digitado en el Recargo Inscripción."
            persistent-hint
            color="primary"
            variant="outlined"
            density="compact"            
            ></v-switch>
        </v-col>
        <v-col col="12" sm="3">
            <v-text-field 
            label="Recargo Inscripcion"
            type="number"
            v-model="item.recargoInscripcion"
            counter="10"
            variant="outlined"
            :rules="[rules.required, v => !isNaN(parseFloat(v)) || 'Debe ser un número']"         
            density="compact"            
            v-validate="'required|numeric|max:10'">
                <template #prepend-inner>
                    <v-icon>mdi-currency-usd</v-icon>
                </template>        
            </v-text-field>
        </v-col>
        <v-col col="12" sm="3">
            <v-switch 
            label="Calcular Recargo por %"
            variant="outlined"
            density="compact"            
            color="primary"
            v-model="item.isRecargoPorciento"
            hint="Si está activo indica que el Recargo se calculará en base al porcentaje digitado en el Recargo de Cuota."
            persistent-hint
            ></v-switch>
        </v-col>
        <v-col col="12" sm="3">
            <v-text-field 
            label="Recargo por Cuota"
            type="number"
            counter="10"
            variant="outlined"
            density="compact" 
            :rules="[rules.required, v => !isNaN(parseFloat(v)) || 'Debe ser un número']"       
            v-model="item.montoRecargoCuota"
            v-validate="'required|numeric|max:12'">
                <template #prepend-inner>
                    <v-icon>mdi-currency-usd</v-icon>
                </template>        
            </v-text-field>
        </v-col>
        <v-col col="12" sm="2">
            <v-text-field 
            label="Monto Inscripcion Monografico"
            v-model="item.montoInscripcionMonografico"
            type="number"
            counter="6"
            v-validate="'numeric|max:6'"
            variant="outlined"
            density="compact"            
            ></v-text-field>
        </v-col>
        <v-col col="12" sm="2">
            <v-text-field 
            label="Monto Inscripcion Tutorias"
            v-model="item.montoInscripcionTutorias"
            type="number"
            counter="6"
            v-validate="'numeric|max:6'"
            variant="outlined"
            density="compact"            
            ></v-text-field>
        </v-col>
        <v-col col="12" sm="2">
            <v-switch 
            label="Default"
            color="primary"
            v-model="item.isDefault"
            variant="outlined"
            density="compact"            
            ></v-switch>
        </v-col>
        <v-col col="12" sm="2">
            <v-switch 
            label="Activo"
            color="primary"
            v-model="item.estado"
            variant="outlined"
            density="compact"            
            ></v-switch>
        </v-col>
        <v-col cols="12" sm="12" v-if="mostrarAdvertenciaCompleto">
          <v-alert type="error" variant="outlined" prominent style="background-color: #eda9a9;">
            <v-p class="text-white">
              {{ mensajeAdvertencia }}
            </v-p>
          </v-alert>
        </v-col>
        <v-col cols="12">
          <v-alert icon="mdi-information" v-if="mostrarAlertaCuotas"  color="primary" variant="tonal">
            Ingresa el monto total y la cantidad de cuotas antes de agregar las cuotas.
          </v-alert>
        </v-col>   
        <v-col cols="12" sm="12">
          <v-row justify="end" class="mb-4">
          </v-row>
          <v-card class="rounded">
            <v-card-title>
              <v-toolbar flat color="primary" class="rounded">
                  <v-toolbar-title >Cuotas Plan de Pago</v-toolbar-title>
                  <v-spacer></v-spacer>
                  <v-tooltip text="Cargar Cuotas" location="top">
                    <template #activator="{ props }">
                      <v-btn
                      class="white"
                      :loading="loadingCargarPlanPagoDet"
                      variant="text"
                      v-bind="props"
                      @click.stop="agregarNuevoPlanPagoDet()">
                        <v-icon start>mdi-plus</v-icon>
                      </v-btn>
                    </template>
                  </v-tooltip>
              </v-toolbar>
              </v-card-title>
                <wDataTable
                :dialogWidth="'800px'"
                :loading="loadingDet"
                :title="'Cuotas Plan de Pago'"
                :headers="headersDet"
                :items="planPagoDet.items"
                :pageNumber="planPagoDet.pageNumber"
                :pageSize="planPagoDet.pageSize"
                :totalPages="planPagoDet.totalPages"
                :totalRecords="planPagoDet.totalRecords"
                v-model:add="nuevoItem"
                @editItem="editItemPlanPagoDet"
                @update="updatePlanPagoDet"
                @newItem="openModalPlaPagoDet()"
                @goTo="goTo">
                <template #editItem>
                  <v-col cols="12" sm="4">
                    <v-text-field
                    label="Monto"
                    type="number"
                    v-model="itemPlanPagoDet.montosDet"
                    v-validate="'required|decimal:2'"
                    variant="outlined"
                    density="compact">
                      <template #prepend-inner>
                          <v-icon>mdi-currency-usd</v-icon>
                      </template>        
                    </v-text-field>
                  </v-col>
                  <v-col cols="12" sm="4">
                    <v-text-field
                    label="Secuencia Cuota"
                    type="number"
                    v-model="itemPlanPagoDet.secuenciaDet"
                    v-validate="'required|numeric'"
                    variant="outlined"
                    density="compact">
                    </v-text-field>
                 </v-col>
                <v-col cols="12" sm="4">
                  <v-text-field
                    label="Día Vencimiento"
                    type="number"
                    v-model="itemPlanPagoDet.diasVencimientosDet"
                    v-validate="'required|numeric'"
                    variant="outlined"
                    density="compact">
                  </v-text-field>
                </v-col>
               </template>
              </wDataTable>
          </v-card>
        </v-col>
        </v-row>
      </v-form>
    </template>
  </WFormData>

<!-- Selector Modal -->
<v-row>
  <v-col cols="12" lg="8" md="6" class="text-right">
    <v-dialog v-model="showSelectorModal" max-width="400px" persistent>
      <v-card>
        <v-card-title class="pa-4 bg-primary">
          <v-icon>mdi-database-plus</v-icon> &nbsp;
          <span class="title text-white">Selecciona tipo de creación</span>
        </v-card-title>

        <v-card-text>
          <v-btn block color="primary" class="mb-2" @click="openModalStepGuide">
            <v-icon start>mdi-form-select</v-icon>
            Guiada
          </v-btn>
          <v-btn block color="secondary" class="mb-2" @click="openModalCompleto">
            <v-icon start>mdi-form-textbox</v-icon>
            Completo
          </v-btn>
        </v-card-text>

        <v-card-text class="pt-0 pb-0">
          <v-divider class="pb-0" color="primary"></v-divider>
        </v-card-text>

        <v-card-actions class="pa-4">
          <v-spacer></v-spacer>
          <v-btn color="error" variant="flat" @click="showSelectorModal = false">
            <v-icon>mdi-close</v-icon> Cancelar
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-col>
</v-row>

<!-- Modal Guiado -->
<v-row>
  <v-col cols="12" lg="8" md="6" class="text-right">
    <v-dialog v-model="showModalStepGuide" max-width="1000px">
      <v-card>
        <v-card-title class="pa-4 bg-primary d-flex align-center">
          <v-icon class="mr-2 text-white">mdi-database-edit-outline</v-icon> &nbsp;
          <span class="title text-white">Nuevo Planes de Pagos</span>
          <v-spacer></v-spacer>
          <v-btn icon variant="text" @click="showModalStepGuide = false" class="text-white">
            <v-icon>mdi-close</v-icon>
          </v-btn>        
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-stepper v-model="step" >
            <v-card class="pa-4 rounded-lg border border-grey-lighten-2 ">
              <v-stepper-header>
                <v-stepper-item
                  v-for="(item, index) in stepItems"
                  :key="index"
                  :value="index + 1"
                  class="px-4 py-2">
                  <template #default>
                    <span class="text-primary font-weight-bold">{{ item.title }}</span>
                  </template>
                </v-stepper-item>
              </v-stepper-header>
            </v-card>            
            <v-stepper-window v-model="step" hide-actions>
              <v-stepper-window-item :value="1">
                <v-card-text>
                  <v-form ref="formStep1" v-model="isValidStep1">
                  <v-row>
                    <v-col cols="12" sm="6">
                        <v-text-field 
                        label="Descripción"
                        hint="Descripción"
                        :rules="[v => !!v || 'Requerido', v => v.length <= 80 || 'Máximo 80 caracteres']"
                        counter="80"
                        v-model="item.descripcion"
                        variant="outlined"
                        density="compact"            
                        ></v-text-field>
                    </v-col>
                    <v-col col="12" sm="6">
                        <v-autocomplete 
                        label="Carrera"
                        hint="Carrera" 
                        v-model="item.carrId"
                        variant="outlined"
                        density="compact"
                        :rules="[v => !!v || 'Carrera requerida']"
                        v-model:search="searchTextCarrera"
                        :items="carrera.items"
                        item-title="descripcion"
                        item-value="id"
                        :custom-filter="customFilter"
                        :loading="loadingCarrera"
                        :loading-text="'Cargando...'"
                        :no-data-text="'No hay datos'"
                        :no-results-text="'No se encontraron resultados'"
                        @update:modelValue="(value) => (item.carrId = value)"
                        @update:search="(value) => (searchTextCarrera = value)">               
                        </v-autocomplete>
                    </v-col>
                    <v-col col="12" sm="6">
                        <v-autocomplete 
                        label="Recinto"
                        hint="Recinto"
                        :rules="[v => !!v || 'Recinto requerida']"
                        v-model="item.recintoId"
                        variant="outlined"
                        density="compact"
                        v-model:search="searchTextRecinto"
                        :items="sede.items"
                        item-title="descripcion"
                        item-value="id"
                        :custom-filter="customFilter"
                        :loading="loadingRecinto"
                        :loading-text="'Cargando...'"
                        :no-data-text="'No hay datos'"
                        :no-results-text="'No se encontraron resultados'"
                        @update:modelValue="(value) => (item.recintoId = value)"
                        @update:search="(value) => (searchTextRecinto = value)">               
                        ></v-autocomplete>
                    </v-col>
                    <v-col col="12" sm="6">
                      <v-autocomplete 
                      label="Moneda"
                      hint="Moneda"
                      :rules="[v => !!v || 'Moneda requerida']"
                      v-model="item.monPagoId"
                      variant="outlined"
                      density="compact"
                      v-model:search="searchTextMonedaPago"
                      :items="monedaPago.items"
                      item-title="descripcion"
                      item-value="id"
                      :custom-filter="customFilter"
                      :loading="loadingMonedaPago"
                      :loading-text="'Cargando...'"
                      :no-data-text="'No hay datos'"
                      :no-results-text="'No se encontraron resultados'"
                      @update:modelValue="(value) => (item.monPagoId = value)"
                      @update:search="(value) => (searchTextMonedaPago = value)">               
                      ></v-autocomplete>
                    </v-col>
                  </v-row>
                </v-form>
                </v-card-text>
              </v-stepper-window-item>
              <v-stepper-window-item :value="2">
                <v-card-text>
                  <v-form ref="formStep2" v-model="isValidStep2">
                  <v-row>
                    <v-col col="12" sm="6">
                      <v-text-field 
                      label="Monto Total"
                      v-model="item.montoTotal"
                      variant="outlined"
                      density="compact"            
                      type="number"
                      counter="10"
                      v-validate="'required|numeric|max:10'"
                      :rules="[v => !!v || !isNaN(parseFloat(v)) || parseFloat(v) >= 0 || 'Requeridoo']">         
                            <template #prepend-inner>
                              <v-icon>mdi-currency-usd</v-icon>
                            </template>        
                          </v-text-field>
                    </v-col>
                    <v-col col="12" sm="6">
                      <v-text-field 
                      label="Cantidad Cuotas Pagos"
                      v-model="item.cantidadPago"
                      variant="outlined"
                      density="compact"            
                      type="number"
                      counter="3"
                      :rules="[v => !!v || !isNaN(parseFloat(v)) || parseFloat(v) >= 0 || 'Requeridoo']">         
                      </v-text-field>
                    </v-col>
                    <v-col col="12" sm="6">
                        <v-text-field 
                        label="% Descuento"
                        v-model="item.porcentajeDescuento"
                        variant="outlined"
                        density="compact"
                        :rules="[rules.required, v => !isNaN(parseFloat(v)) || 'Debe ser un número']"         
                        type="number"
                        counter="3"
                        v-validate="'required|numeric|max:3'"
                        ></v-text-field>
                    </v-col>
                    <v-col col="12" sm="6">
                        <v-text-field 
                        label="Costo de Admision"
                        v-model="item.costoAdmision"
                        variant="outlined"
                        density="compact"            
                        type="number"
                        counter="10"
                        v-validate="'required|numeric|max:10'">
                            <template #prepend-inner>
                                <v-icon>mdi-currency-usd</v-icon>
                            </template>        
                        </v-text-field>
                    </v-col>
                    <v-col col="12" sm="6">
                        <v-text-field 
                        label="Monto Inscripcion"
                        v-model="item.montoInscripcion"
                        type="number"
                        counter="10"
                        :rules="[rules.required, v => !isNaN(parseFloat(v)) || 'Debe ser un número']"         
                        variant="outlined"
                        density="compact"            
                        v-validate="'required|numeric|max:10'">
                            <template #prepend-inner>
                                <v-icon>mdi-currency-usd</v-icon>
                            </template>        
                        </v-text-field>
                    </v-col>
                    <v-col col="12" sm="6">
                        <v-text-field 
                        label="Monto ReInscripcion"
                        v-model="item.montoReinscripcion"
                        type="number"
                        counter="10"
                        :rules="[v => !!v || !isNaN(parseFloat(v)) || parseFloat(v) >= 0 || 'Requerido']"         
                        variant="outlined"
                        density="compact"            
                        v-validate="'required|numeric|max:10'">
                            <template #prepend-inner>
                                <v-icon>mdi-currency-usd</v-icon>
                            </template>        
                    </v-text-field>
                    </v-col>
                    <v-col col="12" sm="6">
                      <v-text-field 
                      label="Monto Inscripcion Monografico"
                      v-model="item.montoInscripcionMonografico"
                      type="number"
                      counter="6"
                      v-validate="'numeric|max:6'"
                      variant="outlined"
                      density="compact"            
                      ></v-text-field>
                    </v-col>
                    <v-col col="12" sm="6">
                      <v-text-field 
                      label="Monto Inscripcion Tutorias"
                      v-model="item.montoInscripcionTutorias"
                      type="number"
                      counter="6"
                      v-validate="'numeric|max:6'"
                      variant="outlined"
                      density="compact"            
                      ></v-text-field>
                    </v-col>
                  <v-col col="12" sm="6">
                      <v-switch 
                      label="Por Creditos"
                      variant="outlined"
                      density="compact"            
                      color="primary"
                      v-model="item.isPorCredito"
                      hint="Si está activo indica que el cálculo del valor de la matriculación se hará en base al Costo por Créditos."
                      persistent-hint
                      ></v-switch>
                  </v-col>
                  <v-col col="12" sm="3" v-show="item.isPorCredito">
                      <v-text-field 
                      label="Costo Creditos"
                      tyoe="number"
                      counter="12"
                      v-model="item.costoCredito"
                      variant="outlined"
                      v-validate="'numeric|max:12'"
                      density="compact">
                          <template #prepend-inner>
                              <v-icon>mdi-currency-usd</v-icon>
                          </template>        
                  </v-text-field>
                  </v-col>
                  <v-col col="12" sm="3" v-show="item.isPorCredito">
                      <v-text-field 
                      label="Creditos Minimos"
                      v-model="item.creditoMinimo"
                      counter="6"
                      type="number"
                      v-validate="'numeric|max:6'"
                      variant="outlined"
                      density="compact"            
                      ></v-text-field>
                  </v-col>
                </v-row>
                </v-form>
              </v-card-text>
            </v-stepper-window-item>
            <v-stepper-window-item :value="3">
              <v-card-text>
                <v-form ref="formStep3" v-model="isValidStep3">
                <v-row>
                  <v-col col="12" sm="6">
                      <v-switch
                      v-model="item.isCalculaRecargoInscripcionPorciento" 
                      label="Calcular Recargo Inscrip. por %"
                      hint="Si está activo indica que el Recargo de Inscripción se calculará en base al porcentaje digitado en el Recargo Inscripción."
                      persistent-hint
                      color="primary"
                      variant="outlined"
                      density="compact"            
                      ></v-switch>
                  </v-col>
                  <v-col col="12" sm="6">
                      <v-text-field 
                      label="Recargo Inscripcion"
                      type="number"
                      v-model="item.recargoInscripcion"
                      counter="10"
                      variant="outlined"
                      :rules="[rules.required, v => !isNaN(parseFloat(v)) || 'Debe ser un número']"         
                      density="compact"            
                      v-validate="'required|numeric|max:10'">
                          <template #prepend-inner>
                              <v-icon>mdi-currency-usd</v-icon>
                          </template>        
                      </v-text-field>
                  </v-col>
                  <v-col col="12" sm="6">
                      <v-switch 
                      label="Calcular Recargo por %"
                      variant="outlined"
                      density="compact"            
                      color="primary"
                      v-model="item.isRecargoPorciento"
                      hint="Si está activo indica que el Recargo se calculará en base al porcentaje digitado en el Recargo de Cuota."
                      persistent-hint
                      ></v-switch>
                  </v-col>
                  <v-col col="12" sm="6">
                      <v-text-field 
                      label="Recargo por Cuota"
                      type="number"
                      counter="10"
                      variant="outlined"
                      density="compact" 
                      :rules="[rules.required, v => !isNaN(parseFloat(v)) || 'Debe ser un número']"       
                      v-model="item.montoRecargoCuota"
                      v-validate="'required|numeric|max:12'">
                          <template #prepend-inner>
                              <v-icon>mdi-currency-usd</v-icon>
                          </template>        
                      </v-text-field>
                  </v-col>
                </v-row>
                </v-form>
              </v-card-text>
            </v-stepper-window-item>
            <v-stepper-window-item :value="4">
              <v-card-text>
                <v-row>
                  <v-col cols="12" sm="12" v-if="mostrarAdvertenciaGuide">
                    <v-alert type="error" variant="outlined" prominent style="background-color: #eda9a9;">
                      <v-p class="text-white">
                        {{ mensajeAdvertencia }}
                      </v-p>
                    </v-alert>
                  </v-col>
                  <v-col col="12" sm="4">
                      <v-switch 
                      label="Default"
                      color="primary"
                      v-model="item.isDefault"
                      variant="outlined"
                      density="compact"            
                      ></v-switch>
                  </v-col>
                  <v-col col="12" sm="4">
                      <v-switch 
                      label="Activo"
                      color="primary"
                      v-model="item.estado"
                      variant="outlined"
                      density="compact"            
                      ></v-switch>
                  </v-col>
                  <v-col cols="12">
                    <v-alert icon="mdi-information" v-if="mostrarAlertaCuotas"  color="primary" variant="tonal">
                      Ingresa el monto total y la cantidad de cuotas antes de agregar las cuotas.
                    </v-alert>
                  </v-col>   
                  <v-col cols="12" sm="12">
                    <v-card class="rounded">
                      <v-card-title>
                          <v-toolbar flat color="primary" class="rounded">
                              <v-toolbar-title >Cuotas Plan de Pago</v-toolbar-title>
                              <v-spacer></v-spacer>
                              <v-tooltip text="Cargar Cuotas" location="top">
                                <template #activator="{ props }">
                                  <v-btn
                                  class="white"
                                  :loading="loadingCargarPlanPagoDet"
                                  variant="text"
                                  v-bind="props"
                                  @click.stop="agregarNuevoPlanPagoDetNuevo()">
                                    <v-icon start>mdi-plus</v-icon>
                                  </v-btn>
                                </template>
                              </v-tooltip>
                          </v-toolbar>
                          </v-card-title>
                          <wDataTable
                          class="pa-4"
                          :dialogWidth="'100%'"
                          :loading="loadingDet"
                          :title="'Cuotas Plan de Pago'"
                          :headers="headersDet"
                          :items="planPagoDetNuevo.items"
                          :pageNumber="planPagoDetNuevo.pageNumber"
                          :pageSize="planPagoDetNuevo.pageSize"
                          :totalPages="planPagoDetNuevo.totalPages"
                          :totalRecords="planPagoDetNuevo.totalRecords"
                          v-model:add="nuevoItem"
                          @editItem="editItemPlanPagoDetNuevo"
                          @update="updatePlanPagoDetNuevo">
                          <template #editItem>
                            <v-col cols="12" sm="4">
                              <v-text-field
                              label="Monto"
                              type="number"
                              v-model="itemPlanPagoDet.montosDet"
                              v-validate="'required|decimal:2'"
                              variant="outlined"
                              density="compact">
                                <template #prepend-inner>
                                    <v-icon>mdi-currency-usd</v-icon>
                                </template>        
                              </v-text-field>
                            </v-col>
                            <v-col cols="12" sm="4">
                              <v-text-field
                              label="Secuencia Cuota"
                              type="number"
                              v-model="itemPlanPagoDet.secuenciaDet"
                              v-validate="'required|numeric'"
                              variant="outlined"
                              density="compact">
                              </v-text-field>
                           </v-col>
                          <v-col cols="12" sm="4">
                            <v-text-field
                              label="Día Vencimiento"
                              type="number"
                              v-model="itemPlanPagoDet.diasVencimientosDet"
                              v-validate="'required|numeric'"
                              variant="outlined"
                              density="compact">
                            </v-text-field>
                          </v-col>
                         </template>
                        </wDataTable>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-stepper-window-item>
          </v-stepper-window>                
        </v-stepper>
      </v-card-text>
      <v-card-actions class="pa-4">
        <v-spacer />
        <v-btn
        v-if="step > 1"
          variant="flat"
          color="error"
          @click="step--">
          <v-icon>mdi-arrow-left</v-icon>
          Atras
        </v-btn>        
        <v-btn
          v-if="step < stepItems.length"
          color="primary"
          variant="flat"
          @click="btnSiguiente">
          Siguiente
          <v-icon>mdi-arrow-right</v-icon>
        </v-btn>
        <v-btn
          v-else
          color="primary"
          variant="flat"
          @click="postPlanPagoNuevoGuide">
          <v-icon>mdi-check-circle</v-icon>Aceptar
        </v-btn>
      </v-card-actions>
    </v-card>
    </v-dialog> 
  </v-col>
</v-row>

<!--Modal Completo-->
<v-row>
  <v-col cols="12" lg="8" md="6" class="text-right">
    <v-dialog v-model="showModalCompleto" max-width="100%">
      <v-card>
        <v-card-title class="pa-4 bg-primary d-flex align-center">
          <v-icon class="mr-2 text-white">mdi-database-edit-outline</v-icon> &nbsp;
          <span class="title text-white">Nuevo Planes de Pagos</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-form ref="formComplete" v-model="isValidComplete">
          <v-row>
            <v-col cols="12" sm="6">
              <v-text-field 
              label="Descripción"
              hint="Descripción"
              :rules="[v => !!v || 'Requerido', v => v.length <= 100 || 'Máximo 80 caracteres']"
              counter="80"
              v-model="item.descripcion"
              variant="outlined"
              density="compact"            
              ></v-text-field>
            </v-col>
            <v-col col="12" sm="6">
              <v-autocomplete 
              label="Carrera"
              hint="Carrera"
              :rules="[v => !!v || 'Requerido']"
              v-model="item.carrId"
              variant="outlined"
              density="compact"
              v-model:search="searchTextCarrera"
              :items="carrera.items"
              item-title="descripcion"
              item-value="id"
              :custom-filter="customFilter"
              :loading="loadingCarrera"
              :loading-text="'Cargando...'"
              :no-data-text="'No hay datos'"
              :no-results-text="'No se encontraron resultados'"
              @update:modelValue="(value) => (item.carrId = value)"
              @update:search="(value) => (searchTextCarrera = value)">               
              </v-autocomplete>
            </v-col>
            <v-col col="12" sm="6">
              <v-autocomplete 
              label="Recinto"
              :rules="[v => !!v || 'Requerido']"
              hint="Recinto"
              v-model="item.recintoId"
              variant="outlined"
              density="compact"
              v-model:search="searchTextRecinto"
              :items="sede.items"
              item-title="descripcion"
              item-value="id"
              :custom-filter="customFilter"
              :loading="loadingRecinto"
              :loading-text="'Cargando...'"
              :no-data-text="'No hay datos'"
              :no-results-text="'No se encontraron resultados'"
              @update:modelValue="(value) => (item.recintoId = value)"
              @update:search="(value) => (searchTextRecinto = value)">               
              ></v-autocomplete>
            </v-col>
            <v-col col="12" sm="6">
              <v-autocomplete 
              label="Moneda"
              hint="Moneda"
              :rules="[v => !!v || 'Requerido']"
              v-model="item.monPagoId"
              variant="outlined"
              density="compact"
              v-model:search="searchTextMonedaPago"
              :items="monedaPago.items"
              item-title="descripcion"
              item-value="id"
              :custom-filter="customFilter"
              :loading="loadingMonedaPago"
              :loading-text="'Cargando...'"
              :no-data-text="'No hay datos'"
              :no-results-text="'No se encontraron resultados'"
              @update:modelValue="(value) => (item.monPagoId = value)"
              @update:search="(value) => (searchTextMonedaPago = value)">               
              ></v-autocomplete>
            </v-col>
            <v-col col="12" sm="4">
              <v-text-field 
              label="Monto Total"
              v-model="item.montoTotal"
              variant="outlined"
              density="compact"            
              type="number"
              counter="10"
              v-validate="'required|numeric|max:10'"
              :rules="[rules.required, v => !isNaN(parseFloat(v)) || parseFloat(v) >= 0 || 'Debe ser un número']">         
                    <template #prepend-inner>
                      <v-icon>mdi-currency-usd</v-icon>
                    </template>        
                  </v-text-field>
            </v-col>
            <v-col col="12" sm="4">
              <v-text-field 
              label="Cantidad Cuotas Pagos"
              v-model="item.cantidadPago"
              variant="outlined"
              density="compact"            
              type="number"
              counter="3"
              :rules="[rules.required, v => !isNaN(parseFloat(v)) || 'Debe ser un número']">
              </v-text-field>
            </v-col>
            <v-col col="12" sm="4">
                <v-text-field 
                label="% Descuento"
                v-model="item.porcentajeDescuento"
                variant="outlined"
                density="compact"
                :rules="[rules.required, v => !isNaN(parseFloat(v)) || 'Debe ser un número']"         
                type="number"
                counter="3"
                v-validate="'required|numeric|max:3'"
                ></v-text-field>
            </v-col>
            <v-col col="12" sm="4">
                <v-text-field 
                label="Costo de Admision"
                v-model="item.costoAdmision"
                variant="outlined"
                density="compact"            
                type="number"
                counter="10"
                v-validate="'required|numeric|max:10'">
                    <template #prepend-inner>
                        <v-icon>mdi-currency-usd</v-icon>
                    </template>        
                </v-text-field>
            </v-col>
            <v-col col="12" sm="4">
                <v-text-field 
                label="Monto Inscripcion"
                v-model="item.montoInscripcion"
                type="number"
                counter="10"
                :rules="[rules.required, v => !isNaN(parseFloat(v)) || 'Debe ser un número']"         
                variant="outlined"
                density="compact"            
                v-validate="'required|numeric|max:10'">
                    <template #prepend-inner>
                        <v-icon>mdi-currency-usd</v-icon>
                    </template>        
                </v-text-field>
            </v-col>
            <v-col col="12" sm="4">
                <v-text-field 
                label="Monto ReInscripcion"
                v-model="item.montoReinscripcion"
                type="number"
                counter="10"
                :rules="[rules.required, v => !isNaN(parseFloat(v)) || 'Debe ser un número']"         
                variant="outlined"
                density="compact"            
                v-validate="'required|numeric|max:10'">
                    <template #prepend-inner>
                        <v-icon>mdi-currency-usd</v-icon>
                    </template>        
            </v-text-field>
            </v-col>
            <v-col col="12" sm="2">
                <v-switch 
                label="Por Creditos"
                variant="outlined"
                density="compact"            
                color="primary"
                v-model="item.isPorCredito"
                hint="Si está activo indica que el cálculo del valor de la matriculación se hará en base al Costo por Créditos."
                persistent-hint
                ></v-switch>
            </v-col>
            <v-col col="12" sm="2" v-show="item.isPorCredito">
                <v-text-field 
                label="Costo Creditos"
                tyoe="number"
                counter="12"
                v-model="item.costoCredito"
                variant="outlined"
                v-validate="'numeric|max:12'"
                density="compact">
                    <template #prepend-inner>
                        <v-icon>mdi-currency-usd</v-icon>
                    </template>        
            </v-text-field>
            </v-col>
            <v-col col="12" sm="2" v-show="item.isPorCredito">
                <v-text-field 
                label="Creditos Minimos"
                v-model="item.creditoMinimo"
                counter="6"
                type="number"
                v-validate="'numeric|max:6'"
                variant="outlined"
                density="compact"            
                ></v-text-field>
            </v-col>
            <v-col col="12" sm="3">
                <v-switch
                v-model="item.isCalculaRecargoInscripcionPorciento" 
                label="Calcular Recargo Inscrip. por %"
                hint="Si está activo indica que el Recargo de Inscripción se calculará en base al porcentaje digitado en el Recargo Inscripción."
                persistent-hint
                color="primary"
                variant="outlined"
                density="compact"            
                ></v-switch>
            </v-col>
            <v-col col="12" sm="3">
                <v-text-field 
                label="Recargo Inscripcion"
                type="number"
                v-model="item.recargoInscripcion"
                counter="10"
                variant="outlined"
                :rules="[rules.required, v => !isNaN(parseFloat(v)) || 'Debe ser un número']"         
                density="compact"            
                v-validate="'required|numeric|max:10'">
                    <template #prepend-inner>
                        <v-icon>mdi-currency-usd</v-icon>
                    </template>        
                </v-text-field>
            </v-col>
            <v-col col="12" sm="3">
                <v-switch 
                label="Calcular Recargo por %"
                variant="outlined"
                density="compact"            
                color="primary"
                v-model="item.isRecargoPorciento"
                hint="Si está activo indica que el Recargo se calculará en base al porcentaje digitado en el Recargo de Cuota."
                persistent-hint
                ></v-switch>
            </v-col>
            <v-col col="12" sm="3">
                <v-text-field 
                label="Recargo por Cuota"
                type="number"
                counter="10"
                variant="outlined"
                density="compact" 
                :rules="[rules.required, v => !isNaN(parseFloat(v)) || 'Debe ser un número']"       
                v-model="item.montoRecargoCuota"
                v-validate="'required|numeric|max:12'">
                    <template #prepend-inner>
                        <v-icon>mdi-currency-usd</v-icon>
                    </template>        
                </v-text-field>
            </v-col>
            <v-col col="12" sm="2">
                <v-text-field 
                label="Monto Inscripcion Monografico"
                v-model="item.montoInscripcionMonografico"
                type="number"
                counter="6"
                v-validate="'numeric|max:6'"
                variant="outlined"
                density="compact"            
                ></v-text-field>
            </v-col>
            <v-col col="12" sm="2">
                <v-text-field 
                label="Monto Inscripcion Tutorias"
                v-model="item.montoInscripcionTutorias"
                type="number"
                counter="6"
                v-validate="'numeric|max:6'"
                variant="outlined"
                density="compact"            
                ></v-text-field>
            </v-col>
            <v-col col="12" sm="2">
                <v-switch 
                label="Default"
                color="primary"
                v-model="item.isDefault"
                variant="outlined"
                density="compact"            
                ></v-switch>
            </v-col>
            <v-col col="12" sm="2">
                <v-switch 
                label="Activo"
                color="primary"
                v-model="item.estado"
                variant="outlined"
                density="compact"            
                ></v-switch>
            </v-col>
            <v-col cols="12" sm="12" v-if="mostrarAdvertenciaCompleto">
              <v-alert type="error" variant="outlined" prominent style="background-color: #eda9a9;">
                <v-p class="text-white">
                  {{ mensajeAdvertencia }}
                </v-p>
              </v-alert>
            </v-col>
            <v-col cols="12">
              <v-alert icon="mdi-information" v-if="mostrarAlertaCuotas"  color="primary" variant="tonal">
                Ingresa el monto total y la cantidad de cuotas antes de agregar las cuotas.
              </v-alert>
            </v-col>   
            <v-col cols="12" sm="12">
              <v-row justify="end" class="mb-4">
              </v-row>
              <v-card class="rounded">
                <v-card-title>
                  <v-toolbar flat color="primary" class="rounded">
                      <v-toolbar-title >Cuotas Plan de Pago</v-toolbar-title>
                      <v-spacer></v-spacer>
                      <v-tooltip text="Cargar Cuotas" location="top">
                        <template #activator="{ props }">
                          <v-btn
                          class="white"
                          :loading="loadingCargarPlanPagoDet"
                          variant="text"
                          v-bind="props"
                          @click.stop="agregarNuevoPlanPagoDetNuevoComplete()">
                            <v-icon start>mdi-plus</v-icon>
                          </v-btn>
                        </template>
                      </v-tooltip>
                  </v-toolbar>
                    </v-card-title>
                    <wDataTable
                    class="pa-4"
                    :dialogWidth="'100%'"
                    :loading="loadingDet"
                    :title="'Cuotas Plan de Pago'"
                    :headers="headersDet"
                    :items="planPagoDetNuevo.items"
                    :pageNumber="planPagoDetNuevo.pageNumber"
                    :pageSize="planPagoDetNuevo.pageSize"
                    :totalPages="planPagoDetNuevo.totalPages"
                    :totalRecords="planPagoDetNuevo.totalRecords"
                    v-model:add="nuevoItem"
                    @editItem="editItemPlanPagoDetNuevo"
                    @update="updatePlanPagoDetNuevo">
                    <template #editItem>
                      <v-col cols="12" sm="4">
                        <v-text-field
                        label="Monto"
                        type="number"
                        v-model="itemPlanPagoDet.montosDet"
                        v-validate="'required|decimal:2'"
                        variant="outlined"
                        density="compact">
                          <template #prepend-inner>
                              <v-icon>mdi-currency-usd</v-icon>
                          </template>        
                        </v-text-field>
                      </v-col>
                      <v-col cols="12" sm="4">
                        <v-text-field
                        label="Secuencia Cuota"
                        type="number"
                        v-model="itemPlanPagoDet.secuenciaDet"
                        v-validate="'required|numeric'"
                        variant="outlined"
                        density="compact">
                        </v-text-field>
                     </v-col>
                    <v-col cols="12" sm="4">
                      <v-text-field
                        label="Día Vencimiento"
                        type="number"
                        v-model="itemPlanPagoDet.diasVencimientosDet"
                        v-validate="'required|numeric'"
                        variant="outlined"
                        density="compact">
                      </v-text-field>
                    </v-col>
                   </template>
                  </wDataTable>
              </v-card>
            </v-col>
        </v-row>
        </v-form>
        </v-card-text>
        <v-card-text class="pt-0 pb-0">
          <v-divider class="pb-0" color="primary"></v-divider>
        </v-card-text>
        <v-card-actions class="pa-4">
          <v-spacer></v-spacer>
          <v-btn color="error" variant="flat" @click="showModalCompleto = false">
            <v-icon>mdi-close</v-icon> Volver
          </v-btn>
          <v-btn color="primary" variant="flat" @click.stop="postPlanPagoNuevoComplete">
            <v-icon>mdi-check-circle</v-icon> Aceptar
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-col>
</v-row>
</template>