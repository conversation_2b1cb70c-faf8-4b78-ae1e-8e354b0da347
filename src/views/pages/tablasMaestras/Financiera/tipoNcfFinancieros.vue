<script setup lang="ts">

import { onMounted, ref } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { tipoNcf } from "@/utils/models/Financiero/tipoNcfFinancieros";
import tipoNcfService from "@/services/Financieros/tipoNcfServices";
import { apiQuery } from "@/utils/helpers/apiQuery";

let loading = ref(false);

const headers = [
        { title: "Descripción", key: "descripcion" },
        { title: "Fiscal", key: "isFiscal" },
        { title: "Estatus", key: "estado" },
        { title: "Acciones", key: "acciones", sortable: false },
      ];

const tipoNcfs = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<tipoNcf>({
    id: 0,
    descripcion: "",
    isFiscal: false,
    estado: false
});

onMounted(async () => {
  await Promise.all([searchItem("")]);
});

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await tipoNcfService.searchItem(value ,pageNumber,pageSize);
    tipoNcfs.value = response;
    loading.value = false;
}

function editItem(tipoNfcEdit: tipoNcf) {
  Object.assign(item.value, tipoNfcEdit)
};

async function updateTipoNcf(){
    try {
        const response = await apiQuery.put(
            `api/financieros/tipos-ncf/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.descripcion);
        loading.value = false;
        //return response;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
}

function newItem(){
  item.value = {

    id: 0,
    descripcion: "",
    isFiscal: false,
    estado: false
}
}

async function savetipoNcf(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/financieros/tipos-ncf`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}
</script>

<template>
    <WFormData
    :loading="loading"
    :title="'Tipos de Nfc'"
    :filters="null"
    :headers="headers"
    :items="tipoNcfs.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tipoNcfs.pageNumber"
    :pageSize="tipoNcfs.pageSize"
    :totalPages="tipoNcfs.totalPages"
    :totalRecords="tipoNcfs.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateTipoNcf()"
    @newItem="newItem()"
    @save="savetipoNcf">

    <template #editItemPanel>
        <v-col cols="12" sm="12">
            <v-text-field
            label="descripcion"
            v-model="item.descripcion"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col col="12" sm="6">           
            <v-switch
            color="primary"
            label="Fiscal"
            v-model="item.isFiscal"
            outlined
            dense
            ></v-switch>
        </v-col>
        <v-col col="12" sm="6">           
            <v-switch
            color="primary"
            label="Estado"
            v-model="item.estado"
            outlined
            dense
            ></v-switch>
        </v-col>
    </template>
    </WFormData>
</template>