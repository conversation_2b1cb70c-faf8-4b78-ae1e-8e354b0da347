<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { aulaAsignacioncarrera } from "@/utils/models/Horarios/aulaAsignacionCarrera";
import AulaAsignacionCarreraService from "@/services/Horarios/AulasAsignacionCarrerasService";
import AulasServices from "@/services/Planta_Fisica/AulasServirces";
import CarrerasServices from "@/services/Academicos/CarrerasServicess";
import generaHoras from "@/utils/helpers/hourGenerator";
import WDataTable from "@/components/apps/wDataTable.vue";
import usePagination from "@/utils/helpers/usePagination";
import type { IAulas } from "@/utils/models/Planta_Fisica/IAulas";
import type { Carrera } from "@/utils/models/Academico/carrera";
import convertHorarioToNumber from "@/utils/helpers/convertHorariotoNumber";
import DiaService from "@/services/Horarios/DiaService";
import type { postAulaAsignacionCarrera } from "@/utils/models/Horarios/postAulaAsignacionCarrera";

const headers = [
  { title: "Alias", key: "alias" },
  { title: "Descripción", key: "descripcion" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const headersAulas = [
  { title: "Aula", key: "aulaDescripcion" },
  { title: "Hora Desde", key: "horaDesde" },
  { title: "Hora Hasta", key: "horaHasta" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const rules = {
  required: (value: any) => !!value || "Requerido.",
  mustSelectOne: (value: any) =>
    value.length > 0 || "Debe seleccionar al menos un día.",
};

const aulasAsignacionesCarreras = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const aulas = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const carreras = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const dias = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<aulaAsignacioncarrera>({
  id: 0,
  carrera: null,
  carreraDescripcion: null,
  aulas: [],
});

let itemAula = ref<postAulaAsignacionCarrera>({
  aula: null,
  aulaDescripcion: "",
  dias: "",
  horaDesde: undefined,
  horaHasta: undefined,
});

let loading = ref(false);
const arrHor = ref([]);
const paginationAulasDet = usePagination(item.value.aulas);
const nuevoItem = ref(false);
const aulaAdded = ref(false);
const isValidComplete = ref(false);
const aulasForm = ref();
const horaMismatch = ref(false);
const aulaEdificio = ref("");
const aulaPiso = ref("");
const aulaCapacidad = ref(0);
const rangoHoraAula = ref(false);
const disableButtonChoque = ref(false);
const rangoHoraAulaMensaje = ref("");

// Montar
onMounted(async () => {
  await Promise.all([getAulas(""), getCarreras(""), getDias("")]);
  generaHoras(arrHor.value, "08:00AM", "06:00PM", 5, false);
});

//Metodos
async function editItem(carrera: Carrera) {
  await searchItem("", carrera.id ?? 0);
  var aulaAsignacionCarrera = aulasAsignacionesCarreras.value.items.find(
    (x) => x.carrera == carrera.id
  );
  if (aulaAsignacionCarrera != undefined) {
    paginationAulasDet.updateList(aulaAsignacionCarrera.aulas);
    Object.assign(item.value, aulaAsignacionCarrera);
  } else {
    item.value.carrera = carrera.id;
    item.value.carreraDescripcion = carrera.descripcion;
    item.value.aulas = [];
    await getAulas("");
    paginationAulasDet.updateList([]);
  }
}

function onSaveAula() {
  const index = item.value.aulas.findIndex(
    (x) => x.aula === itemAula.value.aula
  );
  if (index !== -1) {
    item.value.aulas[index] = itemAula.value;
  } else {
    item.value.aulas.push(itemAula.value);
  }

  paginationAulasDet.updateList(item.value.aulas);
}

function onSelectAula() {
  var aula = aulas.value.items.find((x) => x.id == itemAula.value.aula);

  itemAula.value.aulaDescripcion = aula?.descripcion ?? "";
  aulaEdificio.value = aula?.edificioDescripcion ?? "";
  aulaCapacidad.value = aula?.capacidad ?? 0;
  aulaPiso.value = aula?.piso ?? "";

  aulasForm.value?.validate();

  validateAulas();
}

function deleteAula(aula: IAulas) {
  const index = item.value.aulas.findIndex((x) => x.aula === aula.id);
  if (index !== -1) {
    item.value.aulas.splice(index, 1);
  }
}

function toggleDay(dia: number) {
  let days = itemAula.value.dias ? itemAula.value.dias.split(",") : [];

  const index = days.indexOf(dia.toString());

  if (index === -1) {
    days.push(dia.toString());
  } else {
    days.splice(index, 1);
  }

  itemAula.value.dias = days.join(",");
}

async function onAdd() {
  nuevoItem.value = true;
  itemAula.value.aula = null;
  itemAula.value.dias = "";
  if (item.value.aulas.length > 0) {
    aulas.value.items = aulas.value.items.filter(
      (x) => !item.value.aulas.some((y) => y.aula == x.id)
    );
  } else {
    await getAulas("");
  }
  itemAula.value = {
    aula: null,
    aulaDescripcion: "",
    dias: "",
    horaDesde: undefined,
    horaHasta: undefined,
  };

  aulasForm.value?.validate();
}

async function validateAulas() {
  await searchItem("", 0);

  const startEdit = convertHorarioToNumber(itemAula.value.horaDesde);
  const endEdit = convertHorarioToNumber(itemAula.value.horaHasta);
  if (startEdit === undefined || endEdit === undefined) return false;

  aulasAsignacionesCarreras.value.items.some((itemAulaAsignacion) => {
    if (itemAulaAsignacion.aulas.length == 0) return false;

    var aulaChoque = itemAulaAsignacion.aulas.find(
      (x: any) => x.aula == itemAula.value.aula
    );

    if (aulaChoque != undefined) {
      const diasEdit = itemAula.value.dias.split(",").map(Number) ?? [];
      const diasOther = aulaChoque.dias?.split(",").map(Number) ?? [];

      const diasCoinciden = diasEdit.some((dia) => diasOther.includes(dia));

      const startOther = convertHorarioToNumber(aulaChoque.horaDesde);
      const endOther = convertHorarioToNumber(aulaChoque.horaHasta);
      if (startOther === undefined || endOther === undefined) return false;

      rangoHoraAula.value =
        (startEdit < endOther || endEdit > startOther) && diasCoinciden;
      disableButtonChoque.value =
        (startEdit < endOther || endEdit > startOther) &&
        !diasCoinciden &&
        itemAula.value.dias.length != 0;

      if (rangoHoraAula.value) {
        rangoHoraAulaMensaje.value = `Ya hay una aula asignada en este rango de hora. Ya está asignada desde las ${aulaChoque.horaDesde} hasta las ${aulaChoque.horaHasta}`;
      }
    } else {
      rangoHoraAula.value = false;
      disableButtonChoque.value = itemAula.value.dias.length != 0;
    }
  });
}

function validateHoras() {
  const horaDesde = convertHorarioToNumber(itemAula.value.horaDesde);
  const horaHasta = convertHorarioToNumber(itemAula.value.horaHasta);

  if (
    typeof horaDesde === "number" &&
    typeof horaHasta === "number" &&
    !isNaN(horaDesde) &&
    !isNaN(horaHasta) &&
    horaDesde > horaHasta
  ) {
    horaMismatch.value = true;
    itemAula.value.horaDesde = itemAula.value.horaHasta;
  }
}

function onSelectAulaDia(dia: number) {
  let days = itemAula.value.dias.split(",");

  let dayExist = days.findIndex((x) => x == dia.toString());
  if (dayExist == -1) return false;
  else return true;
}

async function editItemAula(
  postAulaAsignacionCarrera: postAulaAsignacionCarrera
) {
  Object.assign(itemAula.value, postAulaAsignacionCarrera);

  await getAulas("");
  var aula = aulas.value.items.find((x) => x.id == itemAula.value.aula);

  itemAula.value.aulaDescripcion = aula.descripcion;
  aulaEdificio.value = aula.edificioDescripcion;
  aulaCapacidad.value = aula.capacidad;
  aulaPiso.value = aula.piso;

  aulasForm.value?.validate();
}

async function searchItem(
  value: string,
  carrera: number,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await AulaAsignacionCarreraService.getAulaAsignacionCarreras(
    value,
    carrera,
    pageNumber,
    pageSize
  );
  aulasAsignacionesCarreras.value = response;
  loading.value = false;
}

async function getAulas(value: string) {
  loading.value = true;
  const response = await AulasServices.getAulas(value);

  aulas.value = response;
  loading.value = false;
}

async function getDias(value: string) {
  loading.value = true;
  const response = await DiaService.getDias(value);

  dias.value = response;
  loading.value = false;
}

async function getCarreras(value: string) {
  loading.value = true;
  const response = await CarrerasServices.getCarrerasServicios(value);

  carreras.value = response;
  loading.value = false;
}

async function updateAulaAsignacionCarrera() {
  loading.value = true;
  await apiQuery.put(
    `api/horarios/aula-asignacion-carrera/${item.value.id}`,
    item.value
  );
  searchItem(item.value.carreraDescripcion ?? "", item.value.carrera ?? 0);

  loading.value = false;
}

async function addAulaAsignacionCarrera() {
  loading.value = true;
  await apiQuery.post(`api/horarios/aula-asignacion-carrera`, item.value);
  searchItem(item.value.carreraDescripcion ?? "", item.value.carrera ?? 0);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    carrera: null,
    carreraDescripcion: null,
    aulas: [],
  };
}
</script>

<template>
  <wFormData
    :panel="true"
    :loading="loading"
    :title="'Aula Asignación Carrera'"
    :filters="null"
    :headers="headers"
    :items="carreras.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="carreras.pageNumber"
    :pageSize="carreras.pageSize"
    :totalPages="carreras.totalPages"
    :totalRecords="carreras.totalRecords"
    @editItem="editItem"
    @update="updateAulaAsignacionCarrera()"
    @searchItem="getCarreras"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="12">
        <v-autocomplete
          readonly
          v-model="item.carrera"
          :items="carreras.items"
          item-title="descripcion"
          item-value="id"
          label="Carrera"
          density="compact"
          variant="outlined"
          :loading="loading"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
        ></v-autocomplete>
      </v-col>
      <v-card>
        <v-card-title>
          <v-toolbar
            class="text-white rounded"
            color="primary"
            density="comfortable"
          >
            <v-toolbar-title>Aulas</v-toolbar-title>
            <v-btn icon @click="onAdd">
              <v-icon>mdi-plus</v-icon>
            </v-btn>
          </v-toolbar>
        </v-card-title>
        <v-card-text>
          <wDataTable
            :dialogWidth="'1000px'"
            :loading="loading"
            :title="'Aulas'"
            :headers="headersAulas"
            :items="paginationAulasDet.paginatedData.value"
            :pageNumber="paginationAulasDet.currentPage.value"
            :pageSize="paginationAulasDet.itemsPerPage.value"
            :totalPages="paginationAulasDet.totalPages.value"
            :totalRecords="item.aulas.length"
            v-model:add="nuevoItem"
            :edit="false"
            :eliminar="true"
            :imprimir="false"
            :valid="isValidComplete && disableButtonChoque"
            @editItem="editItemAula"
            @save="onSaveAula()"
            @update="onSaveAula()"
            @delete="deleteAula"
            @goTo="paginationAulasDet.goToPage"
          >
            <template #editItem>
              <v-col cols="12">
                <v-form
                  ref="aulasForm"
                  v-model="isValidComplete"
                  @submit.prevent="onSaveAula()"
                >
                  <span class="text-h5">{{
                    `Carrera: ${item.carreraDescripcion}`
                  }}</span>
                  <v-col cols="12" sm="12">
                    <v-autocomplete
                      v-model="itemAula.aula"
                      :items="aulas.items"
                      item-title="descripcion"
                      item-value="id"
                      label="Aula"
                      density="compact"
                      variant="outlined"
                      :loading="loading"
                      :loading-text="'Cargando...'"
                      :no-data-text="'No hay datos'"
                      :no-results-text="'No se encontraron resultados'"
                      :rules="[rules.required]"
                      @update:modelValue="
                        (value) => ((itemAula.aula = value), onSelectAula())
                      "
                    ></v-autocomplete>
                    <v-row>
                      <v-col cols="12" sm="6">
                        <v-autocomplete
                          v-model="itemAula.horaDesde"
                          :items="arrHor"
                          item-title="hora"
                          item-value="hora"
                          label="Hora Desde"
                          density="compact"
                          variant="outlined"
                          :loading="loading"
                          :loading-text="'Cargando...'"
                          :no-data-text="'No hay datos'"
                          :no-results-text="'No se encontraron resultados'"
                          :rules="[rules.required]"
                          @update:modelValue="
                            (value) => (
                              (itemAula.horaDesde = value),
                              validateHoras(),
                              validateAulas()
                            )
                          "
                        ></v-autocomplete>
                      </v-col>
                      <v-col cols="12" sm="6">
                        <v-autocomplete
                          v-model="itemAula.horaHasta"
                          :items="arrHor"
                          item-title="hora"
                          item-value="hora"
                          label="Hora Hasta"
                          density="compact"
                          variant="outlined"
                          :loading="loading"
                          :loading-text="'Cargando...'"
                          :no-data-text="'No hay datos'"
                          :no-results-text="'No se encontraron resultados'"
                          :rules="[rules.required]"
                          @update:modelValue="
                            (value) => (
                              (itemAula.horaHasta = value),
                              validateHoras(),
                              validateAulas()
                            )
                          "
                        ></v-autocomplete>
                      </v-col>
                    </v-row>
                    <v-row class="pb-2">
                      <v-col cols="12" sm="4">
                        <span class="text-h5">{{ `Piso: ${aulaPiso}` }}</span>
                      </v-col>
                      <v-col cols="12" sm="4">
                        <span class="text-h5">{{
                          `Edificio: ${aulaEdificio}`
                        }}</span>
                      </v-col>
                      <v-col cols="12" sm="4">
                        <span class="text-h5">{{
                          `Capacidad: ${aulaCapacidad}`
                        }}</span>
                      </v-col>
                    </v-row>
                    <div>
                      <h3>Días de la semana</h3>
                      <v-row>
                        <div v-for="day in dias.items" :key="day.id">
                          <v-checkbox
                            :label="day.descripcion"
                            hide-details
                            :model-value="onSelectAulaDia(day.id)"
                            @change="toggleDay(day.id), validateAulas()"
                          />
                        </div>
                      </v-row>
                    </div>
                    <v-alert
                      v-if="itemAula.dias.length == 0"
                      type="warning"
                      class="mt-2"
                      variant="outlined"
                    >
                      Debe seleccionar al menos un día.
                    </v-alert>
                  </v-col>
                </v-form>
              </v-col>
            </template>
          </wDataTable>
        </v-card-text>
      </v-card>
      <v-dialog v-model="aulaAdded" max-width="500px">
        <v-card>
          <v-card-title class="pa-4 bg-primary">
            <span class="text-h5">{{ "Aviso" }}</span>
          </v-card-title>
          <v-card-text>
            {{ "Ya esta aula fue asignada a esta carrera" }}
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="error" variant="flat" dark @click="aulaAdded = false"
              >Cancelar</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-dialog v-model="horaMismatch" max-width="500px">
        <v-card>
          <v-card-title class="pa-4 bg-primary">
            <span class="text-h5">{{ "Aviso" }}</span>
          </v-card-title>
          <v-card-text>
            <span class="text-h5">{{
              "La Hora Desde no puede ser mayor a la HoraHasta"
            }}</span>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="error"
              variant="flat"
              dark
              @click="horaMismatch = false"
              >Cancelar</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-dialog>
      <v-dialog v-model="rangoHoraAula" max-width="500px">
        <v-card>
          <v-card-title class="pa-4 bg-primary">
            <span class="text-h5">{{ "Aviso" }}</span>
          </v-card-title>
          <v-card-text>
            {{ rangoHoraAulaMensaje }}
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="error"
              variant="flat"
              dark
              @click="rangoHoraAula = false"
              >Cancelar</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-dialog>
    </template>
  </wFormData>
</template>
