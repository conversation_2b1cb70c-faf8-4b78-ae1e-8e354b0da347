<script setup lang="ts">
import { onMounted, ref } from "vue";
import type { ITipoColegio } from "@/utils/models/Academico/ITipoColegio";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type {Idiomas}  from "@/utils/models/Generales/IIdiomas";
import {IdiomasServices} from "@/services/Generales/IdiomasServices";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estado", key: "estado" },
  { title: "Acciones", key: "acciones" },
];

const idiomas = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});


let item = ref<Idiomas>({
    id: 0,
  company: 0,
  descripcion: "",
  estado: false
})

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});

//Metodos
function editItem(Idiomas: Idiomas) {
  Object.assign(item.value, Idiomas);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await IdiomasServices.getIdiomas(
    value,
    pageNumber,
    pageSize
  );
  idiomas.value = response; 
  loading.value = false;
}

async function updateIdiomas() {
  loading.value = true;
  const response = await apiQuery.put(
    `api/generales/idiomas/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);
  loading.value = false;
}

async function saveIdiomas(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/generales/idiomas`,
    item.value
  );
  
  searchItem(item.value.descripcion);
  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    company: 0,
    descripcion: "",
    estado: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Idiomas'"
    :filters="null"
    :headers="headers"
    :items="idiomas.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="idiomas.pageNumber"
    :pageSize="idiomas.pageSize"
    :totalPages="idiomas.totalPages"
    :totalRecords="idiomas.totalRecords"
    @editItem="editItem"
    @update="updateIdiomas()"
    @searchItem="searchItem"
    @newItem="newItem"
    @save="saveIdiomas"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
