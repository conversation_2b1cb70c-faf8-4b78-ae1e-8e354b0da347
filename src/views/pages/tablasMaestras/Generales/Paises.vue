<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { pais } from "@/utils/models/Generales/paises";
import PaisesService from "@/services/Generales/PaisesServices";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "ISO", key: "iso" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const paises = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<pais>({
  id: 0,
  descripcion: "",
  iso: "",
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(pais: pais) {
  Object.assign(item.value, pais);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await PaisesService.getPaises(value, pageNumber, pageSize);
  paises.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updatePais() {
  loading.value = true;
  await apiQuery.put(`api/generales/pais/${item.value.id}`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    iso: "",
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Países'"
    :filters="null"
    :headers="headers"
    :items="paises.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="paises.pageNumber"
    :pageSize="paises.pageSize"
    :totalPages="paises.totalPages"
    :totalRecords="paises.totalRecords"
    @editItem="editItem"
    @update="updatePais()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-text-field
          color="primary"
          label="Código ISO"
          v-model="item.iso"
          outlined
          dense
        ></v-text-field>
      </v-col>
    </template>
  </wFormData>
</template>
