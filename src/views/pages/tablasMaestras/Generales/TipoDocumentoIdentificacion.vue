<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { tipoDocumentoIdentificacion } from "@/utils/models/Generales/tipoDocumentoIdentificacion";
import TipoDocumentoIdentificacionService from "@/services/Generales/TipoDocumentoIdentificacionService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const tiposDocumentoIdentificacion = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<tipoDocumentoIdentificacion>({
  id: 0,
  descripcion: "",
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(tipoDocumentoIdentificacion: tipoDocumentoIdentificacion) {
  Object.assign(item.value, tipoDocumentoIdentificacion);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response =
    await TipoDocumentoIdentificacionService.getTiposDocumentoIdentificacion(
      value,
      pageNumber,
      pageSize
    );
  tiposDocumentoIdentificacion.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateTipoDocumentoIdentificacion() {
  loading.value = true;
  await apiQuery.put(
    `api/generales/tipo-documento-identificacion/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addTipoDocumentoIdentificacion() {
  loading.value = true;
  await apiQuery.post(
    `api/generales/tipo-documento-identificacion`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Tipo Documento Identificación'"
    :filters="null"
    :headers="headers"
    :items="tiposDocumentoIdentificacion.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tiposDocumentoIdentificacion.pageNumber"
    :pageSize="tiposDocumentoIdentificacion.pageSize"
    :totalPages="tiposDocumentoIdentificacion.totalPages"
    :totalRecords="tiposDocumentoIdentificacion.totalRecords"
    @editItem="editItem"
    @update="updateTipoDocumentoIdentificacion()"
    @save="addTipoDocumentoIdentificacion()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.estatus"
          color="primary"
          label="Estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
