<script setup lang="ts">

import {onMounted, ref} from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { tiposEmpresas } from "@/utils/models/Generales/tipoEmpresaGenerales"
import tipoEmpresaSevice from "@/services/Generales/tipoEmpresaGeneralesService";

const loading = ref(false);

const headers = [
    {title:"Descripción", key:"descripcion"},
    {title:"Estatus", key:"estado"},
    {title:"Acciones", key:"acciones", sortable: false},
];

const tipoEmpresa = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const item = ref<tiposEmpresas>({
    id: 0,
    descripcion: "",
    estado: false
});

onMounted(async () => {
  await Promise.all([searchItem("")]);
});

async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await tipoEmpresaSevice.searchItem(value ,pageNumber, pageSize);
    tipoEmpresa.value = response;
    loading.value = false;
};

function editItem(tipoEmpresaEdit: tiposEmpresas) {
    Object.assign(item.value, tipoEmpresaEdit);
};

function newItem(){

  item.value = {

    id: 0,
    descripcion: "",
    estado: true  
  }
}

async function updateTipoEmpresa(){
    try {
        const response = await apiQuery.put(
            `api/generales/tipos-empresas/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.descripcion);
        loading.value = false;
        //return response;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
}

async function saveTipoEmpresa(){
  loading.value = true;
  const response = await apiQuery.post(
`api/generales/tipos-empresas`,
item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}
</script>

<template>
  <WFormData
    :loading="loading"
    :title="'Tipos de Empresas'"
    :filters="null"
    :headers="headers"
    :items="tipoEmpresa.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tipoEmpresa.pageNumber"
    :pageSize="tipoEmpresa.pageSize"
    :totalPages="tipoEmpresa.totalPages"
    :totalRecords="tipoEmpresa.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateTipoEmpresa()"
    @newItem="newItem()"
    @save="saveTipoEmpresa()">
  <template #editItemPanel>
    <v-col cols="12" sm="12">
      <v-text-field
      label="Descripción"
      variant="outlined"
      density="compact"
      v-model="item.descripcion" 
      ></v-text-field>
    </v-col>
    <v-col cols="12" sm="4">
      <v-switch
      label="Activo"
      density="compact"
      variant="outlined"
      color="primary"
      v-model="item.estado" 
      ></v-switch>
    </v-col>
  </template>
  </WFormData>
</template>