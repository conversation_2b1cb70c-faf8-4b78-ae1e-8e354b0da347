<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { tipoTelefono } from "@/utils/models/Generales/tipoTelefono";
import TipoTelefonoService from "@/services/Generales/TipoTelefonoService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Es Celular", key: "esCelular" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const tiposTelefono = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<tipoTelefono>({
  id: 0,
  descripcion: "",
  estatus: false,
  esCelular: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(tipoTelefono: tipoTelefono) {
  Object.assign(item.value, tipoTelefono);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await TipoTelefonoService.getTiposTelefono(
    value,
    pageNumber,
    pageSize
  );
  tiposTelefono.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateTipoTelefono() {
  loading.value = true;
  await apiQuery.put(
    `api/generales/tipo-telefono/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addTipoTelefono() {
  loading.value = true;
  await apiQuery.post(`api/generales/tipo-telefono`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estatus: true,
    esCelular: false,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Tipo Teléfono'"
    :filters="null"
    :headers="headers"
    :items="tiposTelefono.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tiposTelefono.pageNumber"
    :pageSize="tiposTelefono.pageSize"
    :totalPages="tiposTelefono.totalPages"
    :totalRecords="tiposTelefono.totalRecords"
    @editItem="editItem"
    @update="updateTipoTelefono()"
    @save="addTipoTelefono()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.esCelular"
          color="primary"
          label="Es Celular"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.estatus"
          color="primary"
          label="Estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
