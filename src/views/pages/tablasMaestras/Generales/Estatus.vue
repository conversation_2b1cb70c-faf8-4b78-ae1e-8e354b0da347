<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { estatus } from "@/utils/models/Generales/estatus";
import EstatusService from "@/services/Generales/EstatusService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Activo", key: "activo" },
  { title: "Persona", key: "persona" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const allEstatus = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<estatus>({
  id: 0,
  descripcion: "",
  activo: false,
  persona: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(estatus: estatus) {
  Object.assign(item.value, estatus);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await EstatusService.getEstatus(value, pageNumber, pageSize);
  allEstatus.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateEstatus() {
  loading.value = true;
  await apiQuery.put(`api/generales/estatus/${item.value.id}`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addEstatus() {
  loading.value = true;
  await apiQuery.post(`api/generales/estatus`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    activo: false,
    persona: false,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Estatus'"
    :filters="null"
    :headers="headers"
    :items="allEstatus.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="allEstatus.pageNumber"
    :pageSize="allEstatus.pageSize"
    :totalPages="allEstatus.totalPages"
    :totalRecords="allEstatus.totalRecords"
    @editItem="editItem"
    @update="updateEstatus()"
    @save="addEstatus()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.activo"
          color="primary"
          label="Activo"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.persona"
          color="primary"
          label="Persona"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
