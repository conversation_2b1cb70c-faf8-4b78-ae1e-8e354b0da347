<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { estadoCiudadano } from "@/utils/models/Generales/estadoCiudadano";
import EstadoCiudadanoService from "@/services/Generales/EstadoCiudadano";

const headers = [
  { title: "Descripción", key: "nombre" },
  { title: "Indica Ciudadanía Local", key: "indicaCiudadaniaLocal" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const estadosCiudadano = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<estadoCiudadano>({
  id: 0,
  nombre: "",
  estatus: false,
  indicaCiudadaniaLocal: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(estadoCiudadano: estadoCiudadano) {
  Object.assign(item.value, estadoCiudadano);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await EstadoCiudadanoService.getEstadosCiudadano(
    value,
    pageNumber,
    pageSize
  );
  estadosCiudadano.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateEstadoCiudadano() {
  loading.value = true;
  await apiQuery.put(
    `api/generales/estado-ciudadano/${item.value.id}`,
    item.value
  );
  searchItem(item.value.nombre);

  loading.value = false;
}

async function addEstadoCiudadano() {
  loading.value = true;
  await apiQuery.post(`api/generales/estado-ciudadano`, item.value);
  searchItem(item.value.nombre);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    nombre: "",
    estatus: true,
    indicaCiudadaniaLocal: false,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Estado Ciudadano'"
    :filters="null"
    :headers="headers"
    :items="estadosCiudadano.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="estadosCiudadano.pageNumber"
    :pageSize="estadosCiudadano.pageSize"
    :totalPages="estadosCiudadano.totalPages"
    :totalRecords="estadosCiudadano.totalRecords"
    @editItem="editItem"
    @update="updateEstadoCiudadano()"
    @save="addEstadoCiudadano()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.nombre"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.indicaCiudadaniaLocal"
          color="primary"
          label="Indica Ciudadanía Local"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.estatus"
          color="primary"
          label="Estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
