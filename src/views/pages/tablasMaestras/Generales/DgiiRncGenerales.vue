<script setup lang="ts">

import { onMounted, ref } from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { dgiiRncs } from "@/utils/models/Generales/dgiiRncGenerales";
import dgiiRncService from "@/services/Generales/dgiiRncGeneralesService";

let loading = ref(false);

const headers = [
    {title:"Nombre", key:"nombre"},
    {title:"Rnc", key:"rncId"},
    {title:"Estado Rnc", key:"estadoDescr"},
    {title:"Estatus", key:"estado"},
    {title:"Acciones", key:"acciones", sortable: false},
];

const dgiiRnc = ref<PaginResponse>({
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    totalRecords: 1,
    items: [],
    succeeded: false,
    errors: null,
    message: null,
});

const item = ref<dgiiRncs>({
    id: 0,
    rncId: "",
    nombre: "",
    acronimo: "",
    razonSocial: "",
    direccion1: "",
    direccion2: "",
    direccion3: "",
    telefono: "",
    fecha: "",
    estadoDescr: "",
    tipoRnc: "",
    estado: false
});

const estadoRnc = [
    {value:"ACTIVO"},
    {value:"NO ACTIVO"},
    {value:"DADO DE BAJA"}
];

const tipoRnc = [
    {value:"NORMAL"}
];
onMounted(async () => {
  await Promise.all([searchItem("")]);
});

async function searchItem(value: string) {
    loading.value = true;
    const response = await dgiiRncService.searchItem(value ,1, 10);
    dgiiRnc.value = response;
    loading.value = false;
}

function editItem(dgiiRncEdit: dgiiRncs) {
    Object.assign(item.value, dgiiRncEdit)
};

async function updateDgiiRnc(){
    try {
        const response = await apiQuery.put(
            `api/generales/dgii-rnc/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.rncId);
        loading.value = false;
        //return response;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
}

function newItem(){

  item.value = {
    id: 0,
    rncId: "",
    nombre: "",
    acronimo: "",
    razonSocial: "",
    direccion1: "",
    direccion2: "",
    direccion3: "",
    telefono: "",
    fecha: "",
    estadoDescr: "",
    tipoRnc: "",
    estado: false
    }
}

async function saveDgiiRnc(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/generales/dgii-rnc`,
    item.value
  );
  searchItem(item.value.rncId);

  loading.value = false;
}
</script>

<template>
    <WFormData
    :panel="true"
    :loading="loading"
    :title="'RNC Contribuyentes'"
    :filters="null"
    :headers="headers"
    :items="dgiiRnc.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="dgiiRnc.pageNumber"
    :pageSize="dgiiRnc.pageSize"
    :totalPages="dgiiRnc.totalPages"
    :totalRecords="dgiiRnc.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateDgiiRnc()"
    @newItem="newItem()"
    @save="saveDgiiRnc()"
    >
    <template #editItemPanel>
        <v-col cols="12" sm="4">
            <v-text-field
            label="RNC"
            v-model="item.rncId"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="8">
            <v-text-field
            label="Nombre"
            v-model="item.nombre"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="12">
            <v-text-field
            label="Razón social"
            v-model="item.razonSocial"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-text-field
            label="Acrónimo"
            v-model="item.acronimo"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <!--Fecha-->
        <v-col cols="12" sm="6">
            <v-text-field
            label="Fecha"
            v-model="item.fecha"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-text-field
            label="Dirección 1"
            v-model="item.direccion1"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-text-field
            label="Dirección 2"
            v-model="item.direccion2"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-text-field
            label="Dirección 3"
            v-model="item.direccion3"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-text-field
            label="Teléfono"
            v-model="item.telefono"
            outlined
            dense
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-autocomplete
            label="Estado RNC"
            v-model="item.estadoDescr"
            :items="estadoRnc"
            item-value="value"
            item-title="value"
            outlined
            dense
            ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6">
            <v-autocomplete
            label="Tipo RNC"
            v-model="item.tipoRnc"
            :items="tipoRnc"
            item-value="value"
            item-title="value"
            outlined
            dense
            ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6">
            <v-switch
            label="Estado"
            color="primary"
            v-model="item.estado"
            outlined
            dense
            ></v-switch>
        </v-col>
    </template>
    </WFormData>
</template>