<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { tipoPersona } from "@/utils/models/Generales/tipoPersona";
import TipoPersonaService from "@/services/Generales/TipoPersonaServices";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Es Estudiante", key: "esEstudiante" },
  { title: "Plantilla", key: "plantilla" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const tiposPersona = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<tipoPersona>({
  id: 0,
  descripcion: "",
  estatus: false,
  esEstudiante: false,
  plantilla: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(tipoPersona: tipoPersona) {
  Object.assign(item.value, tipoPersona);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await TipoPersonaService.getTiposPersona(
    value,
    pageNumber,
    pageSize
  );
  tiposPersona.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateTipoPersona() {
  loading.value = true;
  await apiQuery.put(`api/generales/tipo-persona/${item.value.id}`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addTipoPersona() {
  loading.value = true;
  await apiQuery.post(`api/generales/tipo-persona`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estatus: true,
    esEstudiante: false,
    plantilla: false,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Tipo Persona'"
    :filters="null"
    :headers="headers"
    :items="tiposPersona.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tiposPersona.pageNumber"
    :pageSize="tiposPersona.pageSize"
    :totalPages="tiposPersona.totalPages"
    :totalRecords="tiposPersona.totalRecords"
    @editItem="editItem"
    @update="updateTipoPersona()"
    @save="addTipoPersona()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.esEstudiante"
          color="primary"
          label="Es Estudiante"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.estatus"
          color="primary"
          label="Estado"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="12">
        <v-text-field
          v-model="item.plantilla"
          color="primary"
          label="Plantilla"
          outlined
          dense
        ></v-text-field>
      </v-col>
    </template>
  </wFormData>
</template>
