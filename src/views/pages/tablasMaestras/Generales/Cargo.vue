<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { estadoCivil } from "@/utils/models/Generales/estadoCivil";
import EstadoCivilService from "@/services/Generales/EstadoCivilServices";
import type { cargo } from "@/utils/models/Generales/cargo";
import CargosService from "@/services/Generales/CargoService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const cargos = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<cargo>({
  id: 0,
  descripcion: "",
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(cargo: cargo) {
  Object.assign(item.value, cargo);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await CargosService.getCargos(value, pageNumber, pageSize);
  cargos.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateCargo() {
  loading.value = true;
  await apiQuery.put(`api/generales/cargos/${item.value.id}`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addCargo() {
  loading.value = true;
  await apiQuery.post(`api/generales/cargos`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Cargos'"
    :filters="null"
    :headers="headers"
    :items="cargos.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="cargos.pageNumber"
    :pageSize="cargos.pageSize"
    :totalPages="cargos.totalPages"
    :totalRecords="cargos.totalRecords"
    @editItem="editItem"
    @update="updateCargo()"
    @save="addCargo()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.estatus"
          color="primary"
          label="Estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
