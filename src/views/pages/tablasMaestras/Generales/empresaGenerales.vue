
<script setup lang="ts">

import {onMounted, ref, watch} from "vue";
import WFormData from "../../../../components/apps/wFormData.vue";
import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { empresas } from "@/utils/models/Generales/empresaGenerales";
import empresaSevice from "@/services/Generales/empresaGeneralesServices";
import lazyFetch from "@/utils/lazyFetch";
import customFilter from "@/utils/helpers/customFilter"

const loading = ref(false);
const loadingEmpresa = ref(false);



const searchTextTipoEmpresa = ref("");

const headers = [
    {title:"Nombre", key: "descripcion"},
    {title:"Rnc", key: "rnc"},
    {title:"Teléfono", key: "telefono"},
    {title:"Estado", key: "Estatus"},
    {title:"Acciones", key: "acciones", sortbla: false},
];

const empresa = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const tipoEmpresa = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null
});

const item = ref<empresas>({
    id: 0,
    tipEmpresaId:  null,
    tipoEmpresaDescripcion: "",
    descripcion: "",
    rnc: "",
    direccion: "",
    telefono: "",
    fax: "",
    email: "",
    estado: false,
    referencia: "",
    isMontoReprobado: false
});

const rules = {
  required: (value: any) => value !== null && value !== undefined && value !== '' || "Requerido"
};

onMounted(async () => {
  await Promise.all([searchItem("")]);
  await getTipoEmpresa("");
});

watch(searchTextTipoEmpresa, (newValue) => {
  getTipoEmpresa(newValue);
});


async function searchItem(value: string, pageNumber: number = 1, pageSize: number = 10) {
    loading.value = true;
    const response = await empresaSevice.searchItem(value ,pageNumber, pageSize);
    empresa.value = response;
    loading.value = false;
};

async function getTipoEmpresa(value: string = "") {
  loadingEmpresa.value = true;
  const response = await lazyFetch(() => empresaSevice.getTiposEmpresa(value));
  tipoEmpresa.value = response;
  loadingEmpresa.value = false;
};

function editItem(empresaEdit: empresas) {
    Object.assign(item.value, empresaEdit);

    searchTextTipoEmpresa.value = empresaEdit.tipoEmpresaDescripcion || "";
};

function newItem(){

    searchTextTipoEmpresa.value = "";

  item.value = {
    id: 0,
    tipEmpresaId:  null,
    tipoEmpresaDescripcion: "",
    descripcion: "",
    rnc: "",
    direccion: "",
    telefono: "",
    fax: "",
    email: "",
    estado: true,
    referencia: "",
    isMontoReprobado: false 
  }
}

async function updateEmpresa(){
    try {
        const response = await apiQuery.put(
            `api/generales/empresas/${item.value.id}`,
            item.value
        );

        if(!response){
            throw new Error("Network response was not ok")
        }

        searchItem(item.value.descripcion);
        loading.value = false;
        //return response;
    } catch (error) {
        console.error("Error fetching documentos:", error);
        throw error;
    }
}

async function saveEmpresa(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/generales/empresas`,
item.value
  );
  searchItem(item.value.descripcion);

  loading.value = false;
}
</script>

<template>
    <WFormData
    :loading="loading"
    :title="'Empresas'"
    :filters="null"
    :headers="headers"
    :items="empresa.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="empresa.pageNumber"
    :pageSize="empresa.pageSize"
    :totalPages="empresa.totalPages"
    :totalRecords="empresa.totalRecords"
    @searchItem="searchItem"
    @editItem="editItem"
    @update="updateEmpresa()"
    @newItem="newItem()"
    @save="saveEmpresa()"
    >
    <template #editItemPanel>
        <v-col cols="12" sm="6">
            <v-autocomplete
            label="Tipo empresa"
            v-model="item.tipEmpresaId"
            variant="outlined"
            density="compact"
            hint="Tipo empresa"
            v-model:search="searchTextTipoEmpresa"
            :items="tipoEmpresa.items"
            item-title="descripcion"
            item-value="id"
            :loading="loadingEmpresa"
            :rules="[v => !!v || 'Tipo empresa requerida']"
            :loading-text="'Cargando...'"
            :custom-filter="customFilter"
            :no-data-text="'No hay datos'"
            :no-results-text="'No se encontraron resultados'"
            @update:modelValue="(value) => (item.tipEmpresaId = value)"
            @update:search="(value) => (searchTextTipoEmpresa = value)">               
            </v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6">
            <v-text-field
            label="Rnc"
            density="compact"
            variant="outlined"
            v-model="item.rnc"
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="12">
            <v-text-field
            label="Nombre"
            density="compact"
            variant="outlined"
            v-model="item.descripcion"
            :rules="[v => !!v || 'Requerido', v => v.length <= 80 || 'Máximo 80 caracteres']"
            counter="80"
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-text-field
            label="Teléfono"
            density="compact"
            variant="outlined"
            v-model="item.telefono"
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-text-field
            label="Fax"
            density="compact"
            variant="outlined"
            v-model="item.fax"
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="12">
            <v-text-field
            label="Email"
            density="compact"
            variant="outlined"
            v-model="item.email"
            :rules="[v => !!v || 'Email requerido']"
            counter="80"
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="12">
            <v-text-field
            label="Dirección"
            density="compact"
            variant="outlined"
            :rules="[v => !!v || 'Requerido', v => v.length <= 250 || 'Máximo 80 caracteres']"
            counter="250"
            v-model="item.direccion"
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="12">
            <v-text-field
            label="Referencia"
            density="compact"
            variant="outlined"
            v-model="item.referencia"
            ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6">
            <v-switch
            label="Cargar monto retiro/reprobado"
            density="compact"
            variant="outlined"
            color="primary"
            v-model="item.isMontoReprobado"
            ></v-switch>
        </v-col>
        <v-col cols="12" sm="6">
            <v-switch
            label="Activo"
            density="compact"
            variant="outlined"
            color="primary"
            v-model="item.estado"
            ></v-switch>
        </v-col>
    </template>
    </WFormData>
</template>