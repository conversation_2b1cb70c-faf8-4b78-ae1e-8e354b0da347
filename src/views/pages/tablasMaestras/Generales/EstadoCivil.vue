<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { estadoCivil } from "@/utils/models/Generales/estadoCivil";
import EstadoCivilService from "@/services/Generales/EstadoCivilServices";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const estadosCiviles = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<estadoCivil>({
  id: 0,
  descripcion: "",
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(estadoCivil: estadoCivil) {
  Object.assign(item.value, estadoCivil);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await EstadoCivilService.getEstadosCiviles(
    value,
    pageNumber,
    pageSize
  );
  estadosCiviles.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateEstadoCivil() {
  loading.value = true;
  await apiQuery.put(`api/generales/estado-civil/${item.value.id}`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addEstadoCivil() {
  loading.value = true;
  await apiQuery.post(`api/generales/estado-civil`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Estados Civiles'"
    :filters="null"
    :headers="headers"
    :items="estadosCiviles.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="estadosCiviles.pageNumber"
    :pageSize="estadosCiviles.pageSize"
    :totalPages="estadosCiviles.totalPages"
    :totalRecords="estadosCiviles.totalRecords"
    @editItem="editItem"
    @update="updateEstadoCivil()"
    @save="addEstadoCivil()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.estatus"
          color="primary"
          label="Estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
