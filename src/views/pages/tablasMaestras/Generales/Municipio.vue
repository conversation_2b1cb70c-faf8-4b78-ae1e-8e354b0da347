<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { IMunicipio } from "@/utils/models/Generales/IMunicipio";
import PaisService from "@/services/Academicos/PaisService";
import ProvinciaService from "@/services/Generales/ProvinciaServices";
import MunicipioServices from "@/services/Generales/MunicipioServices";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Pais", key: "paisDescripcion" },
  { title: "Provincia", key: "provinciaDescripcion" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const paises = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});
const provincias = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const municipios = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});
let item = ref<IMunicipio>({
  id: 0,
  company: 0,
  descripcion: "",
  pais: null,
  provincia: null,
  estado: true,
  localidad: null,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([getPaises(), getProvincias(), searchItem("")]);
});
//Metodos
function editItem(municipio: IMunicipio) {
  Object.assign(item.value, municipio);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await MunicipioServices.getMunicipio(
    value,
    pageNumber,
    pageSize
  );
  municipios.value = response; // <- ¡Aquí está el cambio importante!
  loading.value = false;
}

async function updateMunicipios() {
  loading.value = true;
  await apiQuery.put(`api/generales/municipios/${item.value.id}`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addMunicipios() {
  loading.value = true;
  await apiQuery.post(`api/generales/municipios`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    company: 0,
    descripcion: "",
    pais: null,
    provincia: null,
    estado: true,
    localidad: null,
  };
}

async function getPaises() {
  loading.value = true;
  const response = await PaisService.getPaises("", 1, 100000);
  paises.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}
async function getProvincias() {
  loading.value = true;
  const response = await ProvinciaService.getProvincias("", 1, 100000);
  provincias.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Municipio'"
    :filters="null"
    :headers="headers"
    :items="municipios.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="municipios.pageNumber"
    :pageSize="municipios.pageSize"
    :totalPages="municipios.totalPages"
    :totalRecords="municipios.totalRecords"
    @editItem="editItem"
    @update="updateMunicipios()"
    @save="addMunicipios()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.pais"
          :items="paises.items"
          item-title="descripcion"
          item-value="id"
          label="Pais"
          density="compact"
          variant="outlined"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.provincia"
          :items="provincias.items"
          item-title="descripcion"
          item-value="id"
          label="Provincias"
          density="compact"
          variant="outlined"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.estado"
          color="primary"
          label="Estado"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12">
        <v-textarea v-model="item.localidad" label="Localidad" outlined dense />
      </v-col>
    </template>
  </wFormData>
</template>
