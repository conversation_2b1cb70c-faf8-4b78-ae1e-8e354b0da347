<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { provincia } from "@/utils/models/Generales/provincias";
import ProvinciaService from "@/services/Generales/ProvinciaServices";
import PaisesService from "@/services/Generales/PaisesServices";
import lazyFetch from "@/utils/lazyFetch";
import PaisService from "@/services/Academicos/PaisService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Código", key: "codigo" },
  { title: "País", key: "paisDescripcion" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const provincias = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const paises = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<provincia>({
  id: 0,
  descripcion: "",
  pais: 0,
  paisDescripcion: "",
  codigo: "",
  estatus: false,
});

let loading = ref(false);
let searchTextPaises = ref("");

// Montar
onMounted(async () => {
  await Promise.all([searchItem(""), searchPaises("")]);
});

watch(searchTextPaises, (newValue) => {
  searchPaises(newValue);
});

//Metodos
function editItem(provincia: provincia) {
  Object.assign(item.value, provincia);
  searchTextPaises.value = provincia.paisDescripcion || "";
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await ProvinciaService.getProvincias(
    value,
    pageNumber,
    pageSize
  );
  provincias.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function searchPaises(value: string) {
  loading.value = true;

  const response = await lazyFetch(() => PaisService.getPaises(value));
  paises.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateProvincia() {
  loading.value = true;
  await apiQuery.put(`api/generales/provincias/${item.value.id}`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addProvincia() {
  loading.value = true;
  await apiQuery.post(`api/generales/provincias`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    pais: null,
    paisDescripcion: "",
    codigo: "",
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Provincias'"
    :filters="null"
    :headers="headers"
    :items="provincias.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="provincias.pageNumber"
    :pageSize="provincias.pageSize"
    :totalPages="provincias.totalPages"
    :totalRecords="provincias.totalRecords"
    @editItem="editItem"
    @update="updateProvincia()"
    @save="addProvincia()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-autocomplete
          :items="paises.items"
          label="País"
          v-model="item.pais"
          v-model:search="searchTextPaises"
          item-title="descripcion"
          item-value="id"
          outlined
          dense
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.pais = value)"
          @update:search="(value) => (searchTextPaises = value)"
        ></v-autocomplete
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Código"
          v-model="item.codigo"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estatus"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
