<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { nacionalidad } from "@/utils/models/Generales/nacionalidad";
import NacionalidadService from "@/services/Generales/NacionalidadService";
import PaisesService from "@/services/Generales/PaisesServices";
import lazyFetch from "@/utils/lazyFetch";
import customFilter from "@/utils/helpers/customFilter";

const headers = [
  { title: "País", key: "paisDescripcion" },
  { title: "Descripción", key: "descripcion" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const nacionalidades = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const paises = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<nacionalidad>({
  id: 0,
  descripcion: "",
  pais: 0,
  paisDescripcion: "",
});

let loading = ref(false);
let searchTextPais = ref("");

// Montar
onMounted(async () => {
  await Promise.all([searchItem(""), searchPaises("")]);
});

watch(searchTextPais, (newValue) => {
  searchPaises(newValue);
});

//Metodos
function editItem(nacionalidad: nacionalidad) {
  Object.assign(item.value, nacionalidad);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await NacionalidadService.getNacionalidades(
    value,
    pageNumber,
    pageSize
  );
  nacionalidades.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function searchPaises(value: string) {
  loading.value = true;

  const response = await lazyFetch(() => PaisesService.getPaises(value));
  paises.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateNacionalidad() {
  loading.value = true;
  await apiQuery.put(`api/generales/estado-civil/${item.value.id}`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addNacionalidad() {
  loading.value = true;
  await apiQuery.post(`api/generales/estado-civil`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    pais: null,
    paisDescripcion: "",
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Nacionalidad'"
    :filters="null"
    :headers="headers"
    :items="nacionalidades.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="nacionalidades.pageNumber"
    :pageSize="nacionalidades.pageSize"
    :totalPages="nacionalidades.totalPages"
    :totalRecords="nacionalidades.totalRecords"
    @editItem="editItem"
    @update="updateNacionalidad()"
    @save="addNacionalidad()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.pais"
          v-model:search="searchTextPais"
          :items="paises.items"
          item-title="descripcion"
          item-value="id"
          label="País"
          density="compact"
          variant="outlined"
          :custom-filter="customFilter"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.pais = value)"
          @update:search="(value) => (searchTextPais = value)"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
    </template>
  </wFormData>
</template>
