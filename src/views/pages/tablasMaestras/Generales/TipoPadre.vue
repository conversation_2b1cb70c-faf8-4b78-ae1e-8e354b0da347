<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { tipoPadre } from "@/utils/models/Generales/tipoPadre";
import TipoPadreService from "@/services/Generales/TipoPadreService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Es Padre", key: "esPadre" },
  { title: "Es Madre", key: "esMadre" },
  { title: "Es Tutor", key: "esTutor" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const tiposPadre = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<tipoPadre>({
  id: 0,
  descripcion: "",
  estatus: false,
  esPadre: false,
  esMadre: false,
  esTutor: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(tipoPadre: tipoPadre) {
  Object.assign(item.value, tipoPadre);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await TipoPadreService.getTiposPadre(
    value,
    pageNumber,
    pageSize
  );
  tiposPadre.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateTipoPadre() {
  loading.value = true;
  await apiQuery.put(`api/generales/tipo-padre/${item.value.id}`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addTipoPadre() {
  loading.value = true;
  await apiQuery.post(`api/generales/tipo-padre`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estatus: true,
    esPadre: false,
    esMadre: false,
    esTutor: false,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Tipo Padre'"
    :filters="null"
    :headers="headers"
    :items="tiposPadre.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tiposPadre.pageNumber"
    :pageSize="tiposPadre.pageSize"
    :totalPages="tiposPadre.totalPages"
    :totalRecords="tiposPadre.totalRecords"
    @editItem="editItem"
    @update="updateTipoPadre()"
    @save="addTipoPadre()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.esPadre"
          color="primary"
          label="Es Padre"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.esMadre"
          color="primary"
          label="Es Madre"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.esTutor"
          color="primary"
          label="Es Tutor"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.estatus"
          color="primary"
          label="Estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
