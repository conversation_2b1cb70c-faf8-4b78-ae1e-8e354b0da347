<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { redSocial } from "@/utils/models/Generales/redSocial";
import RedSocialService from "@/services/Generales/RedSocialService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const redesSociales = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<redSocial>({
  id: 0,
  descripcion: "",
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(redSocial: redSocial) {
  Object.assign(item.value, redSocial);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await RedSocialService.getRedesSociales(
    value,
    pageNumber,
    pageSize
  );
  redesSociales.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateRedSocial() {
  loading.value = true;
  await apiQuery.put(`api/generales/red-social/${item.value.id}`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addRedSocial() {
  loading.value = true;
  await apiQuery.post(`api/generales/red-social`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Red Social'"
    :filters="null"
    :headers="headers"
    :items="redesSociales.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="redesSociales.pageNumber"
    :pageSize="redesSociales.pageSize"
    :totalPages="redesSociales.totalPages"
    :totalRecords="redesSociales.totalRecords"
    @editItem="editItem"
    @update="updateRedSocial()"
    @save="addRedSocial()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.estatus"
          color="primary"
          label="Estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
