<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { ocupacion } from "@/utils/models/Generales/ocupacion";
import OcupacionesService from "@/services/Generales/OcupacionService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const ocupaciones = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<ocupacion>({
  id: 0,
  descripcion: "",
  estatus: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(ocupacion: ocupacion) {
  Object.assign(item.value, ocupacion);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await OcupacionesService.getOcupaciones(
    value,
    pageNumber,
    pageSize
  );
  ocupaciones.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

async function updateOcupacion() {
  loading.value = true;
  await apiQuery.put(`api/generales/ocupaciones/${item.value.id}`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addOcupacion() {
  loading.value = true;
  await apiQuery.post(`api/generales/ocupaciones`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estatus: true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Ocupación'"
    :filters="null"
    :headers="headers"
    :items="ocupaciones.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="ocupaciones.pageNumber"
    :pageSize="ocupaciones.pageSize"
    :totalPages="ocupaciones.totalPages"
    :totalRecords="ocupaciones.totalRecords"
    @editItem="editItem"
    @update="updateOcupacion()"
    @save="addOcupacion()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.estatus"
          color="primary"
          label="Estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
