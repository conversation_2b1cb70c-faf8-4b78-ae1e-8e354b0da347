<script setup lang="ts">
import { onMounted, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { motivo } from "@/utils/models/Generales/motivo";
import MotivoService from "@/services/Generales/MotivoService";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estatus", key: "estatus" },
  { title: "Acciones", key: "acciones", sortable: false },
];

const motivos = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const tiposMotivos = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<motivo>({
  id: 0,
  descripcion: "",
  estatus: false,
  tipoMotivo: 0,
  tipoMotivoDescripcion: "",
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});
//Metodos
function editItem(motivo: motivo) {
  Object.assign(item.value, motivo);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;

  const response = await MotivoService.getMotivos(value, pageNumber, pageSize);
  motivos.value = response; // <- ¡Aquí está el cambio importante!

  loading.value = false;
}

// async function searchTiposMotivo() {
//   loading.value = true;

//   const response = await MotivoService.getTiposMotivo();
//   tiposMotivos.value = response; // <- ¡Aquí está el cambio importante!

//   loading.value = false;
// }

async function updateMotivo() {
  loading.value = true;
  await apiQuery.put(`api/generales/motivo/${item.value.id}`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

async function addMotivo() {
  loading.value = true;
  await apiQuery.post(`api/generales/motivo`, item.value);
  searchItem(item.value.descripcion);

  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    descripcion: "",
    estatus: true,
    tipoMotivo: 0,
    tipoMotivoDescripcion: "",
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Motivos'"
    :filters="null"
    :headers="headers"
    :items="motivos.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="motivos.pageNumber"
    :pageSize="motivos.pageSize"
    :totalPages="motivos.totalPages"
    :totalRecords="motivos.totalRecords"
    @editItem="editItem"
    @update="updateMotivo()"
    @save="addMotivo()"
    @searchItem="searchItem"
    @newItem="newItem"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          v-model="item.estatus"
          color="primary"
          label="Estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
