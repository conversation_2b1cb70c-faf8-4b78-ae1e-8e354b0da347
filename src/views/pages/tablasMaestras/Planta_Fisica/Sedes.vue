<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import { SedesServices } from "@/services/Planta_Fisica/SedesServices";
import { PaisesService } from "@/services/Generales/PaisesServices";
import ProvinciaService from "@/services/Generales/ProvinciaServices";
import type { ISedes } from "@/utils/models/Planta_Fisica/ISedes";
import lazyFetch from "@/utils/lazyFetch";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estado", key: "estado" },
  { title: "Acciones", key: "acciones" },
];

const sedes = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const paises = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});
const provincia = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<ISedes>({
  id: 0,
  company: 0,
  descripcion: "",
  pais: null,
  paisDescripcion: "",
  provincia: null,
  provinciaDescripcion: "",
  iniciales: "",
  direccion: "",
  email: "",
  telefono: "",
  encargado: "",
  centroCosto: "",
  cuentaContable: "",
  cuentaContableDimesion: "",
  tenant: "",
  estado: true,
  pisos: "",
});

let loading = ref(false);
const paisesSearch = ref("");
const provinciasSearch = ref("");

// Montar
onMounted(async () => {
  await Promise.all([getPaises(""), getProvincias(""), searchItem("")]);
});

watch(paisesSearch, (newValue) => {
  getPaises(newValue);
});

watch(provinciasSearch, (newValue) => {
  getProvincias(newValue);
});

//Metodos
async function editItem(sede: ISedes) {
  Object.assign(item.value, sede);
  await getPaises(item.value.paisDescripcion);
  await getProvincias(item.value.provinciaDescripcion);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await SedesServices.getSedes(value, pageNumber, pageSize);
  sedes.value = response;
  loading.value = false;
}

async function updateSedes() {
  loading.value = true;
  const response = await apiQuery.put(
    `api/planta-fisica/sedes/${item.value.id}`,
    item.value
  );

  searchItem(item.value.descripcion);
  loading.value = false;
}

async function saveSedes() {
  loading.value = true;
  console.log(item.value);
  const response = await apiQuery.post(`api/planta-fisica/sedes`, item.value);
  searchItem(item.value.descripcion);
  loading.value = false;
}

async function getPaises(value: string) {
  loading.value = true;
  const response = await lazyFetch(() => PaisesService.getPaises(value));
  paises.value = response;
  loading.value = false;
}

async function getProvincias(value: string) {
  loading.value = true;
  const response = await lazyFetch(() => ProvinciaService.getProvincias(value));
  provincia.value = response;
  loading.value = false;
}

function newItem() {
  item.value = {
    id: 0,
    company: 0,
    descripcion: "",
    pais: null,
    paisDescripcion: "",
    provincia: null,
    provinciaDescripcion: "",
    iniciales: "",
    direccion: "",
    email: " ",
    telefono: "",
    encargado: "",
    centroCosto: "",
    cuentaContable: "",
    cuentaContableDimesion: "",
    tenant: "",
    estado: true,
    pisos: "",
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Sedes'"
    :filters="null"
    :headers="headers"
    :items="sedes.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="sedes.pageNumber"
    :pageSize="sedes.pageSize"
    :totalPages="sedes.totalPages"
    :totalRecords="sedes.totalRecords"
    @editItem="editItem"
    @update="updateSedes()"
    @searchItem="searchItem"
    @newItem="newItem"
    @save="saveSedes()"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Iniciales"
          v-model="item.iniciales"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.pais"
          :items="paises.items"
          v-model:search="paisesSearch"
          item-title="descripcion"
          item-value="id"
          label="Paises"
          density="compact"
          variant="outlined"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.pais = value)"
          @update:search="(value) => (paisesSearch = value)"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.provincia"
          :items="provincia.items"
          v-model:search="provinciasSearch"
          item-title="descripcion"
          item-value="id"
          label="Provincias"
          density="compact"
          variant="outlined"
          :loading-text="'Cargando...'"
          :no-data-text="'No hay datos'"
          :no-results-text="'No se encontraron resultados'"
          @update:modelValue="(value) => (item.provincia = value)"
          @update:search="(value) => (provinciasSearch = value)"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Encargado"
          v-model="item.encargado"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Pisos"
          v-model="item.pisos"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Direccion"
          v-model="item.direccion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Telefono"
          v-model="item.telefono"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Email"
          v-model="item.email"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Centro Costo"
          v-model="item.centroCosto"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Cuenta Contable GP"
          v-model="item.cuentaContable"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Cuenta Contable GP Dimensional"
          v-model="item.cuentaContableDimesion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Tenant"
          v-model="item.tenant"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
