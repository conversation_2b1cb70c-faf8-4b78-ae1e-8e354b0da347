<script setup lang="ts">
import { onMounted, ref } from "vue";
import type { ITipoColegio } from "@/utils/models/Academico/ITipoColegio";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type {ITipoFacilidad} from "@/utils/models/Planta_Fisica/ITipoFacilidad";
import {TipoFacilidadServices} from "@/services/Planta_Fisica/TipoFacilidadServices"

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estado", key: "estado" },
  { title: "Acciones", key: "acciones" },
];

const tipoFacilidad = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<ITipoFacilidad>({
    id:0,
    company:0,
    descripcion:"",
    estado:false,
})

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});

//Metodos
function editItem(tipoFacilidad: ITipoFacilidad) {
  Object.assign(item.value, tipoFacilidad);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await TipoFacilidadServices.getTipoFacilidad(
    value,
    pageNumber,
    pageSize
  );
  tipoFacilidad.value = response; 
  loading.value = false;
}

async function updateTipoFacilidad() {
  loading.value = true;
  const response = await apiQuery.put(
    `api/planta-fisica/tipos-facilidades/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);
  loading.value = false;
}

async function saveTipoFacilidad(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/planta-fisica/tipos-facilidades`,
    item.value
  );
  searchItem(item.value.descripcion);
  loading.value = false;
}

function newItem() {
  item.value = {
    id:0,
    company:0,
    descripcion:"",
    estado:true,
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Tipo Facilidades'"
    :filters="null"
    :headers="headers"
    :items="tipoFacilidad.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tipoFacilidad.pageNumber"
    :pageSize="tipoFacilidad.pageSize"
    :totalPages="tipoFacilidad.totalPages"
    :totalRecords="tipoFacilidad.totalRecords"
    @editItem="editItem"
    @update="updateTipoFacilidad()"
    @searchItem="searchItem"
    @newItem="newItem"
    @save="saveTipoFacilidad()"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
