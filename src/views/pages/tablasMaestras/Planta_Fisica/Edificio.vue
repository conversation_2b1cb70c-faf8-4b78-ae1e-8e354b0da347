<script setup lang="ts">
import { onMounted, ref } from "vue";
import type { ITipoColegio } from "@/utils/models/Academico/ITipoColegio";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type {ITipoFacilidad} from "@/utils/models/Planta_Fisica/ITipoFacilidad";
import type {IEdificio} from "@/utils/models/Planta_Fisica/IEdificio";
import {TipoFacilidadServices} from "@/services/Planta_Fisica/TipoFacilidadServices";
import {EdificiosServices} from "@/services/Planta_Fisica/EdificiosServices";
import {SedesServices} from "@/services/Planta_Fisica/SedesServices";
import { fa } from "vuetify/lib/locale/index.mjs";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estado", key: "estado" },
  { title: "Acciones", key: "acciones" },
];


const edificios = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const sedes = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<IEdificio>({
    id:0,
    company:0,
    descripcion:"",
    sede:null,
    pisos:null,
    estado:false,
    localizacioDescripcion:"",
    localizacion:null
})

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([getSedes(), searchItem("")]);
});

//Metodos
function editItem(tipoFacilidad: ITipoFacilidad) {
  Object.assign(item.value, tipoFacilidad);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await EdificiosServices.getEdificio(
    value,
    pageNumber,
    pageSize
  );
  edificios.value = response; 
  loading.value = false;
}

async function updateEdificios() {
  loading.value = true;
  const response = await apiQuery.put(
    `api/planta-fisica/edificios/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);
  loading.value = false;
}

async function saveEdificios(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/planta-fisica/edificios`,
    item.value
  );
  searchItem(item.value.descripcion);
  loading.value = false;
}

async function getSedes() {
    loading.value = true;
  const response = await SedesServices.getSedes(
    "",
    1,
    100000
  );
  sedes.value = response;
  loading.value = false;
}


function newItem() {
  item.value = {
    id:0,
    company:0,
    descripcion:"",
    sede:null,
    pisos:null,
    estado:false,
    localizacioDescripcion:"",
    localizacion:null
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Edificio'"
    :filters="null"
    :headers="headers"
    :items="edificios.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="edificios.pageNumber"
    :pageSize="edificios.pageSize"
    :totalPages="edificios.totalPages"
    :totalRecords="edificios.totalRecords"
    @editItem="editItem"
    @update="updateEdificios()"
    @searchItem="searchItem"
    @newItem="newItem"
    @save="saveEdificios()"
  >
    <template #editItemPanel>

      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.sede"
          :items="sedes.items"
          item-title="descripcion"
          item-value="id"
          label="Recintos"
          density="compact"
          variant="outlined"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Pisos"
          v-model="item.pisos"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Localizacion"
          v-model="item.localizacioDescripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>

    </template>
  </wFormData>
</template>
