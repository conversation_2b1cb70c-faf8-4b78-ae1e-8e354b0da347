<script setup lang="ts">
import { onMounted, computed, ref } from "vue";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type { IAulas } from "@/utils/models/Planta_Fisica/IAulas";
import { TipoAulasServices } from "@/services/Planta_Fisica/TipoAulasServices";
import { AulasServices } from "@/services/Planta_Fisica/AulasServirces";
import SedesServices from "@/services/Planta_Fisica/SedesServices";
import EdificiosServices from "@/services/Planta_Fisica/EdificiosServices";

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estado", key: "estado" },
  { title: "Acciones", key: "acciones" },
];

const aulas = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const tiposAulas = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const sedes = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const edificios = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

const paises = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<IAulas>({
  id: 0,
  company: 0,
  descripcion: "",
  sede: null,
  tipoAula: null,
  edificio: null,
  piso: "",
  fechaInicio: new Date(),
  fechaFin: new Date(),
  dimesionLargo: 0,
  dimensionAncho: 0,
  capacidad: 0,
  llavePuerta: "",
  llevaPago: false,
  estado: false,
});

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([
    getTipoAulas(),
    getSedes(),
    getEdificio(),
    searchItem(""),
  ]);
});

//Metodos
function editItem(ncfEdit: IAulas) {
  Object.assign(item.value, {
    ...ncfEdit,
    fechaFin: ncfEdit.fechaFin ? new Date(ncfEdit.fechaFin) : null,
    fechaInicio: ncfEdit.fechaInicio ? new Date(ncfEdit.fechaInicio) : null, // Convierte la fecha del backend en un objeto Date o null
  });
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await AulasServices.getAulas(value, pageNumber, pageSize);
  aulas.value = response;
  loading.value = false;
}

async function updateAulas() {
  loading.value = true;
  const response = await apiQuery.put(
    `api/planta-fisica/aulas/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);
  loading.value = false;
}

async function saveAulas() {
  loading.value = true;
  const response = await apiQuery.post(`api/planta-fisica/aulas`, item.value);
  searchItem(item.value.descripcion);
  loading.value = false;
}

//Datos
async function getTipoAulas() {
  loading.value = true;
  const response = await TipoAulasServices.getTipoAulas("", 1, 100000);
  tiposAulas.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}

async function getSedes() {
  loading.value = true;
  const response = await SedesServices.getSedes("", 1, 100000);
  sedes.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}

async function getEdificio() {
  loading.value = true;
  const response = await EdificiosServices.getEdificio("", 1, 100000);
  edificios.value = response;
  // Aquí puedes asignar la respuesta a una variable o hacer algo con ella
  loading.value = false;
}

//formatear

function newItem() {
  item.value = {
    id: 0,
    company: 0,
    descripcion: "",
    sede: null,
    tipoAula: null,
    edificio: null,
    piso: "",
    fechaInicio: new Date(),
    fechaFin: new Date(),
    dimesionLargo: 0,
    dimensionAncho: 0,
    capacidad: 0,
    llavePuerta: "",
    llevaPago: false,
    estado: true,
  };
}

const showDatePicker = ref(false);
const showDatePickerFin = ref(false);

const formatearFechaInicio = computed({
  get() {
    if (!item.value.fechaInicio) return "";
    return item.value.fechaInicio.toISOString().split("T")[0]; // Formato YYYY-MM-DD
  },
  set(value: string) {
    item.value.fechaInicio = value ? new Date(value) : null;
  },
});

const formatearFechaFin = computed({
  get() {
    if (!item.value.fechaFin) return "";
    return item.value.fechaFin.toISOString().split("T")[0]; // Formato YYYY-MM-DD
  },
  set(value: string) {
    item.value.fechaFin = value ? new Date(value) : null;
  },
});

function onDateSelected(value: any) {
  item.value.fechaInicio = value;
  showDatePicker.value = false;
}

function selecionarFechaFin(value: any) {
  item.value.fechaFin = value;
  showDatePickerFin.value = false;
}
</script>

<template>
  <wFormData
    :panel="true"
    :loading="loading"
    :title="'Aulas'"
    :filters="null"
    :headers="headers"
    :items="aulas.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="aulas.pageNumber"
    :pageSize="aulas.pageSize"
    :totalPages="aulas.totalPages"
    :totalRecords="aulas.totalRecords"
    @editItem="editItem"
    @update="updateAulas()"
    @searchItem="searchItem"
    @newItem="newItem"
    @save="saveAulas()"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.tipoAula"
          :items="tiposAulas.items"
          item-title="descripcion"
          item-value="id"
          label="Tipos Aulas"
          density="compact"
          variant="outlined"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.sede"
          :items="sedes.items"
          item-title="descripcion"
          item-value="id"
          label="Recintos"
          density="compact"
          variant="outlined"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="6">
        <v-autocomplete
          v-model="item.edificio"
          :items="edificios.items"
          item-title="descripcion"
          item-value="id"
          label="Edificio"
          density="compact"
          variant="outlined"
        ></v-autocomplete>
      </v-col>
      <v-col cols="12" sm="4">
        <v-text-field
          label="Largo Dimension"
          v-model="item.dimesionLargo"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="4">
        <v-text-field
          label="Ancho Dimension"
          v-model="item.dimensionAncho"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="4">
        <v-text-field
          label="Aula Capacidad"
          v-model="item.capacidad"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="4">
        <v-dialog v-model="showDatePicker" width="300px">
          <template #activator="{ props }">
            <v-text-field
              v-model="formatearFechaInicio"
              label="Fecha Inicio"
              hint="YYYY-MM-DD"
              persistent-hint
              v-bind="props"
            />
          </template>
          <v-date-picker
            show-adjacent-months
            v-model="item.fechaInicio"
            @update:model-value="onDateSelected"
            color="primary"
          />
        </v-dialog>
      </v-col>
      <v-col cols="12" sm="4">
        <v-dialog v-model="showDatePickerFin" width="300px">
          <template #activator="{ props }">
            <v-text-field
              v-model="formatearFechaFin"
              label="Fecha Final"
              hint="YYYY-MM-DD"
              persistent-hint
              v-bind="props"
            />
          </template>
          <v-date-picker
            show-adjacent-months
            v-model="item.fechaFin"
            @update:model-value="selecionarFechaFin"
            color="primary"
          />
        </v-dialog>
      </v-col>

      <v-col cols="12" sm="4">
        <v-text-field
          label="LLave"
          v-model="item.llavePuerta"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="4">
        <v-text-field
          label="Piso"
          v-model="item.piso"
          outlined
          dense
        ></v-text-field
      ></v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Requiere Pago"
          v-model="item.llevaPago"
          outlined
          dense
        ></v-switch>
      </v-col>

      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
