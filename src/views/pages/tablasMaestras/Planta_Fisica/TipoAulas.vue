<script setup lang="ts">
import { onMounted, ref } from "vue";
import type { ITipoColegio } from "@/utils/models/Academico/ITipoColegio";
import wFormData from "../../../../components/apps/wFormData.vue";
import { apiQuery } from "@/utils/helpers/apiQuery";
import type { PaginResponse } from "@/utils/PaginResponse";
import type {ITipoAulas} from "@/utils/models/Planta_Fisica/ITipoAulas";
import {TipoAulasServices} from "@/services/Planta_Fisica/TipoAulasServices"

const headers = [
  { title: "Descripción", key: "descripcion" },
  { title: "Estado", key: "estado" },
  { title: "Acciones", key: "acciones" },
];

const tipoAulas = ref<PaginResponse>({
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,
  totalRecords: 1,
  items: [],
  succeeded: false,
  errors: null,
  message: null,
});

let item = ref<ITipoAulas>({
    id:0,
    company:0,
    descripcion:"",
    estado:false,
    claseExterna:false,
    laboratorio:false,
    virtual:false,
    validaChoque:false
})

let loading = ref(false);

// Montar
onMounted(async () => {
  await Promise.all([searchItem("")]);
});

//Metodos
function editItem(tipoAulas: ITipoAulas) {
  Object.assign(item.value, tipoAulas);
}

async function searchItem(
  value: string,
  pageNumber: number = 1,
  pageSize: number = 10
) {
  loading.value = true;
  const response = await TipoAulasServices.getTipoAulas(
    value,
    pageNumber,
    pageSize
  );
  tipoAulas.value = response; 
  loading.value = false;
}

async function updateTipoAulas() {
  loading.value = true;
  const response = await apiQuery.put(
    `api/planta-fisica/tipos-aulas/${item.value.id}`,
    item.value
  );
  searchItem(item.value.descripcion);
  loading.value = false;
}

async function saveTipoAulas(){
  loading.value = true;
  const response = await apiQuery.post(
    `api/planta-fisica/tipos-aulas`,
    item.value
  );
  searchItem(item.value.descripcion);
  loading.value = false;
}

function newItem() {
  item.value = {
    id:0,
    company:0,
    descripcion:"",
    estado:true,
    claseExterna:false,
    laboratorio:false,
    virtual:false,
    validaChoque:false
  };
}
</script>

<template>
  <wFormData
    :loading="loading"
    :title="'Tipo Aulas'"
    :filters="null"
    :headers="headers"
    :items="tipoAulas.items"
    :icon="'mdi-file-document'"
    :text="``"
    :dialogWidth="'800px'"
    :filtervalue="null"
    :pageNumber="tipoAulas.pageNumber"
    :pageSize="tipoAulas.pageSize"
    :totalPages="tipoAulas.totalPages"
    :totalRecords="tipoAulas.totalRecords"
    @editItem="editItem"
    @update="updateTipoAulas()"
    @searchItem="searchItem"
    @newItem="newItem"
    @save="saveTipoAulas()"
  >
    <template #editItemPanel>
      <v-col cols="12" sm="6">
        <v-text-field
          label="Descripción"
          v-model="item.descripcion"
          outlined
          dense
        ></v-text-field
      ></v-col>

      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Estado"
          v-model="item.estado"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Clase Externa"
          v-model="item.claseExterna"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Laboratorio"
          v-model="item.laboratorio"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Virtual"
          v-model="item.virtual"
          outlined
          dense
        ></v-switch>
      </v-col>
      <v-col cols="12" sm="3">
        <v-switch
          color="primary"
          label="Valida Choque"
          v-model="item.validaChoque"
          outlined
          dense
        ></v-switch>
      </v-col>
    </template>
  </wFormData>
</template>
