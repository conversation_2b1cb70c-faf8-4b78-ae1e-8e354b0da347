<script setup lang="ts">
import { ref } from 'vue';
// common components
import BaseBreadcrumb from '@/components/shared/BaseBreadcrumb.vue';
// template breadcrumb
const page = ref({ title: 'Iconify Icons' });
const breadcrumbs = ref([
    {
        text: 'Home',
        disabled: false,
        href: '#'
    },
    {
        text: 'Iconify icons',
        disabled: true,
        href: '#'
    }
]);
</script>
<template>
     <BaseBreadcrumb :title="page.title" :breadcrumbs="breadcrumbs"></BaseBreadcrumb>
    <v-row>
        <v-col cols="12" md="12">
            <v-card elevation="10">
                <v-card-text class="pa-5"> 
                <div class="pa-7 pt-0">
                    <iframe src="https://icon-sets.iconify.design/solar/" title="Inline Frame Example" frameborder="0" width="100%" height="650"></iframe>
                </div>
            </v-card-text>
            </v-card>
        </v-col>
    </v-row>
</template>