import { ref } from 'vue'

interface DecodedUser {
  usuaNombre: string
  email: string
  fotoPerfil: string
  rol?: string
  [key: string]: any
}

export function useDecodedUser() {
  const user = ref<DecodedUser | null>(null)

  const userString = localStorage.getItem('user')

  if (userString) {
    try {
      user.value = JSON.parse(userString)

      // Agregar el rol si está guardado por separado
      const rol = localStorage.getItem('rol')
      if (rol && user.value) {
        user.value.rol = rol
      }

    } catch (error) {
      console.error('❌ Error al parsear user del localStorage:', error)
    }
  }

  return {
    user
  }
}
