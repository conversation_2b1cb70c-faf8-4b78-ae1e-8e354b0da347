import { ref } from 'vue';
import type { OpcionAplicacion } from '@/utils/OpcionAplicacion';

interface BatchOperation {
  id: string;
  option: OpcionAplicacion;
  timestamp: number;
}

export const useBatchOperations = () => {
  const pendingOperations = ref<Map<number, BatchOperation>>(new Map());
  const isProcessing = ref(false);
  const debounceTimer = ref<NodeJS.Timeout | null>(null);
  const customProcessor = ref<((operations: BatchOperation[]) => Promise<any>) | null>(null);

  const DEBOUNCE_DELAY = 1000; // 1 segundo de delay
  const MAX_BATCH_SIZE = 10; // Máximo 10 operaciones por batch

  const setCustomProcessor = (processor: (operations: BatchOperation[]) => Promise<any>) => {
    customProcessor.value = processor;
  };

  const addOperation = (option: OpcionAplicacion) => {
    const operation: BatchOperation = {
      id: `${option.id}-${Date.now()}`,
      option: { ...option }, // Crear una copia para evitar referencias
      timestamp: Date.now()
    };

    // Agregar o actualizar la operación para esta opción
    pendingOperations.value.set(option.id, operation);

    // Limpiar el timer anterior si existe
    if (debounceTimer.value) {
      clearTimeout(debounceTimer.value);
    }

    // Configurar nuevo timer de debounce
    debounceTimer.value = setTimeout(() => {
      processBatch(customProcessor.value || undefined);
    }, DEBOUNCE_DELAY);

    // Si hay muchas operaciones pendientes, procesar inmediatamente
    if (pendingOperations.value.size >= MAX_BATCH_SIZE) {
      if (debounceTimer.value) {
        clearTimeout(debounceTimer.value);
        debounceTimer.value = null;
      }
      processBatch(customProcessor.value || undefined);
    }
  };

  const processBatch = async (customProcessor?: (operations: BatchOperation[]) => Promise<any>) => {
    if (isProcessing.value || pendingOperations.value.size === 0) {
      return;
    }

    isProcessing.value = true;
    const operations = Array.from(pendingOperations.value.values());
    pendingOperations.value.clear();

    try {
      let result;
      if (customProcessor) {
        result = await customProcessor(operations);
      } else {
        // Fallback por defecto
        result = {
          success: true,
          processedCount: operations.length
        };
      }

      return result;
    } catch (error) {
      console.error('Error processing batch operations:', error);
      return {
        success: false,
        error,
        processedCount: 0
      };
    } finally {
      isProcessing.value = false;
    }
  };

  const forceBatch = async (customProcessor?: (operations: BatchOperation[]) => Promise<any>) => {
    if (debounceTimer.value) {
      clearTimeout(debounceTimer.value);
      debounceTimer.value = null;
    }
    return await processBatch(customProcessor);
  };

  const clearPending = () => {
    if (debounceTimer.value) {
      clearTimeout(debounceTimer.value);
      debounceTimer.value = null;
    }
    pendingOperations.value.clear();
  };

  const getPendingCount = () => {
    return pendingOperations.value.size;
  };

  return {
    addOperation,
    processBatch,
    forceBatch,
    clearPending,
    getPendingCount,
    setCustomProcessor,
    isProcessing,
    pendingOperations: pendingOperations.value
  };
};
