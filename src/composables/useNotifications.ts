import { ref } from 'vue';

interface NotificationOptions {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  position?: 'top' | 'bottom' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

interface Notification extends NotificationOptions {
  id: string;
  visible: boolean;
}

const notifications = ref<Notification[]>([]);

export const useNotifications = () => {
  const showNotification = (options: NotificationOptions) => {
    const id = Date.now().toString();
    const notification: Notification = {
      id,
      visible: true,
      duration: 3000,
      position: 'top-right',
      ...options
    };

    notifications.value.push(notification);

    // Auto-hide after duration
    if (notification.duration && notification.duration > 0) {
      setTimeout(() => {
        hideNotification(id);
      }, notification.duration);
    }

    return id;
  };

  const hideNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id);
    if (index > -1) {
      notifications.value[index].visible = false;
      // Remove from array after animation
      setTimeout(() => {
        notifications.value.splice(index, 1);
      }, 300);
    }
  };

  const showSuccess = (message: string, duration?: number) => {
    return showNotification({ message, type: 'success', duration });
  };

  const showError = (message: string, duration?: number) => {
    return showNotification({ message, type: 'error', duration });
  };

  const showWarning = (message: string, duration?: number) => {
    return showNotification({ message, type: 'warning', duration });
  };

  const showInfo = (message: string, duration?: number) => {
    return showNotification({ message, type: 'info', duration });
  };

  const clearAll = () => {
    notifications.value = [];
  };

  return {
    notifications,
    showNotification,
    hideNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    clearAll
  };
};
