import { configure, defineRule } from "vee-validate";
import { localize, setLocale } from "@vee-validate/i18n";
import { required, email, min } from "@vee-validate/rules";
import es from "@vee-validate/i18n/dist/locale/es.json";

const veeValidate = () => {
  // Reglas básicas
  defineRule("required", required);
  defineRule("email", email);
  defineRule("min", min);

  // Validación personalizada: Contraseña fuerte
  defineRule("strong_password", (value: unknown) => {
    if (typeof value !== "string" || !value) {
      return "La contraseña es requerida.";
    }
    const strongRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{6,}$/;
    return strongRegex.test(value)
      ? true
      : "La contraseña debe tener al menos 1 minúscula, 1 mayúscula, 1 número, 1 carácter especial y mínimo 6 caracteres.";
  });

  // Validación personalizada: Fecha menor a actual
  defineRule("actual_date", (value: unknown) => {
    if (typeof value !== "string" || !value) {
      return "La fecha es requerida.";
    }
    const inputDate = new Date(value);
    const today = new Date();
    if (isNaN(inputDate.getTime())) {
      return "La fecha ingresada no es válida.";
    }
    today.setHours(0, 0, 0, 0);
    inputDate.setHours(0, 0, 0, 0);
    return inputDate < today
      ? true
      : "La fecha debe ser anterior a la fecha actual.";
  });

  // Configurar localización con manejo de errores
  try {
    configure({
      generateMessage: localize({
        es: {
          ...es,
          messages: {
            required: "El campo {field} es obligatorio.",
            email: "El campo {field} debe ser un correo válido.",
            min: "El campo {field} debe tener al menos {length} caracteres.",
            strong_password:
              "La contraseña debe tener al menos 1 minúscula, 1 mayúscula, 1 número, 1 carácter especial y mínimo 6 caracteres.",
            actual_date: "La fecha debe ser anterior a la fecha actual.",
          },
        },
      }),
      validateOnInput: true,
      validateOnBlur: true,
      validateOnChange: true,
    });

    setLocale("es");
  } catch (error) {
    console.error("Error al configurar vee-validate:", error);
    // Fallback a configuración en inglés si falla
    configure({
      generateMessage: localize("en"),
    });
    setLocale("en");
  }
};
export default veeValidate;
export { veeValidate };
