import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/academicos/estudiante-grado-obtenido";

const EstudianteGradoObtenidoService = {
  async getEstudianteGradosObtenidos(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching estudiante grado obtenido:", error);
      throw error;
    }
  },
  async getEstudianteGradoObtenido(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching estudiante grado obtenido:", error);
      throw error;
    }
  },
};

export default EstudianteGradoObtenidoService;
export { EstudianteGradoObtenidoService as EstudianteGradoObtenidoService };
