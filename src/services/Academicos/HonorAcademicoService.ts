import { apiQuery } from "../../utils/helpers/apiQuery";

const baseUrl = "api/academicos/honores-academicos";

const HonorAcademicoService = {
  async getHonoresAcademicos(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching honor académico:", error);
      throw error;
    }
  },
  async getHonorAcademicoId(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching honor académico:", error);
      throw error;
    }
  },
};

export default HonorAcademicoService;
export { HonorAcademicoService as HonorAcademicoService };
