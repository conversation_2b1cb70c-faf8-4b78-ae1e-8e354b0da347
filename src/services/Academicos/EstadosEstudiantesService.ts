import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/academicos/estados-estudiantes";

const EstadosEstudiantesService = {
  async getEstadosEstudiantes(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching estados estudiantes:", error);
      throw error;
    }
  },
  async getEstadoEstudianteById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching estados estudiantes:", error);
      throw error;
    }
  },
};

export default EstadosEstudiantesService;
export { EstadosEstudiantesService as EstadosEstudiantesService };
