import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/academicos/tipo-estudiante-mensaje";

const TipoEstudianteMensaje = {
  async getTiposEstudiantesMensajes(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo carrera:", error);
      throw error;
    }
  },
  async getTipoMensajeEstudianteById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo carrera:", error);
      throw error;
    }
  },
};

export default TipoEstudianteMensaje;
export { TipoEstudianteMensaje as TipoEstudianteMensajeService };
