import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/academicos/carreras/GetCarreras";

const CarreraService = {
  async getCarreras(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching motivos disciplina:", error);
      throw error;
    }
  },
  async getCarreraById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching motivos disciplina:", error);
      throw error;
    }
  },
};

export default CarreraService;
export { CarreraService as CarreraService };
