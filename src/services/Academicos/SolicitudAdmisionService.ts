import { apiQuery } from "@/utils/helpers/apiQuery";
import type { SolicitudAdmisionDto } from "@/utils/models/Academico/SolicitudAdmisionDto";

const baseUrl = "api/academicos/solicitudesAdmisiones";

const SolicitudAdmisionService = {
  async getSolicitudes(value = "", pageNumber = 1, pageSize = 10) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${encodeURIComponent(value)}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      return response;
    } catch (error) {
      console.error("Error al obtener las solicitudes de admisión:", error);
      throw error;
    }
  },

  async getSolicitudById(id: number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${id}`);
      return response;
    } catch (error) {
      console.error(`Error al obtener solicitud con ID ${id}:`, error);
      throw error;
    }
  },

  async createSolicitud(solicitud: SolicitudAdmisionDto) {
    try {
      const response = await apiQuery.post(baseUrl, solicitud);
      return response;
    } catch (error) {
      console.error("Error al crear la solicitud de admisión:", error);
      throw error;
    }
  },

  async updateSolicitud(id: number, solicitud: SolicitudAdmisionDto) {
    try {
      const response = await apiQuery.put(`${baseUrl}/${id}`, solicitud);
      return response;
    } catch (error) {
      console.error(`Error al actualizar solicitud con ID ${id}:`, error);
      throw error;
    }
  },

};

export default SolicitudAdmisionService;
export { SolicitudAdmisionService };
