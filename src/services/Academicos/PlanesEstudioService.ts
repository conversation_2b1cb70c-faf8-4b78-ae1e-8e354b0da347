import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/academicos/plan-estudio";

const PlanEstudioService = {
  async getPlanesEstudio(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching motivos disciplina:", error);
      throw error;
    }
  },
  async getPlanEstudioById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching motivos disciplina:", error);
      throw error;
    }
  },
};

export default PlanEstudioService;
export { PlanEstudioService as PlanEstudioService };
