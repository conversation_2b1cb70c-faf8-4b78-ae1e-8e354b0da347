import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/academicos/tipos-calificaciones-plantillas";

const TipoCalificacionPlantillaService = {
  async GetTipoCalificacionPlantilla(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching documentos:", error);
      throw error;
    }
  },
  async GetTipoCalificacionPlantillaById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching documentos:", error);
      throw error;
    }
  },
};

export default TipoCalificacionPlantillaService;
export { TipoCalificacionPlantillaService };