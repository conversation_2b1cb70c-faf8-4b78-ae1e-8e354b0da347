import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/academicos/carrera-tipo-integrante";

const CarreraTipoIntegranteService = {
  async getCarrerasTipoIntegrante(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching asignaturas:", error);
      throw error;
    }
  },
  async getCarreraTipoIntegranteById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching asignaturas:", error);
      throw error;
    }
  },
};

export default CarreraTipoIntegranteService;
export { CarreraTipoIntegranteService as CarreraTipoIntegranteService };
