import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/academicos/financiamientos-estudios";

const FinanciamientosEstudiosService = {
  async getFinanciamientosEstudios(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching financiamientos:", error);
      throw error;
    }
  },
  async getFinanciamientoEstudiosById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching financiamientos:", error);
      throw error;
    }
  },
};

export default FinanciamientosEstudiosService;
export { FinanciamientosEstudiosService as FinanciamientosEstudiosService };
