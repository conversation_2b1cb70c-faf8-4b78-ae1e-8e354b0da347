import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/academico/estados-solicitud";

const EstadosSolicitudAdmisionService = {
  async getEstadosSolicitudAdmision(
    value: string = "",
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${encodeURIComponent(value)}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) throw new Error("Network response was not ok");
      return response;
    } catch (error) {
      console.error("Error fetching estados de solicitud de admisión:", error);
      throw error;
    }
  },

  async getEstadoSolicitudAdmisionById(id: number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${id}`);
      if (!response) throw new Error("Network response was not ok");
      return response;
    } catch (error) {
      console.error(`Error fetching estado de solicitud con ID ${id}:`, error);
      throw error;
    }
  },

  async addEstadoSolicitudAdmision(payload: any) {
    try {
      const response = await apiQuery.post(`${baseUrl}`, payload);
      if (!response) throw new Error("Network response was not ok");
      return response;
    } catch (error) {
      console.error("Error adding estado de solicitud de admisión:", error);
      throw error;
    }
  },

};

export default EstadosSolicitudAdmisionService;
export { EstadosSolicitudAdmisionService };
