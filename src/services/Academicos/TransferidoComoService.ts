import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/academicos/transferido-como";

const TransferidoComoService = {
  async getTransferidosComo(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo graduación:", error);
      throw error;
    }
  },
  async getTransferidoComoById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo graduación:", error);
      throw error;
    }
  },
};

export default TransferidoComoService;
export { TransferidoComoService as TransferidoComoService };
