import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/academicos/asignaturas";

const AsignaturasService = {
  async getAsignaturas(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching asignaturas:", error);
      throw error;
    }
  },
  async getAsignatura(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching asignaturas:", error);
      throw error;
    }
  },
};

export default AsignaturasService;
export { AsignaturasService as AsignaturasService };
