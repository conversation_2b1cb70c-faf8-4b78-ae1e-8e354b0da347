import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/seleccion/tipo-excepcion-seleccion";

const TipoExcepcionSeleccionService = {
  async getTiposExcepcionesSeleccion(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching estados estudiantes:", error);
      throw error;
    }
  },
  async getTipoExcepcionSeleccionbyId(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching estados estudiantes:", error);
      throw error;
    }
  },
};

export default TipoExcepcionSeleccionService;
export { TipoExcepcionSeleccionService as TipoExcepcionSeleccionService };
