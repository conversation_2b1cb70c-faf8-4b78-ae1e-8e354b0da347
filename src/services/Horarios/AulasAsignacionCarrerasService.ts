import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/horarios/aula-asignacion-carrera";

const AulaAsignacionCarreraService = {
  async getAulaAsignacionCarreras(
    value: string,
    carrera: number,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}/${carrera}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching aulas asignaciones carreras:", error);
      throw error;
    }
  },
  async getAulaAsignacionCarreraById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/aula/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching aula asignación carrera:", error);
      throw error;
    }
  },
};

export default AulaAsignacionCarreraService;
export { AulaAsignacionCarreraService as AulaAsignacionCarreraService };
