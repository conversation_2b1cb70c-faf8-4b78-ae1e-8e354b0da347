import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/horarios/dias";

const DiaService = {
  async getDias(value: string, pageNumber: number = 1, pageSize: number = 10) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching aulas asignaciones carreras:", error);
      throw error;
    }
  },
  async getDiaById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching aula asignación carrera:", error);
      throw error;
    }
  },
};

export default DiaService;
export { DiaService as DiaService };
