

import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/financieros/profesor-categoria-pago";

const  categoriaProfesorService = {

      async searchItem(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ) {
        try {
          const response = await apiQuery.get(
            `${baseUrl}/GetProfesorCategoriaPago?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      },

      async monedaPago(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ){
        try {
          const response = await apiQuery.get(
            `api/financieros/monedas-pagos/GetMonedaPagos?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      }
}

export default categoriaProfesorService;
export { categoriaProfesorService }