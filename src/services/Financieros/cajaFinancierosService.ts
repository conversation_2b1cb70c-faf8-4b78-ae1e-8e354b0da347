
import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/financieros/cajas";

const  cajaService = {

      async searchItem(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ) {
        try {
          const response = await apiQuery.get(
            `${baseUrl}/GetCajas?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      },

      async getSede(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ) {
        try {
          const response = await apiQuery.get(
            `api/planta-fisica/sedes?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      }
}

export default cajaService;
export { cajaService }