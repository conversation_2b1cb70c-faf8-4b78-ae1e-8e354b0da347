

import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/financieros/AulaPago";

const  aulaPagoService = {

      async searchItem(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ) {
        try {
          const response = await apiQuery.get(
            `${baseUrl}/GetAulaPago?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      },

      async getSede(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ){
        try {
          const response = await apiQuery.get(
            `api/planta-fisica/sedes?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      },

      async getAula(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ){
        try {
          const response = await apiQuery.get(
            `api/planta-fisica/aulas?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      },

      async getCarrera(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ){
        try {
          const response = await apiQuery.get(
            `api/academicos/carreras/GetCarreras?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      },

      async getMonedaPago(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ){
        try {
          const response = await apiQuery.get(
            `api/financieros/monedas-pagos/GetMonedaPagos?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      }
}

export default aulaPagoService;
export { aulaPagoService }