

import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/financieros/estudiante-descuento-pago";

const  estudiantePagoService = {

      async searchItem(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ) {
        try {
          const response = await apiQuery.get(
            `${baseUrl}/GetEstudiante?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      },

    async getEstudianteDescuentoById(
      estudianteId: number,
      pageNumber: number = 1,
      pageSize: number = 10)
    {
        try {
          const response = await apiQuery.get(
            `${baseUrl}/GetEstudianteDescuentoPago?value=${estudianteId}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
    },

    async getTipoDescuento(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ) {
        try {
          const response = await apiQuery.get(
            `api/financieros/tipos-descuentos/GetTiposDescuentos?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      },

    async getJustificacionesDescuento(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ) {
        try {
          const response = await apiQuery.get(
            `api/financieros/descuentos-justificaciones/GetDescuentosJustificaciones?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      },

    async getEmpresa(
      value: string,
      pageNumber: number = 1,
      pageSize: number = 10
    ){
        try {
          const response = await apiQuery.get(
            `api/generales/empresas/GetEmpresas?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
    },
        async getPerfilEstudiante(estudianteId: number)
    {
        try {
          const response = await apiQuery.get(
            `${baseUrl}/${estudianteId}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
    },
    }

export default estudiantePagoService;
export { estudiantePagoService }