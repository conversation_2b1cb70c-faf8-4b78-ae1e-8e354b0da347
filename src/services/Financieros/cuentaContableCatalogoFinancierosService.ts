

import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/financieros/catalogo-cuenta";

const  cuentaContableCatologoService = {

      async searchItem(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ) {
        try {
          const response = await apiQuery.get(
            `${baseUrl}/GetCuentaContableCatalogos?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      },

      async getTipoCuentaContable(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10

      ){
        try {
          const response = await apiQuery.get(
            `api/financieros/tipo-cuenta-contable/GetTipoCatalogoCuentaContableFinancieros/?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      },

      async getCuentaContableOrigen(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ){
        try {
          const response = await apiQuery.get(
            `api/financieros/cuenta-contable-origen/GetCuentaContableOrigenFinancieros/?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      }
}

export default cuentaContableCatologoService;
export { cuentaContableCatologoService }