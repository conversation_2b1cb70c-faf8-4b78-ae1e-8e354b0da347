import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/generales/tipo-telefono";

const TipoTelefonoService = {
  async getTiposTelefono(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo teléfono:", error);
      throw error;
    }
  },
  async getTipoTelefonoById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo teléfono:", error);
      throw error;
    }
  },
};

export default TipoTelefonoService;
export { TipoTelefonoService as TipoTelefonoService };
