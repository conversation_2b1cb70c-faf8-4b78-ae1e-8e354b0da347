import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/generales/tipos-empresas";

const  tipoEmpresaSevice = {

      async searchItem(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ) {
        try {
          const response = await apiQuery.get(
            `${baseUrl}/GetTipoEmpresa?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      },
    }

export default tipoEmpresaSevice;
export { tipoEmpresaSevice }