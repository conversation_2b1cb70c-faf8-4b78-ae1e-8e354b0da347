import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/generales/tipo-documento-identificacion";

const TipoDocumentoIdentificacionService = {
  async getTiposDocumentoIdentificacion(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo documento identificación:", error);
      throw error;
    }
  },
  async getTipoDocumentoIdentificacionById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo documento identificación:", error);
      throw error;
    }
  },
};

export default TipoDocumentoIdentificacionService;
export { TipoDocumentoIdentificacionService as TipoDocumentoIdentificacionService };
