import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/generales/estado-civil";

const EstadoCivilService = {
  async getEstadosCiviles(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching estado civil:", error);
      throw error;
    }
  },
  async GetEstadoCivilById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching estado civil:", error);
      throw error;
    }
  },
};

export default EstadoCivilService;
export { EstadoCivilService as EstadoCivilService };
