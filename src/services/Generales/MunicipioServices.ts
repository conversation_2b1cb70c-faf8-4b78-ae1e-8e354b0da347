import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/generales/municipios";

const MunicipioServices = {
  async getMunicipios(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo asignatura:", error);
      throw error;
    }
  },
  async getMunicipioById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo asignatura:", error);
      throw error;
    }
  },
  async getMunicipiosByPaisId(paisId: Number) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?paisId=${paisId}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching municipios by pais:", error);
      throw error;
    }
  },
};

export default MunicipioServices;
export { MunicipioServices };
