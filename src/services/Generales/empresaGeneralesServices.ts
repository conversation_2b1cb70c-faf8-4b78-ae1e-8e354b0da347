
import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/generales/empresas";

const  empresaSevice = {

      async searchItem(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ) {
        try {
          const response = await apiQuery.get(
            `${baseUrl}/GetEmpresas?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      },

      async getTiposEmpresa(
        value: string,
        pageNumber: number = 1,
        pageSize: number = 10
      ){
        try {
          const response = await apiQuery.get(
            `api/generales/tipos-empresas/GetTipoEmpresa?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
          );
          if (!response) {
            throw new Error("Network response was not ok");
          }
          return response;
        } catch (error) {
          console.error("Error fetching documentos:", error);
          throw error;
        }
      }

    }

export default empresaSevice;
export { empresaSevice }