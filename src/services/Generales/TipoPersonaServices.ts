import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/generales/tipo-persona";

const TipoPersonaService = {
  async getTiposPersona(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo persona:", error);
      throw error;
    }
  },
  async getTipoPersonaById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo persona:", error);
      throw error;
    }
  },
};

export default TipoPersonaService;
export { TipoPersonaService as TipoPersonaService };
