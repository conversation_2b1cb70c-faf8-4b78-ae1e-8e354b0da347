import { apiQuery } from "../../utils/helpers/apiQuery";

const baseUrl = "api/generales/tipo-padre";

const TipoPadreService = {
  async getTiposPadre(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo padre:", error);
      throw error;
    }
  },
  async getTipoPadreById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo padre:", error);
      throw error;
    }
  },
};

export default TipoPadreService;
export { TipoPadreService as TipoPadreService };
