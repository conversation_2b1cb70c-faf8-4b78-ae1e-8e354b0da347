import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/generales/tipo-sangre";

const TipoSangreService = {
  async getTipoSangre(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo de sangre:", error);
      throw error;
    }
  },
  async getTipoSangreById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo asignatura:", error);
      throw error;
    }
  },
};

export default TipoSangreService;
export { TipoSangreService };
