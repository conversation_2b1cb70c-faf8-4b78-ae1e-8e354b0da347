import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/generales/ocupaciones";

const OcupacionesService = {
  async getOcupaciones(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching ocupación:", error);
      throw error;
    }
  },
  async getOcupacionById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching ocupación:", error);
      throw error;
    }
  },
};

export default OcupacionesService;
export { OcupacionesService as OcupacionesService };
