import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/generales/motivo";

const MotivoService = {
  async getMotivos(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching motivo:", error);
      throw error;
    }
  },
  async getMotivoById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching motivo:", error);
      throw error;
    }
  },
  async getTiposMotivo() {
    try {
      const response = await apiQuery.get(`api/generales/tipos-motivo`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching tipo motivo:", error);
      throw error;
    }
  },
};

export default MotivoService;
export { MotivoService as MotivoService };
