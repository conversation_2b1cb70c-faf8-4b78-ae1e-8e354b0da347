import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/generales/cargos";

const CargosService = {
  async getCargos(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching estado ciudadano:", error);
      throw error;
    }
  },
  async getCargoById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching estado ciudadano:", error);
      throw error;
    }
  },
};

export default CargosService;
export { CargosService as CargosService };
