import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/security/usuarios";

const UsuariosService = {
  async getUsuarios(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching nacionalidad:", error);
      throw error;
    }
  },
  async getUsuariosById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching nacionalidad:", error);
      throw error;
    }
  },
};

export default UsuariosService;
export { UsuariosService as UsuariosService };
