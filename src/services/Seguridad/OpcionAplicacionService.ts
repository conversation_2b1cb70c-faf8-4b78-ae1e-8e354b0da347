import { apiQuery } from "@/utils/helpers/apiQuery";
import type { OpcionAplicacion } from "@/utils/OpcionAplicacion";

const baseUrl = "api/security/opcionaplicacion";
const rolPermisoUrl = "api/security/opcionaplicacion/rol-permiso";

const OpcionAplicacionService = {
  async getOpcionesAplicacion() {
    try {
      const response = await apiQuery.get(baseUrl);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching opciones aplicacion:", error);
      throw error;
    }
  },

  async getOpcionesAplicacionByRol(rolId: number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/rol/${rolId}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching opciones aplicacion by rol:", error);
      throw error;
    }
  },

  async saveRolPermiso(rolId: number, opcion: OpcionAplicacion) {
    try {
      // Preparar la estructura de datos según el formato requerido por el endpoint rol-permiso
      const payload = {
        rolId: rolId,
        opcionAplicacionId: opcion.id,
        roPeLeer: opcion.permisos.ver,
        roPeImprimir: opcion.permisos.imprimir,
        roPeAgregar: opcion.permisos.crear,
        roPeEliminar: opcion.permisos.eliminar,
        roPeModificar: opcion.permisos.editar
      };

      const response = await apiQuery.post(rolPermisoUrl, payload);

      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error saving rol permiso:", error);
      throw error;
    }
  },

  // Mantener la función original para compatibilidad si es necesaria
  async saveOpcionAplicacion(opcion: OpcionAplicacion, rolId?: number) {
    if (rolId) {
      return this.saveRolPermiso(rolId, opcion);
    }

    try {
      // Preparar la estructura de datos según el formato requerido
      const payload = {
        id: opcion.id,
        descripcion: opcion.descripcion,
        descripcionIngles: opcion.decripcionIngles || opcion.descripcion, // Usar descripcion como fallback
        icon: opcion.icon || "",
        url: opcion.url || "",
        padre: opcion.padre || 0,
        orden: opcion.orden || 0,
        activa: opcion.activa,
        permisos: {
          crear: opcion.permisos.crear,
          eliminar: opcion.permisos.eliminar,
          imprimir: opcion.permisos.imprimir,
          ver: opcion.permisos.ver,
          editar: opcion.permisos.editar
        },
        subOpciones: opcion.subOpciones?.map(sub => sub.descripcion) || []
      };

      let response;
      if (opcion.id && opcion.id > 0) {
        // Actualizar registro existente
        response = await apiQuery.put(`${baseUrl}/${opcion.id}`, payload);
      } else {
        // Crear nuevo registro
        response = await apiQuery.post(baseUrl, payload);
      }

      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error saving opcion aplicacion:", error);
      throw error;
    }
  },

  async updateOpcionAplicacion(opcion: OpcionAplicacion) {
    try {
      // Preparar la estructura de datos según el formato requerido
      const payload = {
        id: opcion.id,
        descripcion: opcion.descripcion,
        descripcionIngles: opcion.decripcionIngles || opcion.descripcion,
        icon: opcion.icon || "",
        url: opcion.url || "",
        padre: opcion.padre || 0,
        orden: opcion.orden || 0,
        activa: opcion.activa,
        permisos: {
          crear: opcion.permisos.crear,
          eliminar: opcion.permisos.eliminar,
          imprimir: opcion.permisos.imprimir,
          ver: opcion.permisos.ver,
          editar: opcion.permisos.editar
        },
        subOpciones: opcion.subOpciones?.map(sub => sub.descripcion) || []
      };

      const response = await apiQuery.put(`${baseUrl}/${opcion.id}`, payload);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error updating opcion aplicacion:", error);
      throw error;
    }
  },

  async saveBatchRolPermisos(rolId: number, opciones: OpcionAplicacion[]) {
    try {
      // Preparar todas las operaciones usando el endpoint rol-permiso
      const operations = opciones.map(opcion => {
        const payload = {
          rolId: rolId,
          opcionAplicacionId: opcion.id,
          roPeLeer: opcion.permisos.ver,
          roPeImprimir: opcion.permisos.imprimir,
          roPeAgregar: opcion.permisos.crear,
          roPeEliminar: opcion.permisos.eliminar,
          roPeModificar: opcion.permisos.editar
        };

        return apiQuery.post(rolPermisoUrl, payload);
      });

      // Ejecutar todas las operaciones en paralelo
      const results = await Promise.allSettled(operations);

      // Analizar resultados
      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.filter(result => result.status === 'rejected').length;

      if (failed > 0) {
        console.warn(`Batch operation completed with ${failed} failures out of ${opciones.length} operations`);
      }

      return {
        total: opciones.length,
        successful,
        failed,
        results
      };
    } catch (error) {
      console.error("Error in batch save rol permisos:", error);
      throw error;
    }
  },

  // Mantener función original para compatibilidad
  async saveBatchOpcionesAplicacion(opciones: OpcionAplicacion[], rolId?: number) {
    if (rolId) {
      return this.saveBatchRolPermisos(rolId, opciones);
    }

    try {
      // Preparar todas las operaciones
      const operations = opciones.map(opcion => {
        const payload = {
          id: opcion.id,
          descripcion: opcion.descripcion,
          descripcionIngles: opcion.decripcionIngles || opcion.descripcion,
          icon: opcion.icon || "",
          url: opcion.url || "",
          padre: opcion.padre || 0,
          orden: opcion.orden || 0,
          activa: opcion.activa,
          permisos: {
            crear: opcion.permisos.crear,
            eliminar: opcion.permisos.eliminar,
            imprimir: opcion.permisos.imprimir,
            ver: opcion.permisos.ver,
            editar: opcion.permisos.editar
          },
          subOpciones: opcion.subOpciones?.map(sub => sub.descripcion) || []
        };

        // Determinar si es PUT o POST
        if (opcion.id && opcion.id > 0) {
          return apiQuery.put(`${baseUrl}/${opcion.id}`, payload);
        } else {
          return apiQuery.post(baseUrl, payload);
        }
      });

      // Ejecutar todas las operaciones en paralelo
      const results = await Promise.allSettled(operations);

      // Analizar resultados
      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.filter(result => result.status === 'rejected').length;

      if (failed > 0) {
        console.warn(`Batch operation completed with ${failed} failures out of ${opciones.length} operations`);
      }

      return {
        total: opciones.length,
        successful,
        failed,
        results
      };
    } catch (error) {
      console.error("Error in batch save opciones aplicacion:", error);
      throw error;
    }
  }
};

export default OpcionAplicacionService;
export { OpcionAplicacionService };
