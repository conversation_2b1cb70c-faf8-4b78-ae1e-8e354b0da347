import type { PaginResponse } from "@/utils/PaginResponse";
import { apiQuery } from "@/utils/helpers/apiQuery";

const baseUrl = "api/security/roles";

const RolService = {
    async searchItem(value: string, pageNumber: number, pageSize: number): Promise<PaginResponse> {
        const response = await apiQuery.get(
            `api/security/roles?searchValue=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
        );
        return response;
    },
    
  async getRoleById(id: string) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error(`Error fetching role with ID ${id}:`, error);
      throw error;
    }
  },

  async getRoles() {
    try {
      const response = await apiQuery.get(baseUrl);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching roles:", error);
      throw error;
    }
  },
};

export default RolService;
export { RolService }; 