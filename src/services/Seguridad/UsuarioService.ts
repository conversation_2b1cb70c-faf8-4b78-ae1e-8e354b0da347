import { apiQuery } from "@/utils/helpers/apiQuery";
import type { Usuario } from "@/utils/models/Seguridad/Usuario";

const baseUrl = "api/security/usuarios";
const completoUrl = "api/security/usuarios/completo";

const UsuarioService = {
  async getUsuarios(
    value: string,
    pageNumber: number = 1,
    pageSize: number = 10
  ) {
    try {
      const response = await apiQuery.get(
        `${baseUrl}?value=${value}&pageNumber=${pageNumber}&pageSize=${pageSize}`
      );
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching usuarios:", error);
      throw error;
    }
  },
  async getUsuarioById(Id: Number) {
    try {
      const response = await apiQuery.get(`${baseUrl}/${Id}`);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error fetching usuario:", error);
      throw error;
    }
  },

  async createUsuarioCompleto(usuarioData: any) {
    try {
      const response = await apiQuery.post(completoUrl, usuarioData);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error creating usuario completo:", error);
      throw error;
    }
  },

  async updateUsuarioCompleto(id: number, usuarioData: any) {
    try {
      const response = await apiQuery.put(`${completoUrl}/${id}`, usuarioData);
      if (!response) {
        throw new Error("Network response was not ok");
      }
      return response;
    } catch (error) {
      console.error("Error updating usuario completo:", error);
      throw error;
    }
  },

  // Función para transformar los datos del usuario al formato requerido por la API
  transformUsuarioToApiFormat(usuario: any) {
    return {
      usuario: usuario.usuario || "",
      correo: usuario.correo || usuario.usuaEmail || "",
      estado: usuario.estado !== undefined ? usuario.estado : usuario.estatus,
      nombres: usuario.nombres || usuario.persona?.nombres || "",
      apellidos: usuario.apellidos || usuario.persona?.apellidos || "",
      rolId: usuario.rolId || 0,
      activo: usuario.activo !== undefined ? usuario.activo : usuario.estatus,
      fechaVencimientoClave: usuario.fechaVencimientoClave || usuario.usuaClaveVence_Fecha || null,
      cambiarClave: usuario.cambiarClave !== undefined ? usuario.cambiarClave : usuario.usuaCambiaClave || false,
      persona: {
        nombres: usuario.persona?.nombres || usuario.nombres || "",
        apellidos: usuario.persona?.apellidos || usuario.apellidos || "",
        tipoDocumentoId: usuario.persona?.tipoDocumentoId || usuario.tipoDocumento || 1,
        noDocumento: usuario.persona?.noDocumento || usuario.documento?.toString() || "",
        sexoId: usuario.persona?.sexoId || 1,
        estadoCivilId: usuario.persona?.estadoCivilId || 1,
        fechaNacimiento: usuario.persona?.fechaNacimiento || null,
        telefono: usuario.persona?.telefono || usuario.telefono || "",
        nacionalidadId: usuario.persona?.nacionalidadId || 1,
        paisId: usuario.persona?.paisId || 1,
        provinciaId: usuario.persona?.provinciaId || 1,
        municipioId: usuario.persona?.municipioId || 1,
        ciudadId: usuario.persona?.ciudadId || 1,
        tipoSangreId: usuario.persona?.tipoSangreId || 1,
        alergias: usuario.persona?.alergias || "",
        fotoUrl: usuario.persona?.fotoUrl || usuario.foto || ""
      },
      direccion: {
        tipoDireccionId: usuario.direccion?.tipoDireccionId || 0,
        direccion: usuario.direccion?.direccion || "",
        referencia: usuario.direccion?.referencia || null,
        paisId: usuario.direccion?.paisId || usuario.persona?.paisId || 1,
        provinciaId: usuario.direccion?.provinciaId || usuario.persona?.provinciaId || 1,
        municipioId: usuario.direccion?.municipioId || usuario.persona?.municipioId || 1,
        ciudadId: usuario.direccion?.ciudadId || usuario.persona?.ciudadId || 1,
        telefono: usuario.direccion?.telefono || null,
        codigoPostal: usuario.direccion?.codigoPostal || null
      },
      sedes: usuario.sedes || []
    };
  },
};

export default UsuarioService;
export { UsuarioService };