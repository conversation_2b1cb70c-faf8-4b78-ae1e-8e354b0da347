import type { faqPageType } from '@/types/components/pages/faqData';
const faqPage: faqPageType[] = [
    {
        question: 'What is an Admin Dashboard?',
        answer: 'Admin Dashboard is the backend interface of a website or an application that helps to manage the website s overall content and settings. It is widely used by the site owners to keep track of their website, make changes to their content, and more.'
    },
    {
        question: 'What should an admin dashboard template include?',
        answer: 'Admin dashboard template should include user & SEO friendly design with a variety of components and application designs to help create your own web application with ease. This could include customization options, technical support and about 6 months of future updates.'
    },
    {
        question: 'Why should I buy admin templates from AdminMart?',
        answer: 'Adminmart offers high-quality templates that are easy to use and fully customizable. With over 101,801 happy customers & trusted by 10,000+ Companies. AdminMart is recognized as the leading online source for buying admin templates.'
    },
    {
        question: 'Do Adminmart offers a money back guarantee?',
        answer: 'There is no money back guarantee in most companies, but if you are unhappy with our product, Adminmart gives you a 100% money back guarantee.'
    }
];

export { faqPage};
