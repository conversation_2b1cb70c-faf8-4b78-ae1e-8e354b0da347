import type { SearchLinkType } from '@/types/components/pages/searchLinks';
const SearchLinkData: SearchLinkType[] = [
    {
        title: 'AngularJs',
        linktitle: 'http://www.google.com/angularjs',
        subtitle:'Lorem Ipsum viveremus probamus opus apeirian haec perveniri, memoriter.Praebeat pecunias viveremus probamus opus apeirian haec perveniri, memoriter.',
        url:'/'
    },
    {
        title: 'AngularJs — Superheroic JavaScript MVW Framework',
        linktitle: 'http://www.google.com/angularjs',
        subtitle:'Lorem Ipsum viveremus probamus opus apeirian haec perveniri, memoriter.Praebeat pecunias viveremus probamus opus apeirian haec perveniri, memoriter.',
        url:'/'
    },
    {
        title: 'AngularJS Tutorial - W3Schools',
        linktitle: 'http://www.google.com/angularjs',
        subtitle:'Lorem Ipsum viveremus probamus opus apeirian haec perveniri, memoriter.Praebeat pecunias viveremus probamus opus apeirian haec perveniri, memoriter.',
        url:'/'
    },
    {
        title: 'Introduction to AngularJS - W3Schools',
        linktitle: 'http://www.google.com/angularjs',
        subtitle:'Lorem Ipsum viveremus probamus opus apeirian haec perveniri, memoriter.Praebeat pecunias viveremus probamus opus apeirian haec perveniri, memoriter.',
        url:'/'
    },
    {
        title: 'AngularJS Tutorial',
        linktitle: 'http://www.google.com/angularjs',
        subtitle:'Lorem Ipsum viveremus probamus opus apeirian haec perveniri, memoriter.Praebeat pecunias viveremus probamus opus apeirian haec perveniri, memoriter.',
        url:'/'
    },
    {
        title: 'Angular 2: One framework',
        linktitle: 'http://www.google.com/angularjs',
        subtitle:'Lorem Ipsum viveremus probamus opus apeirian haec perveniri, memoriter.Praebeat pecunias viveremus probamus opus apeirian haec perveniri, memoriter.',
        url:'/'
    },
   
];

export { SearchLinkData};
