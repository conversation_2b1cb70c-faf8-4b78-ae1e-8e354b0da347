import type { ThemeTypes } from "@/types/themeTypes/ThemeType";

const DARK_BLUE_THEME: ThemeTypes = {
  name: "DARK_BLUE_THEME",
  dark: true,
  variables: {
    "border-color": "#333F55",
    "border-opacity": 1,
  },
  colors: {
    primary: "#003d99",
    secondary: "#14E9E2",
    info: "#3CACC8",
    success: "#13DEB9",
    warning: "#FFAE1F",
    error: "#cc0000",
    lightprimary: "#29305F",
    lightsecondary: "#194D59",
    lightsuccess: "#1B3C48",
    lighterror: "#4B313D",
    lightinfo: "#274A60",
    lightwarning: "#4D3A2A",
    textPrimary: "#EAEFF4",
    textSecondary: "#7C8FAC",
    borderColor: "#333F55",
    containerBg: "#1A2537",
    background: "#1F2A3D",
    hoverColor: "#333f55",
    surface: "#1a2537",
    grey100: "#333F55",
    grey200: "#465670",
    light: "#1F2A3D",
    muted: "#babfc3",
    darkgray: "#404A5A",
  },
};

const DARK_AQUA_THEME: ThemeTypes = {
  name: "DARK_AQUA_THEME",
  dark: true,
  variables: {
    "border-color": "#333F55",
    "border-opacity": 1,
  },
  colors: {
    primary: "#0074BA",
    secondary: "#47D7BC",
    info: "#3CACC8",
    success: "#13DEB9",
    warning: "#FFAE1F",
    error: "#ff6692",
    lightprimary: "#103247",
    lightsecondary: "#0C4339",
    lightsuccess: "#1B3C48",
    lighterror: "#4B313D",
    lightinfo: "#274A60",
    lightwarning: "#4D3A2A",
    textPrimary: "#EAEFF4",
    textSecondary: "#7C8FAC",
    borderColor: "#333F55",
    containerBg: "#1A2537",
    background: "#1F2A3D",
    hoverColor: "#333f55",
    surface: "#1a2537",
    grey100: "#333F55",
    grey200: "#465670",
    light: "#1F2A3D",
    muted: "#babfc3",
    darkgray: "#404A5A",
  },
};

const DARK_PURPLE_THEME: ThemeTypes = {
  name: "DARK_PURPLE_THEME",
  dark: true,
  variables: {
    "border-color": "#333F55",
    "border-opacity": 1,
  },
  colors: {
    primary: "#763EBD",
    secondary: "#95CFD5",
    info: "#3CACC8",
    success: "#13DEB9",
    warning: "#FFAE1F",
    error: "#ff6692",
    lightprimary: "#26153C",
    lightsecondary: "#09454B",
    lightsuccess: "#1B3C48",
    lighterror: "#4B313D",
    lightinfo: "#274A60",
    lightwarning: "#4D3A2A",
    textPrimary: "#EAEFF4",
    textSecondary: "#7C8FAC",
    borderColor: "#333F55",
    containerBg: "#1A2537",
    background: "#1F2A3D",
    hoverColor: "#333f55",
    surface: "#1a2537",
    grey100: "#333F55",
    grey200: "#465670",
    light: "#1F2A3D",
    muted: "#babfc3",
    darkgray: "#404A5A",
  },
};

const DARK_GREEN_THEME: ThemeTypes = {
  name: "DARK_GREEN_THEME",
  dark: true,
  variables: {
    "border-color": "#333F55",
    "border-opacity": 1,
  },
  colors: {
    primary: "#0A7EA4",
    secondary: "#CCDA4E",
    info: "#3CACC8",
    success: "#13DEB9",
    warning: "#FFAE1F",
    error: "#ff6692",
    lightprimary: "#05313F",
    lightsecondary: "#282917",
    lightsuccess: "#1B3C48",
    lighterror: "#4B313D",
    lightinfo: "#274A60",
    lightwarning: "#4D3A2A",
    textPrimary: "#EAEFF4",
    textSecondary: "#7C8FAC",
    borderColor: "#333F55",
    containerBg: "#1A2537",
    background: "#1F2A3D",
    hoverColor: "#333f55",
    surface: "#1a2537",
    grey100: "#333F55",
    grey200: "#465670",
    light: "#1F2A3D",
    muted: "#babfc3",
    darkgray: "#404A5A",
  },
};

const DARK_CYAN_THEME: ThemeTypes = {
  name: "DARK_CYAN_THEME",
  dark: true,
  variables: {
    "border-color": "#333F55",
    "border-opacity": 1,
  },
  colors: {
    primary: "#01C0C8",
    secondary: "#FB9678",
    info: "#3CACC8",
    success: "#13DEB9",
    warning: "#FFAE1F",
    error: "#ff6692",
    lightprimary: "#003638",
    lightsecondary: "#40241C",
    lightsuccess: "#1B3C48",
    lighterror: "#4B313D",
    lightinfo: "#274A60",
    lightwarning: "#4D3A2A",
    textPrimary: "#EAEFF4",
    textSecondary: "#7C8FAC",
    borderColor: "#333F55",
    containerBg: "#1A2537",
    background: "#1F2A3D",
    hoverColor: "#333f55",
    surface: "#1a2537",
    grey100: "#333F55",
    grey200: "#465670",
    light: "#1F2A3D",
    muted: "#babfc3",
    darkgray: "#404A5A",
  },
};

const DARK_ORANGE_THEME: ThemeTypes = {
  name: "DARK_ORANGE_THEME",
  dark: true,
  variables: {
    "border-color": "#333F55",
    "border-opacity": 1,
  },
  colors: {
    primary: "#FA896B",
    secondary: "#0074BA",
    info: "#3CACC8",
    success: "#13DEB9",
    warning: "#FFAE1F",
    error: "#ff6692",
    lightprimary: "#402E32",
    lightsecondary: "#082E45",
    lightsuccess: "#1B3C48",
    lighterror: "#4B313D",
    lightinfo: "#274A60",
    lightwarning: "#4D3A2A",
    textPrimary: "#EAEFF4",
    textSecondary: "#7C8FAC",
    borderColor: "#333F55",
    containerBg: "#1A2537",
    background: "#1F2A3D",
    hoverColor: "#333f55",
    surface: "#1a2537",
    grey100: "#333F55",
    grey200: "#465670",
    light: "#1F2A3D",
    muted: "#babfc3",
    darkgray: "#404A5A",
  },
};

export {
  DARK_BLUE_THEME,
  DARK_AQUA_THEME,
  DARK_ORANGE_THEME,
  DARK_PURPLE_THEME,
  DARK_GREEN_THEME,
  DARK_CYAN_THEME,
};
