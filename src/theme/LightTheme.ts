import type { ThemeTypes } from "@/types/themeTypes/ThemeType";

const BLUE_THEME: ThemeTypes = {
  name: "BLUE_THEME",
  dark: false,
  variables: {
    "border-color": "#e0e6eb",
    "border-opacity": 1,
  },
  colors: {
    primary: "#003d99",
    secondary: "#16CDC7",
    info: "#46caeb",
    success: "#36c76c",
    warning: "#ffd648",
    error: "#cc0000",
    lightprimary: "#DDDBFF",
    lightsecondary: "#A6F7F5",
    lightsuccess: "#E1F7E9",
    lighterror: "#FFCCDB",
    lightinfo: "#DAF4FB",
    lightwarning: "#FFF9E5",
    textPrimary: "#29343D",
    textSecondary: "#98A4AE",
    borderColor: "#e0e6eb",
    containerBg: "#ffffff",
    background: "#F4F7FB",
    hoverColor: "#f6f9fc",
    surface: "#fff",
    grey100: "#F6F7F9",
    grey200: "#29343d",
    darkgray: "#0a2540",
    light: "#EFF4FA",
    muted: "#526b7a",
  },
};

const AQUA_THEME: ThemeTypes = {
  name: "AQUA_THEME",
  dark: false,
  variables: {
    "border-color": "#e0e6eb",
    "border-opacity": 1,
  },
  colors: {
    primary: "#0074BA",
    secondary: "#47D7BC",
    info: "#539BFF",
    success: "#13DEB9",
    warning: "#FFAE1F",
    error: "#ff6692",
    lightprimary: "#EFF9FF",
    lightsecondary: "#EDFBF7",
    lightsuccess: "#E6FFFA",
    lighterror: "#FDEDE8",
    lightinfo: "#EBF3FE",
    lightwarning: "#FEF5E5",
    textPrimary: "#29343D",
    textSecondary: "#98A4AE",
    borderColor: "#e5eaef",
    containerBg: "#ffffff",
    background: "#F4F7FB",
    hoverColor: "#f6f9fc",
    surface: "#fff",
    grey100: "#F6F7F9",
    grey200: "#29343d",
    darkgray: "#0a2540",
    light: "#EFF4FA",
    muted: "#526b7a",
  },
};

const PURPLE_THEME: ThemeTypes = {
  name: "PURPLE_THEME",
  dark: false,
  variables: {
    "border-color": "#e0e6eb",
    "border-opacity": 1,
  },
  colors: {
    primary: "#763EBD",
    secondary: "#95CFD5",
    info: "#539BFF",
    success: "#13DEB9",
    warning: "#FFAE1F",
    error: "#ff6692",
    lightprimary: "#F2ECF9",
    lightsecondary: "#EDF8FA",
    lightsuccess: "#E6FFFA",
    lighterror: "#FDEDE8",
    lightinfo: "#EBF3FE",
    lightwarning: "#FEF5E5",
    textPrimary: "#29343D",
    textSecondary: "#98A4AE",
    borderColor: "#e5eaef",
    containerBg: "#ffffff",
    background: "#F4F7FB",
    hoverColor: "#f6f9fc",
    surface: "#fff",
    grey100: "#F6F7F9",
    grey200: "#29343d",
    darkgray: "#0a2540",
    light: "#EFF4FA",
    muted: "#526b7a",
  },
};

const GREEN_THEME: ThemeTypes = {
  name: "GREEN_THEME",
  dark: false,
  variables: {
    "border-color": "#e0e6eb",
    "border-opacity": 1,
  },
  colors: {
    primary: "#0A7EA4",
    secondary: "#CCDA4E",
    info: "#539BFF",
    success: "#13DEB9",
    warning: "#FFAE1F",
    error: "#ff6692",
    lightprimary: "#F4F9FB",
    lightsecondary: "#FAFBEF",
    lightsuccess: "#E6FFFA",
    lighterror: "#FDEDE8",
    lightinfo: "#EBF3FE",
    lightwarning: "#FEF5E5",
    textPrimary: "#29343D",
    textSecondary: "#98A4AE",
    borderColor: "#e5eaef",
    containerBg: "#ffffff",
    background: "#F4F7FB",
    hoverColor: "#f6f9fc",
    surface: "#fff",
    grey100: "#F6F7F9",
    grey200: "#29343d",
    darkgray: "#0a2540",
    light: "#EFF4FA",
    muted: "#526b7a",
  },
};

const CYAN_THEME: ThemeTypes = {
  name: "CYAN_THEME",
  dark: false,
  variables: {
    "border-color": "#e0e6eb",
    "border-opacity": 1,
  },
  colors: {
    primary: "#01C0C8",
    secondary: "#FB9678",
    info: "#539BFF",
    success: "#13DEB9",
    warning: "#FFAE1F",
    error: "#ff6692",
    lightprimary: "#EBF9FA",
    lightsecondary: "#FFF5F2",
    lightsuccess: "#E6FFFA",
    lighterror: "#FDEDE8",
    lightinfo: "#EBF3FE",
    lightwarning: "#FEF5E5",
    textPrimary: "#29343D",
    textSecondary: "#98A4AE",
    borderColor: "#e5eaef",
    containerBg: "#ffffff",
    background: "#F4F7FB",
    hoverColor: "#f6f9fc",
    surface: "#fff",
    grey100: "#F6F7F9",
    grey200: "#29343d",
    darkgray: "#0a2540",
    light: "#EFF4FA",
    muted: "#526b7a",
  },
};

const ORANGE_THEME: ThemeTypes = {
  name: "ORANGE_THEME",
  dark: false,
  variables: {
    "border-color": "#e0e6eb",
    "border-opacity": 1,
  },
  colors: {
    primary: "#FA896B",
    secondary: "#0074BA",
    info: "#539BFF",
    success: "#13DEB9",
    warning: "#FFAE1F",
    error: "#ff6692",
    lightprimary: "#FBF2EF",
    lightsecondary: "#EFF9FF",
    lightsuccess: "#E6FFFA",
    lighterror: "#FDEDE8",
    lightinfo: "#EBF3FE",
    lightwarning: "#FEF5E5",
    textPrimary: "#29343D",
    textSecondary: "#98A4AE",
    borderColor: "#e5eaef",
    containerBg: "#ffffff",
    background: "#F4F7FB",
    hoverColor: "#f6f9fc",
    surface: "#fff",
    grey100: "#F6F7F9",
    grey200: "#29343d",
    darkgray: "#0a2540",
    light: "#EFF4FA",
    muted: "#526b7a",
  },
};

export {
  BLUE_THEME,
  AQUA_THEME,
  ORANGE_THEME,
  PURPLE_THEME,
  GREEN_THEME,
  CYAN_THEME,
};
