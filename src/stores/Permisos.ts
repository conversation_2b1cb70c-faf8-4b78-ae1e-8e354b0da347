import type { Permisos } from "@/utils/permisos";
import { useRoute } from "vue-router";
import type { OpcionAplicacion } from "@/utils/OpcionAplicacion";

const menuPermisos = {
  getPermisosOpcionMenu(): Permisos {
    const option = localStorage.getItem("opciones");
    const route = useRoute();
    const path = route.path;

    if (!option)
      return {
        ver: false,
        crear: false,
        editar: false,
        eliminar: false,
        imprimir: false,
      };

    const opciones = JSON.parse(option) as OpcionAplicacion[];

    const buscarOpcionPorRuta = (
      opciones: OpcionAplicacion[],
      ruta: string
    ): OpcionAplicacion[] => {
      let resultado: OpcionAplicacion[] = [];

      for (const opcion of opciones) {
        if (opcion.url === ruta) {
          resultado.push(opcion);
        }

        if (opcion.subOpciones && opcion.subOpciones.length > 0) {
          const subResultado = buscarOpcionPorRuta(opcion.subOpciones, ruta);
          resultado = resultado.concat(subResultado);
        }
      }

      return resultado;
    };

    const opcionesFiltradas = buscarOpcionPorRuta(opciones, path);

    if (opcionesFiltradas.length === 0) {
      return {
        ver: false,
        crear: false,
        editar: false,
        eliminar: false,
        imprimir: false,
      };
    }
    var permiso = opcionesFiltradas[0].permisos;
    return permiso;
  },
};

export default menuPermisos;
export { menuPermisos };
