// src/stores/apps/auth.ts
import { defineStore } from "pinia";
import { jwtDecode } from "jwt-decode";
import { router } from "@/router";
import { apiQuery } from "@/utils/helpers/apiQuery";
import * as Msal from "msal";
import type { auth } from "@/utils/Microsoft/auth";
import type { loginApi } from "@/utils/Microsoft/loginApi";
import { Configuration } from "@/configuration";
import { ref } from "vue";
import type { TokenResponseGoogle } from "@/utils/Google/TokenResponseGoogle";

const baseUrl = `api/security/login`;

interface DecodedToken {
  usuaNombre: string;
  rol: string;
  email: string;
  [key: string]: any;
}

// ✅ Verificación de expiración
function isTokenExpired(token: string): boolean {
  try {
    const { exp } = jwtDecode<{ exp: number }>(token);
    return exp * 1000 < Date.now();
  } catch {
    return true; // Si falla al decodificar, lo tratamos como expirado
  }
}

declare const grecaptcha: any;

const verificarCaptcha = async () => {
  const token = await grecaptcha.execute(Configuration.getGoogleKeyV3(), {
    action: "submit",
  });

  var googleToke = ref<TokenResponseGoogle>({ token });

  return googleToke.value.token;
};

export const useAuthStore = defineStore({
  id: "auth",
  state: () => {
    const token = localStorage.getItem("token");
    const valid = token && !isTokenExpired(token);

    const user = valid
      ? (JSON.parse(localStorage.getItem("user") || "null") as DecodedToken)
      : null;

    return {
      user,
      token: valid ? token : null,
      returnUrl: null as string | null,
    };
  },
  actions: {
    /// Loguearse conMicrosoft
    async loginWithMicrosoft(): Promise<void> {
      const config: auth | null = Configuration.getMicrosoft365Configuration();
      if (!config) {
        console.error("No se pudo obtener la configuración de Microsoft 365.");
        return;
      }

      const msalConfig: Msal.Configuration = {
        auth: {
          clientId: config.clientId,
          authority: `https://login.microsoftonline.com/${config.tenantId}`,
          redirectUri: config.redirectUri,
        },
        cache: {
          cacheLocation: "sessionStorage",
          storeAuthStateInCookie: false,
        },
      };

      const loginRequest: Msal.AuthenticationParameters = {
        scopes: ["openid", "profile", "User.Read", "Mail.Read"],
      };

      const tokenRequest: Msal.AuthenticationParameters = {
        scopes: ["Mail.Read"],
      };

      const myMSALObj = new Msal.UserAgentApplication(msalConfig);

      try {
        const loginResponse = await myMSALObj.loginPopup(loginRequest);
        const account = myMSALObj.getAccount();
        if (!account) {
          console.error("No se encontró la cuenta de usuario.");
          return;
        }
        // Obtener token si lo necesitas:
        const tokenResponse = await myMSALObj.acquireTokenSilent(tokenRequest);
        tokenResponse;

        await this.loginTokenMicrosoft(loginResponse.idToken.rawIdToken);
      } catch (error) {
        console.error("Login error:", error);
      }
    },

    async login(username: string, password: string) {
      try {
        let googleToken = await verificarCaptcha();

        // 1️⃣ Hacer login con las credenciales
        const response = await apiQuery.post(`${baseUrl}`, {
          username,
          password,
          googleToken,
        });

        // 2️⃣ Obtener el token desde la respuesta
        const token =
          typeof response === "string"
            ? response
            : response.token || response.value || null;

        // 3️⃣ Validar que sea un string
        if (!token || typeof token !== "string") {
          throw new Error(response.message || "🚫 Ha ocurrido un error");
        }

        this.tokengenerado(token);
      } catch (err: any) {
        console.error("❌ Error en login():", err.message);
        throw err;
      }
    },

    async tokengenerado(token: string) {
      // 4️⃣ Decodificar el token
      const decoded = jwtDecode<DecodedToken>(token);

      // 5️⃣ Guardar token y usuario en estado (Pinia) y en localStorage
      this.token = token;
      this.user = decoded;
      localStorage.setItem("token", token);
      localStorage.setItem("user", JSON.stringify(decoded));

      // 6️⃣ Obtener las opciones planas del backend
      const opcionesRaw = await apiQuery.get(`api/security/opcionaplicacion`);

      // 8️⃣ Guardar en localStorage
      localStorage.setItem("opciones", JSON.stringify(opcionesRaw));

      // 9️⃣ Redirigir al dashboard
      router.push(this.returnUrl || "/dashboards/dashboard1");
    },

    async loginTokenMicrosoft(toKen: string) {
      let modelMicrosoft = ref<loginApi>({
        toKen: toKen,
        mobile: false,
      });

      const response = await apiQuery.post(`${baseUrl}/microsoft`, {
        ...modelMicrosoft.value,
      });

      if (response) {
        if (response.token != null && response.token != undefined) {
          this.tokengenerado(response.token);
        }
      }
    },

    logout() {
      this.user = null;
      this.token = null;
      localStorage.removeItem("user");
      localStorage.removeItem("token");
      router.push("/");
    },
  },
});
