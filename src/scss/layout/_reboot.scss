.h-100 {
    height: 100%;
}
.w-100 {
    width: 100%;
}

.h-100vh {
    height: 100vh;
}

.gap-2 {
    gap: 8px;
}

.gap-3 {
    gap: 16px;
}

.gap-4 {
    gap: 24px;
}

.text-white {
    color: rgb(255, 255, 255) !important;
}

// border
.border-bottom {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.opacity-1 {
    opacity: 1 !important;
}

.opacity-20 {
    opacity: 0.2;
}
.opacity-30 {
    opacity: 0.3;
}
.opacity-50 {
    opacity: 0.5;
}
.opacity-80 {
    opacity: 0.8;
}

.z-auto.v-card {
    z-index: auto;
}

.obj-cover {
    object-fit: cover;
}
.cursor-move{
    cursor: move;
}
.z-index-2{
    z-index: 2;
}
h1,h2,h3,h4,h5,h6{
    font-weight: 600 !important;
}
.lh-30{
    line-height: 30px;
}
.lh-0{
    line-height: 0;
}

.heading{
    color:rgba(var(--v-theme-textPrimary));
}
body{
    font-size: 14px;
    color: rgb(var(--v-theme-textSecondary));
}
.hover-link-primary{
    &:hover{
        color:rgba(var(--v-theme-primary)) !important;
    }      
}

//Date time picker
input[type="date"],input[type="time"] { 
    display:block !important;
  }
  
  input[type="date"]::-webkit-calendar-picker-indicator, input[type="time"]::-webkit-calendar-picker-indicator  {
    display:block !important;
  }

  .ProseMirror{
    min-height: 150px;
  }

  .upload-btn-wrapper{
    width: 150px;
    height: 140px;
    margin: 0 auto;
    box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
    input[type=file] {
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
        height: 100%;
        width: 100%;
    } 
  }

  .bg-transparent{
    background-color: transparent !important;
  }

  @media screen and (max-width:1368px) and (min-width:1200px){
    .space-20{
        padding: 30px 20px !important;
    }
  }