//
//Full Calendar

.fc {
    .fc-button-group {
        >.fc-button {
            display: flex;
            align-items: center;
            padding: 6px 22px;

        }
    }

    .fc-daygrid-event {
        border-radius: 4px 20px 20px 4px;
        padding: 8px 10px;
        font-weight: 500;
        border-width: 0 0 0 4px;
        border-style: solid;
    }

    .fc-button {
        font-size: 1rem;
        font-weight: 500;
        text-transform: capitalize;

        .fc-icon {
            font-size: 1.5em;
            vertical-align: unset;
        }
    }

    .fc-button-primary {
        background: rgba(var(--v-theme-grey100));
        border-color: rgba(var(--v-theme-borderColor));
        color: rgb(var(--v-theme-textSecondary));

        &:focus {
            box-shadow: none;
        }

        &:hover {
            background-color: rgb(var(--v-theme-primary));
            border-color: rgb(var(--v-theme-primary));
        }

        &:not(:disabled).fc-button-active {
            background-color: rgb(var(--v-theme-primary));
            border-color: rgb(var(--v-theme-primary));

            &:focus {
                box-shadow: none;
            }
        }

        &:not(:disabled) {
            &:active {
                background-color: rgb(var(--v-theme-primary));
                border-color: rgb(var(--v-theme-primary));

                &:focus {
                    box-shadow: none;
                }
            }
        }

        &:disabled {
            background-color: rgb(var(--v-theme-primary));
            border-color: rgb(var(--v-theme-primary));
            opacity: 1;
        }
    }

    .fc-col-header-cell-cushion {
        display: inline-block;
        padding: 10px 5px;
        font-size: 14px;
    }
}

.fc-theme-standard {
    td {
        border: 1px solid rgba(var(--v-border-color), 1) !important;
    }

    th {
        border: 0 !important;
        border-width: 0;
        height: 56px;
        background-color: rgba(var(--v-theme-grey100));
        vertical-align: middle;
    }

    .fc-scrollgrid {
        border: 0 !important;
    }
}

.fc-h-event {
    background-color: rgb(var(--v-theme-primary));
    border: 1px solid rgb(var(--v-theme-primary));
    display: block;
}

.fc-direction-ltr {
    .fc-button-group {
        >.fc-button {
            &:not(:last-child) {
                border-bottom-left-radius: $border-radius-root;
                border-top-left-radius: $border-radius-root;
            }

            &:not(:first-child) {
                border-bottom-right-radius: $border-radius-root;
                border-top-right-radius: $border-radius-root;
                margin-left: -1px;
            }
        }
    }
}

.fc-button-group {
    .fc-dayGridMonth-button {
        border-bottom-right-radius: 0px !important;
        border-top-right-radius: 0px !important;
    }

    .fc-timeGridDay-button {
        border-bottom-left-radius: 0px !important;
        border-top-left-radius: 0px !important;
    }

    .fc-timeGridWeek-button {
        border-radius: 0 !important;
    }
}

.fc-today-button {
    border-radius: $border-radius-root !important;
}

@media screen and (max-width:600px) {
    .fc {
        .fc-toolbar {
            display: block;
            text-align: center;
        }
    }

    .fc-toolbar-chunk {
        .fc-toolbar-title {
            margin: 15px 0;
        }
    }
}

// Ecommerce
.product-page {
    .left-part {
        width: 270px;
    }
}

.product-details {
    .v-btn-group {
        height: 35px;
    }
}

.shop-sidebar {
    .v-checkbox .v-selection-control {
        min-height: 35px;
    }
}

#thumbnails {
    margin-top: 10px;

    .carousel__slide {
        border: 2px solid transparent;
        line-height: 0px;
        border-radius: $border-radius-root + 4px !important;
        overflow: hidden;
    }

    .carousel__slide--active {
        border: 2px solid rgb(var(--v-theme-primary));
    }

    .carousel__track {
        display: flex;
        gap: 14px
    }
}

//Chat
.chatlist {
    .v-list-item__prepend>.v-avatar~.v-list-item__spacer {
        width: 0;
    }

    .v-list-item__append {
        z-index: 1;
    }
}

.attachment {
    .download {
        opacity: 0;
    }

    &:hover {
        .download {
            opacity: 1;
        }
    }
}


.customTab {
    .v-btn {
        &.v-tab-item--selected {
            background-color: rgb(var(--v-theme-lightprimary)) !important;

            .icon {
                background-color: rgb(var(--v-theme-primary)) !important;
                color: #fff !important;
            }
        }
    }
}

.email-items {
    padding: 0px 24px 12px;

    &.selected-email {
        .email-title {
            color: rgb(var(--v-theme-primary)) !important;
        }
    }

    &:hover {
        background-color: rgb(var(--v-theme-light));

        .email-title {
            color: rgb(var(--v-theme-primary)) !important;
        }

    }
}

.email-content {
    p {
        margin: 10px 0;
    }
}