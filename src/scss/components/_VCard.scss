// Outline Card
.v-card--variant-outlined {
    border-color: rgba(var(--v-theme-borderColor)) !important;
    background-color:rgb(var(--v-theme-surface)) !important; 
}

.v-card--variant-elevated,
.v-card--variant-flat {
    color: rgb(var(--v-theme-textPrimary));
}

.card-hover {
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;

    &:hover {
        img {
            transform: scale(1.09);
            transition: transform .2s;
        }
    }
}

.v-card {
    width: 100%;
    overflow: visible;

    .color-inherits {
        color: inherit;
    }

    .feature-card {
        .v-responsive__content {
            height: 100%;
        }
    }

    .v-card-title {
        line-height: normal !important;
        margin-bottom: 4px;
    }
    .v-card-subtitle{
        color: rgb(var(--v-theme-textSecondary));
    }
    .v-card-text{
        font-size: 14px;
    }
    .v-card-text{
        padding: 30px 30px;
    }
    .v-card-item{
        padding: 30px 30px;
    }

}

// Theme cards
.cardBordered {
    .v-card {
        box-shadow: none !important;
        border: 1px solid rgb(var(--v-theme-borderColor));
    }
}