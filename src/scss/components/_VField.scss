.v-field--variant-outlined .v-field__outline__start.v-locale--is-ltr,
.v-locale--is-ltr .v-field--variant-outlined .v-field__outline__start {
    border-radius: $border-radius-root 0 0 $border-radius-root;
}

.v-field--variant-outlined .v-field__outline__end.v-locale--is-ltr,
.v-locale--is-ltr .v-field--variant-outlined .v-field__outline__end {
    border-radius: 0 $border-radius-root $border-radius-root 0;
}

.v-field {
    font-size: 14px !important;
    color:  rgba(var(--v-theme-textPrimary));
}

// select outlined
.v-field--variant-outlined .v-field__outline__start,
.v-field--variant-outlined .v-field__outline__notch::before,
.v-field--variant-outlined .v-field__outline__notch::after,
.v-field--variant-outlined .v-field__outline__end {
    opacity: 1;
}


.v-label{
    color: rgba(var(--v-theme-textPrimary)) !important;
}

.v-field.v-field--active .v-label.v-field-label--floating{
    color: rgba(var(--v-theme-textPrimary),0.6) !important;
}