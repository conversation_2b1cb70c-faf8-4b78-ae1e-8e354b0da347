.v-table .v-table__wrapper>table>tbody>tr:not(:last-child)>td,
.v-table .v-table__wrapper>table>tbody>tr:not(:last-child)>th,
.v-table .v-table__wrapper>table>thead>tr:last-child>th {
    border-bottom: thin solid rgba(var(--v-theme-borderColor)) !important;
}

.v-table {
    &.dark-table {
        background-color: rgb(var(--v-theme-grey200));
    }
}

.v-table {
    .v-table__wrapper {
        table {
            tbody {
                tr {

                    td {
                        padding: 14px 24px !important;

                        &:first-child {
                            padding-left: 0;
                        }
                    }
                }
            }

            thead {
                tr {
                    th {
                        padding: 14px 24px !important;

                        &:first-child {
                            padding-left: 0;
                        }
                    }
                }
            }
        }

    }
}

.v-table {


    &.ticket-table {

        table {
            thead {
                th {
                    font-weight: 600 !important;
                }
            }

            tbody {
                tr {

                    td {
                        padding: 20px 24px !important;
                    }
                }
            }
        }

    }

    &.invoice-table {
        .v-table__wrapper {
            table {
                thead {
                    th {
                        font-weight: 600 !important;
                        padding: 0px 24px !important;
                        &:first-child {
                            padding-left: 0 !important;
                        }
                        &:last-child {
                            padding-right: 0 !important;
                        }
                        
                    }
                }

                tbody {
                    tr {

                        td {
                            padding: 8px 24px !important;
                            &:first-child {
                                padding-left: 0 !important;
                            }
                            &:last-child {
                                padding-right: 0 !important;
                            }
                        }
                    }
                }
            }
        }


    }


    &.revenue-table {
        .v-table__wrapper {
            table {
                tbody {
                    tr {

                        td {
                            padding: 14px 24px !important;

                            &:first-child {
                                padding-left: 0 !important;
                            }
                        }
                    }
                }

                thead {
                    tr {
                        th {
                            padding: 14px 24px !important;

                            &:first-child {
                                padding-left: 0 !important;
                            }
                        }
                    }
                }
            }

        }
    }

    &.light-border {
        .v-table__wrapper>table>tbody>tr:not(:last-child)>td {
            border-bottom: thin solid rgba(var(--v-theme-borderColor), 0.3) !important;
        }

        .v-table__wrapper>table>tbody>tr:not(:last-child)>th,
        .v-table__wrapper>table>thead>tr:last-child>th {
            border-bottom: thin solid rgba(var(--v-theme-borderColor), 0.3) !important;
        }
    }
}

.v-data-table {
    th.v-data-table__th {
        font-size: 16px;
        color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));
    }

    td.v-data-table__td {
        font-size: 14px;
        text-wrap: nowrap;
    }

    .v-data-table-footer {
        padding: 15px 8px;
    }

    .v-data-table-header__sort-badge {
        background-color: rgb(var(--v-theme-borderColor)) !important;
    }

    .tdhead {
        font-size: 16px;
    }
}

@media screen and (max-width:767px) {
    .v-data-table-footer {
        justify-content: center;
    }
}


.datatabels {
    overflow: hidden;

    table {
        tbody {
            tr {

                td {
                    padding: 14px 24px;

                    &:first-child {
                        padding-left: 24px !important;
                    }
                }
            }
        }

        thead {
            tr {
                th {
                    padding: 14px 24px;

                    &:first-child {
                        padding-left: 24px !important;
                    }
                }
            }
        }
    }
}



// Datatable Pagination 
.v-pagination {
    .v-pagination__list {
        .v-pagination__item--is-active {
            .v-btn {
                .v-btn__overlay {
                    opacity: 0;
                }

                background-color: rgb(var(--v-theme-light)) !important;
            }
        }
    }
}