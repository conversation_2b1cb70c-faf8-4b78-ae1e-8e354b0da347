import { createApp } from "vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import { router } from "./router";
import vuetify from "./plugins/vuetify";
import "@/scss/style.scss";
import { PerfectScrollbar } from 'vue3-perfect-scrollbar';
import VueApexCharts from "vue3-apexcharts";
import VueTablerIcons from "vue-tabler-icons";
import { Icon } from "@iconify/vue";
import "vue3-carousel/dist/carousel.css";
import { Configuration } from "./configuration";
import veeValidate from "./plugins/vee-validate";

//Mock Api data
import "./_mockApis";

import Maska from "maska";


//i18
import { createI18n } from "vue-i18n";
import messages from "@/utils/locales/messages";

//ScrollTop
import VueScrollTo from "vue-scrollto";

//LightBox
import VueEasyLightbox from "vue-easy-lightbox";

const i18n = createI18n({
  locale: "es",
  messages: messages,
  silentTranslationWarn: true,
  silentFallbackWarn: true,
});

veeValidate(); // Initialize vee-validate

/// Importo el capcha de google
// Cargar reCAPTCHA v3 dinámicamente
const loadRecaptcha = () => {
  const script = document.createElement("script");
  script.src = `https://www.google.com/recaptcha/api.js?render=${Configuration.getGoogleKeyV3()}`;
  script.async = true;
  script.defer = true;
  document.head.appendChild(script);
};

// Llamar antes de crear la app
loadRecaptcha();

const app = createApp(App);
const pinia = createPinia();
app.config.devtools = true;
app.component("Icon", Icon);

app.use(pinia); // Make sure to call this before router
app.use(router);
app.use(PerfectScrollbar);

app.use(VueTablerIcons);
app.use(i18n);
app.use(Maska);
app.use(VueApexCharts);
app.use(vuetify).mount("#app");
//Lightbox
app.use(VueEasyLightbox);
//ScrollTop Use
app.use(VueScrollTo, {
  duration: 1000,
  easing: "ease",
});
